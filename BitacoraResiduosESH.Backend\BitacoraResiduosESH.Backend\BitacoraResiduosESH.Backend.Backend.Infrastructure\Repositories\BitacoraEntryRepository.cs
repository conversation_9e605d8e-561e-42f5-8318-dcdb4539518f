﻿namespace BitacoraResiduosESH.Backend.Backend.Infrastructure.Repositories;

public class BitacoraEntryRepository(AppDbContext context)
    : GenericRepository<BitacoraEntry>(context), IBitacoraEntryRepository
{
    private static readonly ILogger Logger = LogManager.GetCurrentClassLogger();

    public new async Task<BitacoraEntry?> GetByIdAsync(int id, bool includeDeleted = false)
    {
        Logger.Debug("Obteniendo BitacoraEntry por ID: {Id}, IncludeDeleted: {IncludeDeleted}", id, includeDeleted);

        var query = DbSet.AsQueryable()
            .Include(e => e.WasteType)
            .Include(e => e.ContainerType)
            .Include(e => e.Area);

        var result = await query.FirstOrDefaultAsync(e => e.Id == id);

        Logger.Debug("BitacoraEntry obtenida por ID {Id}: {Found}", id, result != null);

        return result;
    }

    public new async Task<PagedResponse<BitacoraEntry>> GetAllAsync(PaginationFilter filter)
    {
        Logger.Debug("Obteniendo todas las BitacoraEntry paginadas - Página: {PageNumber}, Tamaño: {PageSize}, IncludeDeleted: {IncludeDeleted}",
            filter.PageNumber, filter.PageSize, filter.IncludeDeleted);

        var query = DbSet.AsQueryable()
            .Include(e => e.WasteType)
            .Include(e => e.ContainerType)
            .Include(e => e.Area)
            .Where(e => e.IsDeleted == filter.IncludeDeleted);

        var totalRecords = await query.CountAsync();

        var data = await query
            .OrderBy(e => e.Id)
            .Skip((filter.PageNumber - 1) * filter.PageSize)
            .Take(filter.PageSize)
            .ToListAsync();

        var result = new PagedResponse<BitacoraEntry>
        {
            PageNumber = filter.PageNumber,
            PageSize = filter.PageSize,
            TotalRecords = totalRecords,
            Data = data
        };

        Logger.Debug("Todas las BitacoraEntry obtenidas paginadas: {Count} elementos de {TotalRecords} totales",
            result.Data.Count, result.TotalRecords);

        return result;
    }

    public new async Task<PagedResponse<BitacoraEntry>> GetActiveAsync(PaginationFilter filter)
    {
        Logger.Debug("Obteniendo BitacoraEntry activas paginadas - Página: {PageNumber}, Tamaño: {PageSize}",
            filter.PageNumber, filter.PageSize);

        var query = DbSet.AsQueryable()
            .Include(e => e.WasteType)
            .Include(e => e.ContainerType)
            .Include(e => e.Area)
            .Where(e => e.Active && !e.IsDeleted);

        var totalRecords = await query.CountAsync();

        var data = await query
            .OrderBy(e => e.Id)
            .Skip((filter.PageNumber - 1) * filter.PageSize)
            .Take(filter.PageSize)
            .ToListAsync();

        var result = new PagedResponse<BitacoraEntry>
        {
            PageNumber = filter.PageNumber,
            PageSize = filter.PageSize,
            TotalRecords = totalRecords,
            Data = data
        };

        Logger.Debug("BitacoraEntry activas obtenidas paginadas: {Count} elementos de {TotalRecords} totales",
            result.Data.Count, result.TotalRecords);

        return result;
    }

    public new async Task<PagedResponse<BitacoraEntry>> GetByNameAsync(string name, PaginationFilter filter, bool includeDeleted = false)
    {
        Logger.Debug("Buscando BitacoraEntry por nombre paginadas - Nombre: {Name}, Página: {PageNumber}, Tamaño: {PageSize}, IncludeDeleted: {IncludeDeleted}",
            name, filter.PageNumber, filter.PageSize, includeDeleted);

        var query = DbSet.AsQueryable()
            .Include(e => e.WasteType)
            .Include(e => e.ContainerType)
            .Include(e => e.Area)
            .Where(e => e.IsDeleted == filter.IncludeDeleted);

        // Apply name filter
        query = query.Where(e => e.Name.ToLower().Contains(name.ToLower()));

        var totalRecords = await query.CountAsync();

        var data = await query
            .OrderBy(e => e.Id)
            .Skip((filter.PageNumber - 1) * filter.PageSize)
            .Take(filter.PageSize)
            .ToListAsync();

        var result = new PagedResponse<BitacoraEntry>
        {
            PageNumber = filter.PageNumber,
            PageSize = filter.PageSize,
            TotalRecords = totalRecords,
            Data = data
        };

        Logger.Debug("BitacoraEntry por nombre obtenidas paginadas: {Count} elementos de {TotalRecords} totales",
            result.Data.Count, result.TotalRecords);

        return result;
    }

    public new async Task<PagedResponse<BitacoraEntry>> FuzzySearchAsync(string searchTerm, PaginationFilter filter, bool includeDeleted = false)
    {
        Logger.Debug("Búsqueda fuzzy en BitacoraEntry - Término: {SearchTerm}, Página: {PageNumber}, Tamaño: {PageSize}, IncludeDeleted: {IncludeDeleted}",
            searchTerm, filter.PageNumber, filter.PageSize, includeDeleted);

        var query = DbSet.AsQueryable()
            .Include(e => e.WasteType)
            .Include(e => e.ContainerType)
            .Include(e => e.Area)
            .Where(e => e.IsDeleted == filter.IncludeDeleted);

        // Fuzzy search: search in name, description, and related entities
        var searchTermLower = searchTerm.ToLower();
        query = query.Where(e =>
            e.Name.ToLower().Contains(searchTermLower) ||
            (e.Description != null && e.Description.ToLower().Contains(searchTermLower)) ||
            e.WasteType.Name.ToLower().Contains(searchTermLower) ||
            e.ContainerType.Name.ToLower().Contains(searchTermLower) ||
            e.Area.Name.ToLower().Contains(searchTermLower) ||
            e.EnteredBy.ToLower().Contains(searchTermLower)
        );

        var totalRecords = await query.CountAsync();

        // Order by relevance: exact matches first, then contains in name, then other fields
        var data = await query
            .OrderBy(e => e.Name.ToLower() == searchTermLower ? 0 : 1)
            .ThenBy(e => e.Name.ToLower().StartsWith(searchTermLower) ? 0 : 1)
            .ThenBy(e => e.Name.ToLower().Contains(searchTermLower) ? 0 : 1)
            .ThenBy(e => e.Name)
            .Skip((filter.PageNumber - 1) * filter.PageSize)
            .Take(filter.PageSize)
            .ToListAsync();

        var result = new PagedResponse<BitacoraEntry>
        {
            PageNumber = filter.PageNumber,
            PageSize = filter.PageSize,
            TotalRecords = totalRecords,
            Data = data
        };

        Logger.Debug("Búsqueda fuzzy en BitacoraEntry completada: {Count} elementos de {TotalRecords} totales",
            result.Data.Count, result.TotalRecords);

        return result;
    }
}
