namespace BitacoraResiduosESH.Backend.Backend.Application.Services;

public class BitacoraEntryService(IBitacoraEntryRepository repository)
    : SimpleEntityService<BitacoraEntry, BitacoraEntryDto, CreateBitacoraEntryDto, UpdateBitacoraEntryDto, BitacoraEntryFilterDto>(repository), IBitacoraEntryService
{
    private static readonly ILogger Logger = LogManager.GetCurrentClassLogger();

    protected override BitacoraEntryDto MapToDto(BitacoraEntry entity)
    {
        var dto = base.MapToDto(entity);

        // Map additional properties
        dto.WasteTypeId = entity.WasteTypeId;
        dto.GrossWeight = entity.GrossWeight;
        dto.Tare = entity.Tare;
        dto.NetWeightLB = entity.NetWeightLB;
        dto.NetWeightKG = entity.NetWeightKG;
        dto.UnitPrice = entity.UnitPrice;
        dto.ContainerTypeId = entity.ContainerTypeId;
        dto.AreaId = entity.AreaId;
        dto.EntryDate = entity.EntryDate;
        dto.EntryDateString = entity.EntryDate.ToString(DateFormats.LONG_DATE_TIME);
        dto.DepartureDate = entity.DepartureDate;
        dto.DepartureDateString = entity.DepartureDate?.ToString(DateFormats.LONG_DATE_TIME);
        dto.EnteredBy = entity.EnteredBy;

        // Map navigation properties if loaded
        if (entity.WasteType != null)
        {
            dto.WasteType = new WasteTypeDto
            {
                Id = entity.WasteType.Id,
                Name = entity.WasteType.Name,
                Description = entity.WasteType.Description,
                Active = entity.WasteType.Active,
                Created = entity.WasteType.Created,
                CreatedString = entity.WasteType.Created.ToString(DateFormats.LONG_DATE_TIME),
                CreatedBy = entity.WasteType.CreatedBy,
                Updated = entity.WasteType.Updated,
                UpdatedString = entity.WasteType.Updated?.ToString(DateFormats.LONG_DATE_TIME),
                UpdatedBy = entity.WasteType.UpdatedBy,
                IsDeleted = entity.WasteType.IsDeleted,
                Deleted = entity.WasteType.Deleted,
                DeletedString = entity.WasteType.Deleted?.ToString(DateFormats.LONG_DATE_TIME),
                DeletedBy = entity.WasteType.DeletedBy
            };
        }

        if (entity.ContainerType != null)
        {
            dto.ContainerType = new ContainerTypeDto
            {
                Id = entity.ContainerType.Id,
                Name = entity.ContainerType.Name,
                Description = entity.ContainerType.Description,
                Active = entity.ContainerType.Active,
                Created = entity.ContainerType.Created,
                CreatedString = entity.ContainerType.Created.ToString(DateFormats.LONG_DATE_TIME),
                CreatedBy = entity.ContainerType.CreatedBy,
                Updated = entity.ContainerType.Updated,
                UpdatedString = entity.ContainerType.Updated?.ToString(DateFormats.LONG_DATE_TIME),
                UpdatedBy = entity.ContainerType.UpdatedBy,
                IsDeleted = entity.ContainerType.IsDeleted,
                Deleted = entity.ContainerType.Deleted,
                DeletedString = entity.ContainerType.Deleted?.ToString(DateFormats.LONG_DATE_TIME),
                DeletedBy = entity.ContainerType.DeletedBy
            };
        }

        if (entity.Area != null)
        {
            dto.Area = new AreaDto
            {
                Id = entity.Area.Id,
                Name = entity.Area.Name,
                Description = entity.Area.Description,
                Active = entity.Area.Active,
                Created = entity.Area.Created,
                CreatedString = entity.Area.Created.ToString(DateFormats.LONG_DATE_TIME),
                CreatedBy = entity.Area.CreatedBy,
                Updated = entity.Area.Updated,
                UpdatedString = entity.Area.Updated?.ToString(DateFormats.LONG_DATE_TIME),
                UpdatedBy = entity.Area.UpdatedBy,
                IsDeleted = entity.Area.IsDeleted,
                Deleted = entity.Area.Deleted,
                DeletedString = entity.Area.Deleted?.ToString(DateFormats.LONG_DATE_TIME),
                DeletedBy = entity.Area.DeletedBy
            };
        }

        return dto;
    }

    public new async Task<BitacoraEntryDto> CreateAsync(CreateBitacoraEntryDto dto, string createdBy)
    {
        Logger.Debug("Creando nueva BitacoraEntry: {@Dto}, CreatedBy: {User}", dto, createdBy);

        // Validar que el nombre no exista
        if (await ExistsByNameAsync(dto.Name))
        {
            Logger.Warn("Intento de crear BitacoraEntry con nombre duplicado: {Name}", dto.Name);
            throw new InvalidOperationException($"Ya existe un elemento con el nombre '{dto.Name}'");
        }

        var entity = new BitacoraEntry
        {
            Name = dto.Name,
            Description = dto.Description,
            WasteTypeId = dto.WasteTypeId,
            GrossWeight = dto.GrossWeight,
            Tare = dto.Tare,
            NetWeightLB = dto.NetWeightLB,
            NetWeightKG = dto.NetWeightKG,
            UnitPrice = dto.UnitPrice,
            ContainerTypeId = dto.ContainerTypeId,
            AreaId = dto.AreaId,
            EntryDate = dto.EntryDate,
            DepartureDate = dto.DepartureDate,
            EnteredBy = dto.EnteredBy,
            CreatedBy = createdBy
        };

        var createdEntity = await repository.AddAsync(entity);
        var result = MapToDto(createdEntity);

        Logger.Info("BitacoraEntry creada exitosamente con ID: {Id} por usuario: {User}", result.Id, createdBy);

        return result;
    }

    public new async Task<BitacoraEntryDto> UpdateAsync(int id, UpdateBitacoraEntryDto dto, string updatedBy)
    {
        Logger.Debug("Actualizando BitacoraEntry ID: {Id}, DTO: {@Dto}, UpdatedBy: {User}", id, dto, updatedBy);

        var entity = await repository.GetByIdAsync(id);
        if (entity == null)
        {
            Logger.Warn("Intento de actualizar BitacoraEntry inexistente con ID: {Id}", id);
            throw new InvalidOperationException($"No se encontró el elemento con ID {id}");
        }

        // Validar que el nombre no exista en otro elemento
        if (await ExistsByNameAsync(dto.Name, id))
        {
            Logger.Warn("Intento de actualizar BitacoraEntry con nombre duplicado: {Name}, ID: {Id}", dto.Name, id);
            throw new InvalidOperationException($"Ya existe otro elemento con el nombre '{dto.Name}'");
        }

        // Update all properties
        entity.Name = dto.Name;
        entity.Description = dto.Description;
        entity.WasteTypeId = dto.WasteTypeId;
        entity.GrossWeight = dto.GrossWeight;
        entity.Tare = dto.Tare;
        entity.NetWeightLB = dto.NetWeightLB;
        entity.NetWeightKG = dto.NetWeightKG;
        entity.UnitPrice = dto.UnitPrice;
        entity.ContainerTypeId = dto.ContainerTypeId;
        entity.AreaId = dto.AreaId;
        entity.EntryDate = dto.EntryDate;
        entity.DepartureDate = dto.DepartureDate;
        entity.EnteredBy = dto.EnteredBy;

        var updatedEntity = await repository.UpdateAsync(entity);
        var result = MapToDto(updatedEntity);

        Logger.Info("BitacoraEntry actualizada exitosamente con ID: {Id} por usuario: {User}", result.Id, updatedBy);

        return result;
    }
}
