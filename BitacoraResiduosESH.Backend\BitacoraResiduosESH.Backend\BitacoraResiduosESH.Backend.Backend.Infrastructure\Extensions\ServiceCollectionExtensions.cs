using LogLevel = Microsoft.Extensions.Logging.LogLevel;

namespace BitacoraResiduosESH.Backend.Backend.Infrastructure.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddRepositories(this IServiceCollection services)
    {
        services.AddScoped<IRoleRepository, RoleRepository>();
        services.AddScoped<IUserRepository, UserRepository>();
        services.AddScoped<IAreaRepository, AreaRepository>();
        services.AddScoped<IContainerTypeRepository, ContainerTypeRepository>();
        services.AddScoped<IWasteTypeRepository, WasteTypeRepository>();
        services.AddScoped<IBitacoraEntryRepository, BitacoraEntryRepository>();

        return services;
    }

    public static IServiceCollection AddApplicationServices(this IServiceCollection services)
    {
        // Registrar servicios de aplicación
        services.AddScoped<IRoleService, RoleService>();
        services.AddScoped<IUserService, UserService>();
        services.AddScoped<IAreaService, AreaService>();
        services.AddScoped<IContainerTypeService, ContainerTypeService>();
        services.AddScoped<IWasteTypeService, WasteTypeService>();
        services.AddScoped<IBitacoraEntryService, BitacoraEntryService>();

        return services;
    }

    public static IServiceCollection AddInfrastructureServices(this IServiceCollection services)
    {
        // Registrar servicios de infraestructura
        services.AddScoped<IOpenIdConnectService, OpenIdConnectService>();
        services.AddScoped<IPasswordService, PasswordService>();
        services.AddScoped<IJwtService, JwtService>();
        services.AddScoped<IAuthService, AuthService>();

        // Configurar HttpClient para OpenID Connect
        services.AddHttpClient<OpenIdConnectService>(client =>
        {
            client.Timeout = TimeSpan.FromSeconds(30);
            client.DefaultRequestHeaders.Add("User-Agent", "ToolsMS.Backend/1.0");
        });

        // Aquí puedes agregar más servicios de infraestructura según los necesites
        // services.AddScoped<IEmailService, EmailService>();
        // services.AddScoped<IFileStorageService, FileStorageService>();
        // services.AddScoped<ICacheService, CacheService>();
        // etc.

        return services;
    }

    public static IServiceCollection AddDatabase(this IServiceCollection services, IConfiguration configuration)
    {
        // Obtener la cadena de conexión
        var connectionString = configuration.GetConnectionString("DefaultConnection");

        if (string.IsNullOrEmpty(connectionString))
            // Fallback a una cadena de conexión por defecto
            connectionString = "Data Source=app.db";

        // Configurar Entity Framework con SQLite
        services.AddDbContext<AppDbContext>(options =>
        {
            options.UseSqlite(connectionString, sqliteOptions =>
            {
                // Configuraciones específicas de SQLite
                sqliteOptions.MigrationsAssembly(typeof(AppDbContext).Assembly.GetName().Name);
            });

            // Configuraciones adicionales para desarrollo
        });

        return services;
    }

    public static IServiceCollection AddNLog(this IServiceCollection services, IConfiguration configuration)
    {
        // Configurar NLog
        LogManager.Setup().LoadConfigurationFromFile("nlog.config");

        // Configurar el logging de ASP.NET Core para usar NLog
        services.AddLogging(loggingBuilder =>
        {
            loggingBuilder.ClearProviders();
            loggingBuilder.SetMinimumLevel(LogLevel.Trace);
            loggingBuilder.AddNLog();
        });

        return services;
    }

    /// <summary>
    /// Inicializa la base de datos con datos de prueba
    /// </summary>
    public static async Task InitializeDatabaseAsync(this IServiceProvider serviceProvider)
    {
        using var scope = serviceProvider.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
        var logger = scope.ServiceProvider.GetRequiredService<ILogger<AppDbContext>>();
        var passwordService = scope.ServiceProvider.GetRequiredService<IPasswordService>();

        try
        {
            logger.LogInformation("Inicializando base de datos con datos de prueba...");

            // Verificar si ya existen datos
            if (await context.Roles.AnyAsync())
            {
                logger.LogInformation("La base de datos ya contiene datos. Saltando inicialización.");
                return;
            }

            // Crear rol Admin
            var adminRole = new Role
            {
                Name = "Admin",
                Description = "Administrador del sistema con acceso completo",
                CreatedBy = "system",
                Active = true
            };

            context.Roles.Add(adminRole);
            await context.SaveChangesAsync(true, "system");

            logger.LogInformation("Rol Admin creado exitosamente");

            // Crear usuario administrador
            var adminUser = new User
            {
                Name = "Maximiliano Ponce",
                EmployeeNumber = "123456",
                Email = "<EMAIL>",
                Password = passwordService.HashPassword("123456"),
                RoleId = adminRole.Id,
                CreatedBy = "system",
                Active = true
            };

            context.User.Add(adminUser);
            await context.SaveChangesAsync(true, "system");

            logger.LogInformation("Usuario administrador creado exitosamente");
            logger.LogInformation("Inicialización de base de datos completada exitosamente");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error al inicializar la base de datos con datos de prueba");
            throw;
        }
    }
}