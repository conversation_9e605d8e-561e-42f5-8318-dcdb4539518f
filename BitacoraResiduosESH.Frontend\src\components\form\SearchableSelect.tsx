import React, { useState, useEffect, useRef } from 'react';
import { ChevronDown, Search, X } from 'lucide-react';

export interface SearchableSelectOption {
    id: number;
    name: string;
    description?: string;
}

interface SearchableSelectProps {
    options: SearchableSelectOption[];
    value?: number;
    onChange: (value: number | undefined) => void;
    onSearch: (searchTerm: string) => void;
    placeholder?: string;
    label?: string;
    required?: boolean;
    isLoading?: boolean;
    error?: string;
    disabled?: boolean;
    className?: string;
}

export const SearchableSelect: React.FC<SearchableSelectProps> = ({
    options,
    value,
    onChange,
    onSearch,
    placeholder = "Seleccionar...",
    label,
    required = false,
    isLoading = false,
    error,
    disabled = false,
    className = ""
}) => {
    const [isOpen, setIsOpen] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [highlightedIndex, setHighlightedIndex] = useState(-1);
    const dropdownRef = useRef<HTMLDivElement>(null);
    const searchInputRef = useRef<HTMLInputElement>(null);

    const selectedOption = options.find(option => option.id === value);

    // Close dropdown when clicking outside
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setIsOpen(false);
                setSearchTerm('');
                setHighlightedIndex(-1);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    // Handle search with debounce
    useEffect(() => {
        const timeoutId = setTimeout(() => {
            if (searchTerm.trim()) {
                onSearch(searchTerm.trim());
            }
        }, 300);

        return () => clearTimeout(timeoutId);
    }, [searchTerm, onSearch]);

    // Focus search input when dropdown opens
    useEffect(() => {
        if (isOpen && searchInputRef.current) {
            searchInputRef.current.focus();
        }
    }, [isOpen]);

    // Handle keyboard navigation
    const handleKeyDown = (e: React.KeyboardEvent) => {
        if (!isOpen) {
            if (e.key === 'Enter' || e.key === ' ' || e.key === 'ArrowDown') {
                e.preventDefault();
                setIsOpen(true);
            }
            return;
        }

        switch (e.key) {
            case 'Escape':
                setIsOpen(false);
                setSearchTerm('');
                setHighlightedIndex(-1);
                break;
            case 'ArrowDown':
                e.preventDefault();
                setHighlightedIndex(prev => 
                    prev < options.length - 1 ? prev + 1 : 0
                );
                break;
            case 'ArrowUp':
                e.preventDefault();
                setHighlightedIndex(prev => 
                    prev > 0 ? prev - 1 : options.length - 1
                );
                break;
            case 'Enter':
                e.preventDefault();
                if (highlightedIndex >= 0 && highlightedIndex < options.length) {
                    handleSelect(options[highlightedIndex]);
                }
                break;
        }
    };

    const handleSelect = (option: SearchableSelectOption) => {
        onChange(option.id);
        setIsOpen(false);
        setSearchTerm('');
        setHighlightedIndex(-1);
    };

    const handleClear = (e: React.MouseEvent) => {
        e.stopPropagation();
        onChange(undefined);
    };

    const toggleDropdown = () => {
        if (!disabled) {
            setIsOpen(!isOpen);
            if (!isOpen) {
                setSearchTerm('');
                setHighlightedIndex(-1);
            }
        }
    };

    return (
        <div className={`relative ${className}`} ref={dropdownRef}>
            {label && (
                <label className="block text-sm font-medium mb-1">
                    {label} {required && <span className="text-red-500">*</span>}
                </label>
            )}
            
            <div
                className={`
                    relative w-full px-3 py-2 border rounded-md cursor-pointer
                    transition-colors duration-200
                    ${disabled 
                        ? 'bg-gray-100 cursor-not-allowed' 
                        : 'bg-white hover:border-gray-400'
                    }
                    ${error 
                        ? 'border-red-500 focus-within:ring-2 focus-within:ring-red-500' 
                        : 'border-gray-300 focus-within:ring-2 focus-within:ring-blue-500'
                    }
                    ${isOpen ? 'ring-2 ring-blue-500' : ''}
                `}
                onClick={toggleDropdown}
                onKeyDown={handleKeyDown}
                tabIndex={disabled ? -1 : 0}
            >
                <div className="flex items-center justify-between">
                    <span className={`block truncate ${
                        selectedOption ? 'text-gray-900' : 'text-gray-500'
                    }`}>
                        {selectedOption ? selectedOption.name : placeholder}
                    </span>
                    
                    <div className="flex items-center space-x-1">
                        {selectedOption && !disabled && (
                            <button
                                type="button"
                                onClick={handleClear}
                                className="p-1 hover:bg-gray-200 rounded"
                            >
                                <X className="w-4 h-4 text-gray-400" />
                            </button>
                        )}
                        <ChevronDown 
                            className={`w-4 h-4 text-gray-400 transition-transform duration-200 ${
                                isOpen ? 'transform rotate-180' : ''
                            }`} 
                        />
                    </div>
                </div>
            </div>

            {/* Dropdown */}
            {isOpen && (
                <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-hidden">
                    {/* Search input */}
                    <div className="p-2 border-b border-gray-200">
                        <div className="relative">
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                            <input
                                ref={searchInputRef}
                                type="text"
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                placeholder="Buscar..."
                                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                        </div>
                    </div>

                    {/* Options list */}
                    <div className="max-h-48 overflow-y-auto">
                        {isLoading ? (
                            <div className="p-3 text-center text-gray-500">
                                Cargando...
                            </div>
                        ) : options.length === 0 ? (
                            <div className="p-3 text-center text-gray-500">
                                {searchTerm ? 'No se encontraron resultados' : 'No hay opciones disponibles'}
                            </div>
                        ) : (
                            options.map((option, index) => (
                                <div
                                    key={option.id}
                                    className={`
                                        px-3 py-2 cursor-pointer transition-colors duration-150
                                        ${index === highlightedIndex 
                                            ? 'bg-blue-100 text-blue-900' 
                                            : 'hover:bg-gray-100'
                                        }
                                        ${option.id === value ? 'bg-blue-50 text-blue-700' : ''}
                                    `}
                                    onClick={() => handleSelect(option)}
                                    onMouseEnter={() => setHighlightedIndex(index)}
                                >
                                    <div className="font-medium">{option.name}</div>
                                    {option.description && (
                                        <div className="text-sm text-gray-500 truncate">
                                            {option.description}
                                        </div>
                                    )}
                                </div>
                            ))
                        )}
                    </div>
                </div>
            )}

            {error && (
                <p className="mt-1 text-sm text-red-600">{error}</p>
            )}
        </div>
    );
};
