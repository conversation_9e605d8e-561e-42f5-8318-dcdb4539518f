namespace BitacoraResiduosESH.Backend.Backend.Application.Services;

public class SimpleEntityService<TEntity, TDto, TCreateDto, TUpdateDto, TFilterDto>
    : ISimpleEntityService<TEntity, TDto, TCreateDto, TUpdateDto, TFilterDto>
    where TEntity : SimpleEntity, new()
    where TDto : SimpleEntityDto, new()
    where TCreateDto : CreateSimpleEntityDto
    where TUpdateDto : UpdateSimpleEntityDto
    where TFilterDto : SimpleEntityFilterDto
{
    private static readonly ILogger Logger = LogManager.GetCurrentClassLogger();
    private readonly ISimpleEntityRepository<TEntity> _repository;

    public SimpleEntityService(ISimpleEntityRepository<TEntity> repository)
    {
        _repository = repository;
    }

    #region Métodos privados

    protected virtual TDto MapToDto(TEntity entity)
    {
        return new TDto
        {
            Id = entity.Id,
            Name = entity.Name,
            Description = entity.Description,
            Created = entity.Created,
            CreatedString = entity.Created.ToString(DateFormats.LONG_DATE_TIME),
            CreatedBy = entity.CreatedBy,
            Updated = entity.Updated,
            UpdatedString = entity.Updated?.ToString(DateFormats.LONG_DATE_TIME),
            UpdatedBy = entity.UpdatedBy,
            IsDeleted = entity.IsDeleted,
            Deleted = entity.Deleted,
            DeletedString = entity.Deleted?.ToString(DateFormats.LONG_DATE_TIME),
            DeletedBy = entity.DeletedBy,
            Active = entity.Active,
        };
    }

    #endregion

    #region Operaciones de lectura

    public async Task<TDto?> GetByIdAsync(int id, bool includeDeleted = false)
    {
        Logger.Debug("Obteniendo {EntityType} por ID: {Id}, IncludeDeleted: {IncludeDeleted}", typeof(TEntity).Name, id,
            includeDeleted);

        var entity = await _repository.GetByIdAsync(id, includeDeleted);
        var result = entity != null ? MapToDto(entity) : null;

        Logger.Debug("{EntityType} obtenida por ID {Id}: {Found}", typeof(TEntity).Name, id, result != null);

        return result;
    }

    public async Task<PagedResponse<TDto>> GetAllAsync(PaginationFilter filter)
    {
        Logger.Debug(
            "Obteniendo todas las {EntityType} paginadas - Página: {PageNumber}, Tamaño: {PageSize}, IncludeDeleted: {IncludeDeleted}",
            typeof(TEntity).Name, filter.PageNumber, filter.PageSize, filter.IncludeDeleted);

        var pagedResult = await _repository.GetAllAsync(filter);
        var result = new PagedResponse<TDto>
        {
            PageNumber = pagedResult.PageNumber,
            PageSize = pagedResult.PageSize,
            TotalRecords = pagedResult.TotalRecords,
            Data = pagedResult.Data.Select(MapToDto).ToList()
        };

        Logger.Debug("Todas las {EntityType} obtenidas paginadas: {Count} elementos de {TotalRecords} totales",
            typeof(TEntity).Name, result.Data.Count, result.TotalRecords);

        return result;
    }

    public async Task<PagedResponse<TDto>> GetPagedAsync(TFilterDto filter)
    {
        Logger.Debug("Obteniendo {EntityType} paginadas - Página: {PageNumber}, Tamaño: {PageSize}, Filtros: {@Filter}",
            typeof(TEntity).Name, filter.PageNumber, filter.PageSize, filter);

        var paginationFilter = new PaginationFilter
        {
            PageNumber = filter.PageNumber,
            PageSize = filter.PageSize,
            IncludeDeleted = filter.IncludeDeleted
        };

        var pagedResult = await _repository.GetPagedAsync(paginationFilter);

        // Aplicar filtros adicionales en memoria
        var filteredData = pagedResult.Data.AsEnumerable();

        if (!string.IsNullOrWhiteSpace(filter.Name))
        {
            var nameLower = filter.Name.ToLower();
            filteredData = filteredData.Where(e => e.Name.ToLower().Contains(nameLower));
        }

        if (!string.IsNullOrWhiteSpace(filter.Description))
        {
            var descriptionLower = filter.Description.ToLower();
            filteredData = filteredData.Where(e =>
                e.Description != null && e.Description.ToLower().Contains(descriptionLower));
        }

        if (filter.Active.HasValue)
            filteredData = filteredData.Where(e => e.Active == filter.Active.Value);

        var result = new PagedResponse<TDto>
        {
            PageNumber = pagedResult.PageNumber,
            PageSize = pagedResult.PageSize,
            TotalRecords = pagedResult.TotalRecords,
            Data = filteredData.Select(MapToDto).ToList()
        };

        Logger.Debug(
            "{EntityType} paginadas obtenidas - Total: {TotalRecords}, Página: {PageNumber}, Elementos: {Count}",
            typeof(TEntity).Name, result.TotalRecords, result.PageNumber, result.Data.Count);

        return result;
    }

    public async Task<PagedResponse<TDto>> GetActiveAsync(PaginationFilter filter)
    {
        Logger.Debug("Obteniendo {EntityType} activas paginadas - Página: {PageNumber}, Tamaño: {PageSize}",
            typeof(TEntity).Name, filter.PageNumber, filter.PageSize);

        var pagedResult = await _repository.GetActiveAsync(filter);
        var result = new PagedResponse<TDto>
        {
            PageNumber = pagedResult.PageNumber,
            PageSize = pagedResult.PageSize,
            TotalRecords = pagedResult.TotalRecords,
            Data = pagedResult.Data.Select(MapToDto).ToList()
        };

        Logger.Debug("{EntityType} activas obtenidas paginadas: {Count} elementos de {TotalRecords} totales",
            typeof(TEntity).Name, result.Data.Count, result.TotalRecords);

        return result;
    }

    public async Task<PagedResponse<TDto>> GetByNameAsync(string name, PaginationFilter filter,
        bool includeDeleted = false)
    {
        Logger.Debug(
            "Obteniendo {EntityType} por nombre paginadas - Nombre: {Name}, Página: {PageNumber}, Tamaño: {PageSize}, IncludeDeleted: {IncludeDeleted}",
            typeof(TEntity).Name, name, filter.PageNumber, filter.PageSize, includeDeleted);

        var pagedResult = await _repository.GetByNameAsync(name, filter, includeDeleted);
        var result = new PagedResponse<TDto>
        {
            PageNumber = pagedResult.PageNumber,
            PageSize = pagedResult.PageSize,
            TotalRecords = pagedResult.TotalRecords,
            Data = pagedResult.Data.Select(MapToDto).ToList()
        };

        Logger.Debug(
            "{EntityType} por nombre obtenidas paginadas: {Count} elementos de {TotalRecords} totales para '{Name}'",
            typeof(TEntity).Name, result.Data.Count, result.TotalRecords, name);

        return result;
    }

    public async Task<PagedResponse<TDto>> GetByDescriptionAsync(string description, PaginationFilter filter,
        bool includeDeleted = false)
    {
        Logger.Debug(
            "Obteniendo {EntityType} por descripción paginadas - Descripción: {Description}, Página: {PageNumber}, Tamaño: {PageSize}, IncludeDeleted: {IncludeDeleted}",
            typeof(TEntity).Name, description, filter.PageNumber, filter.PageSize, includeDeleted);

        var pagedResult = await _repository.GetByDescriptionAsync(description, filter, includeDeleted);
        var result = new PagedResponse<TDto>
        {
            PageNumber = pagedResult.PageNumber,
            PageSize = pagedResult.PageSize,
            TotalRecords = pagedResult.TotalRecords,
            Data = pagedResult.Data.Select(MapToDto).ToList()
        };

        Logger.Debug(
            "{EntityType} por descripción obtenidas paginadas: {Count} elementos de {TotalRecords} totales para '{Description}'",
            typeof(TEntity).Name, result.Data.Count, result.TotalRecords, description);

        return result;
    }

    public async Task<PagedResponse<TDto>> FuzzySearchAsync(string searchTerm, PaginationFilter filter,
        bool includeDeleted = false)
    {
        Logger.Debug(
            "Búsqueda fuzzy en {EntityType} - Término: {SearchTerm}, Página: {PageNumber}, Tamaño: {PageSize}, IncludeDeleted: {IncludeDeleted}",
            typeof(TEntity).Name, searchTerm, filter.PageNumber, filter.PageSize, includeDeleted);

        var pagedResult = await _repository.FuzzySearchAsync(searchTerm, filter, includeDeleted);
        var result = new PagedResponse<TDto>
        {
            PageNumber = pagedResult.PageNumber,
            PageSize = pagedResult.PageSize,
            TotalRecords = pagedResult.TotalRecords,
            Data = pagedResult.Data.Select(MapToDto).ToList()
        };

        Logger.Debug(
            "Búsqueda fuzzy completada en {EntityType}: {Count} elementos de {TotalRecords} totales para '{SearchTerm}'",
            typeof(TEntity).Name, result.Data.Count, result.TotalRecords, searchTerm);

        return result;
    }

    #endregion

    #region Operaciones de escritura

    public async Task<TDto> CreateAsync(TCreateDto dto, string createdBy)
    {
        Logger.Debug("Creando nueva {EntityType}: {@Dto}, CreatedBy: {User}", typeof(TEntity).Name, dto, createdBy);

        // Validar que el nombre no exista
        if (await ExistsByNameAsync(dto.Name))
        {
            Logger.Warn("Intento de crear {EntityType} con nombre duplicado: {Name}", typeof(TEntity).Name, dto.Name);
            throw new InvalidOperationException($"Ya existe un elemento con el nombre '{dto.Name}'");
        }

        var entity = new TEntity
        {
            Name = dto.Name,
            Description = dto.Description,
            CreatedBy = createdBy
        };

        var createdEntity = await _repository.AddAsync(entity);
        var result = MapToDto(createdEntity);

        Logger.Info("{EntityType} creada exitosamente con ID: {Id} por usuario: {User}", typeof(TEntity).Name,
            result.Id, createdBy);

        return result;
    }

    public async Task<TDto> UpdateAsync(int id, TUpdateDto dto, string updatedBy)
    {
        Logger.Debug("Actualizando {EntityType} ID: {Id}, DTO: {@Dto}, UpdatedBy: {User}", typeof(TEntity).Name, id,
            dto, updatedBy);

        var entity = await _repository.GetByIdAsync(id);
        if (entity == null)
        {
            Logger.Warn("Intento de actualizar {EntityType} inexistente con ID: {Id}", typeof(TEntity).Name, id);
            throw new InvalidOperationException($"No se encontró el elemento con ID {id}");
        }

        // Validar que el nombre no exista en otro elemento
        if (await ExistsByNameAsync(dto.Name, id))
        {
            Logger.Warn("Intento de actualizar {EntityType} con nombre duplicado: {Name}, ID: {Id}",
                typeof(TEntity).Name, dto.Name, id);
            throw new InvalidOperationException($"Ya existe otro elemento con el nombre '{dto.Name}'");
        }

        entity.Name = dto.Name;
        entity.Description = dto.Description;

        var updatedEntity = await _repository.UpdateAsync(entity);
        var result = MapToDto(updatedEntity);

        Logger.Info("{EntityType} actualizada exitosamente con ID: {Id} por usuario: {User}", typeof(TEntity).Name,
            result.Id, updatedBy);

        return result;
    }

    #endregion

    #region Operaciones de eliminación

    public async Task<bool> DeleteAsync(int id, string deletedBy)
    {
        Logger.Debug("Eliminando {EntityType} ID: {Id} por usuario: {User}", typeof(TEntity).Name, id, deletedBy);

        var result = await _repository.DeleteAsync(id, deletedBy);

        if (result)
            Logger.Info("{EntityType} eliminada exitosamente con ID: {Id} por usuario: {User}", typeof(TEntity).Name,
                id, deletedBy);
        else
            Logger.Warn("{EntityType} no encontrada para eliminar con ID: {Id}", typeof(TEntity).Name, id);

        return result;
    }

    public async Task<bool> HardDeleteAsync(int id)
    {
        Logger.Debug("Eliminación permanente de {EntityType} ID: {Id}", typeof(TEntity).Name, id);

        var result = await _repository.HardDeleteAsync(id);

        if (result)
            Logger.Info("{EntityType} eliminada permanentemente con ID: {Id}", typeof(TEntity).Name, id);
        else
            Logger.Warn("{EntityType} no encontrada para eliminación permanente con ID: {Id}", typeof(TEntity).Name,
                id);

        return result;
    }

    #endregion

    #region Operaciones de activación/desactivación

    public async Task<bool> ActivateAsync(int id, string updatedBy)
    {
        Logger.Debug("Activando {EntityType} ID: {Id} por usuario: {User}", typeof(TEntity).Name, id, updatedBy);

        var result = await _repository.ActivateAsync(id, updatedBy);

        if (result)
            Logger.Info("{EntityType} activada exitosamente con ID: {Id} por usuario: {User}", typeof(TEntity).Name, id,
                updatedBy);
        else
            Logger.Warn("{EntityType} no encontrada para activar con ID: {Id}", typeof(TEntity).Name, id);

        return result;
    }

    public async Task<bool> DeactivateAsync(int id, string updatedBy)
    {
        Logger.Debug("Desactivando {EntityType} ID: {Id} por usuario: {User}", typeof(TEntity).Name, id, updatedBy);

        var result = await _repository.DeactivateAsync(id, updatedBy);

        if (result)
            Logger.Info("{EntityType} desactivada exitosamente con ID: {Id} por usuario: {User}", typeof(TEntity).Name,
                id, updatedBy);
        else
            Logger.Warn("{EntityType} no encontrada para desactivar con ID: {Id}", typeof(TEntity).Name, id);

        return result;
    }

    #endregion

    #region Operaciones de validación

    public async Task<bool> ExistsAsync(int id, bool includeDeleted = false)
    {
        Logger.Debug("Verificando existencia de {EntityType} ID: {Id}, IncludeDeleted: {IncludeDeleted}",
            typeof(TEntity).Name, id, includeDeleted);

        var result = await _repository.ExistsAsync(id, includeDeleted);

        Logger.Debug("Verificación de existencia completada para {EntityType} ID {Id}: {Exists}", typeof(TEntity).Name,
            id, result);

        return result;
    }

    public async Task<bool> ExistsByNameAsync(string name, int? excludeId = null, bool includeDeleted = false)
    {
        Logger.Debug(
            "Verificando existencia de {EntityType} por nombre: {Name}, ExcludeId: {ExcludeId}, IncludeDeleted: {IncludeDeleted}",
            typeof(TEntity).Name, name, excludeId, includeDeleted);

        // Usar el método específico del repositorio
        var entities = await _repository.GetByNameAsync(name, new PaginationFilter
        {
            PageNumber = 1,
            PageSize = 1 // No se necesita paginar
        }, includeDeleted);

        // Filtrar por nombre exacto (case-insensitive) y excluir ID si es necesario
        var nameLower = name.ToLower();
        var filteredEntities = entities.Data.Where(e => e.Name.ToLower() == nameLower);

        if (excludeId.HasValue)
            filteredEntities = filteredEntities.Where(e => e.Id != excludeId.Value);

        var result = filteredEntities.Any();

        Logger.Debug("Verificación de existencia por nombre completada para '{Name}' en {EntityType}: {Exists}", name,
            typeof(TEntity).Name, result);

        return result;
    }

    #endregion
}