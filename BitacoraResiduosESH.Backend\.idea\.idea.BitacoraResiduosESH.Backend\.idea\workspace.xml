<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoGeneratedRunConfigurationManager">
    <projectFile profileName="http">BitacoraResiduosESH.Backend/BitacoraResiduosESH.Backend.Backend.WebAPI/BitacoraResiduosESH.Backend.Backend.WebAPI.csproj</projectFile>
    <projectFile profileName="https">BitacoraResiduosESH.Backend/BitacoraResiduosESH.Backend.Backend.WebAPI/BitacoraResiduosESH.Backend.Backend.WebAPI.csproj</projectFile>
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="301f36bb-497d-4dc0-a0ed-6294221f6aff" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/../BitacoraResiduosESH.Frontend/src/components/Sidebar.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/../BitacoraResiduosESH.Frontend/src/components/Sidebar.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../BitacoraResiduosESH.Frontend/src/routes.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/../BitacoraResiduosESH.Frontend/src/routes.tsx" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="DpaMonitoringSettings">
    <option name="firstShow" value="false" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="mock:///AIAssistantSnippet.." root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/BitacoraResiduosESH.Backend/BitacoraResiduosESH.Backend.Backend.Application/DTOs/ContainerType/ContainerTypeFilterDto.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="mock://C:/Repositories/WebProjects/BitacoraResiduosESH/BitacoraResiduosESH.Backend/BitacoraResiduosESH.Backend/BitacoraResiduosESH.Backend.Backend.Application/DTOs/Role/RoleDto.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock://C:/Repositories/WebProjects/BitacoraResiduosESH/BitacoraResiduosESH.Backend/BitacoraResiduosESH.Backend/BitacoraResiduosESH.Backend.Backend.Application/Interfaces/Repositories/IGenericRepository.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock://C:/Repositories/WebProjects/BitacoraResiduosESH/BitacoraResiduosESH.Backend/BitacoraResiduosESH.Backend/BitacoraResiduosESH.Backend.Backend.Application/Services/RoleService.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock://C:/Repositories/WebProjects/BitacoraResiduosESH/BitacoraResiduosESH.Backend/BitacoraResiduosESH.Backend/BitacoraResiduosESH.Backend.Backend.WebAPI/Program.cs" root0="SKIP_HIGHLIGHTING" />
  </component>
  <component name="MetaFilesCheckinStateConfiguration" checkMetaFiles="true" />
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 8
}</component>
  <component name="ProjectId" id="2zYkkn3LalpmVy9PKKlOOHXINYg" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="1" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    ".NET Launch Settings Profile.BitacoraResiduosESH.Backend.Backend.WebAPI: http.executor": "Run",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "master",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.standard": "",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "project.propVCSSupport.DirectoryMappings",
    "ts.external.directory.path": "C:\\Repositories\\WebProjects\\BitacoraResiduosESH\\BitacoraResiduosESH.Frontend\\node_modules\\typescript\\lib",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager" selected=".NET Launch Settings Profile.BitacoraResiduosESH.Backend.Backend.WebAPI: http">
    <configuration name="BitacoraResiduosESH.Backend.Backend.WebAPI: http" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/BitacoraResiduosESH.Backend/BitacoraResiduosESH.Backend.Backend.WebAPI/BitacoraResiduosESH.Backend.Backend.WebAPI.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net9.0" />
      <option name="LAUNCH_PROFILE_NAME" value="http" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="BitacoraResiduosESH.Backend.Backend.WebAPI: https" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/BitacoraResiduosESH.Backend/BitacoraResiduosESH.Backend.Backend.WebAPI/BitacoraResiduosESH.Backend.Backend.WebAPI.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net9.0" />
      <option name="LAUNCH_PROFILE_NAME" value="https" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="301f36bb-497d-4dc0-a0ed-6294221f6aff" name="Changes" comment="" />
      <created>1751915540611</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751915540611</updated>
      <workItem from="1751915541826" duration="30000" />
      <workItem from="1751915603435" duration="121000" />
      <workItem from="1751930556243" duration="2057000" />
      <workItem from="1751982886282" duration="14114000" />
    </task>
    <task id="LOCAL-00001" summary="Update VCS directory mapping configuration">
      <option name="closed" value="true" />
      <created>1751915713454</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1751915713454</updated>
    </task>
    <option name="localTasksCounter" value="2" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnityCheckinConfiguration" checkUnsavedScenes="true" />
  <component name="UnityProjectConfiguration" hasMinimizedUI="false" />
  <component name="VcsManagerConfiguration">
    <ignored-roots>
      <path value="$PROJECT_DIR$" />
    </ignored-roots>
    <option name="CLEAR_INITIAL_COMMIT_MESSAGE" value="true" />
    <MESSAGE value="Update VCS directory mapping configuration" />
    <option name="LAST_COMMIT_MESSAGE" value="Update VCS directory mapping configuration" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.OperationCanceledException" breakIfHandledByOtherCode="false" displayValue="System.OperationCanceledException" />
          <option name="timeStamp" value="1" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.Tasks.TaskCanceledException" breakIfHandledByOtherCode="false" displayValue="System.Threading.Tasks.TaskCanceledException" />
          <option name="timeStamp" value="2" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.ThreadAbortException" breakIfHandledByOtherCode="false" displayValue="System.Threading.ThreadAbortException" />
          <option name="timeStamp" value="3" />
        </breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>