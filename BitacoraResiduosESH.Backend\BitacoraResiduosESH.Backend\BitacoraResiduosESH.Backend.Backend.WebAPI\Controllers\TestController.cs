using System.Security.Claims;

namespace BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers;

[ApiController]
[Route("api/[controller]")]
public class TestController : ControllerBase
{
    /// <summary>
    ///     Endpoint público para verificar que la API funciona
    /// </summary>
    [HttpGet("public")]
    public ActionResult<string> PublicEndpoint()
    {
        return Ok("API funcionando correctamente - Endpoint público");
    }

    /// <summary>
    ///     Endpoint protegido que requiere autenticación
    /// </summary>
    [HttpGet("protected")]
    [Authorize]
    public ActionResult<string> ProtectedEndpoint()
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value
                     ?? User.FindFirst(ClaimTypes.Name)?.Value
                     ?? User.FindFirst("nameid")?.Value;
        var email = User.FindFirst(ClaimTypes.Email)?.Value;
        var role = User.FindFirst(ClaimTypes.Role)?.Value;

        return Ok(new
        {
            message = "Endpoint protegido accedido correctamente",
            userId,
            email,
            role,
            claims = User.Claims.Select(c => new { c.Type, c.Value }).ToList()
        });
    }

    /// <summary>
    ///     Endpoint protegido que requiere rol Admin
    /// </summary>
    [HttpGet("admin")]
    [Authorize(Roles = "Admin")]
    public ActionResult<string> AdminEndpoint()
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value
                     ?? User.FindFirst(ClaimTypes.Name)?.Value
                     ?? User.FindFirst("nameid")?.Value;
        var email = User.FindFirst(ClaimTypes.Email)?.Value;

        return Ok(new
        {
            message = "Endpoint de Admin accedido correctamente",
            userId,
            email,
            role = "Admin"
        });
    }
}