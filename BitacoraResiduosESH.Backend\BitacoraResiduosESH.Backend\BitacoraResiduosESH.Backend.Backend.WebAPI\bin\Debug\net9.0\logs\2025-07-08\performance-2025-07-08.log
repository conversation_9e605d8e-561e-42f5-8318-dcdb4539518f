2025-07-08 08:28:43.5181|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (27ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;|duration: 
2025-07-08 08:28:43.5299|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Program|Base de datos inicializada correctamente|duration: 
2025-07-08 08:28:43.5409|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|Inicializando base de datos con datos de prueba...|duration: 
2025-07-08 08:28:43.7761|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (2ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM "Role" AS "r")|duration: 
2025-07-08 08:28:43.7902|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|La base de datos ya contiene datos. Saltando inicialización.|duration: 
2025-07-08 08:28:43.7902|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Program|Datos de prueba inicializados correctamente|duration: 
2025-07-08 08:28:43.8909|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Program|Aplicación iniciada correctamente|duration: 
2025-07-08 08:28:43.9291|INFO|Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager|User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.|duration: 
2025-07-08 08:28:43.9939|WARN|Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware|The WebRootPath was not found: C:\Repositories\WebProjects\BitacoraResiduosESH\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend.Backend.WebAPI\wwwroot. Static files may be unavailable.|duration: 
2025-07-08 08:28:44.0866|INFO|Microsoft.Hosting.Lifetime|Now listening on: http://localhost:5001|duration: 
2025-07-08 08:28:44.1005|INFO|Microsoft.Hosting.Lifetime|Application started. Press Ctrl+C to shut down.|duration: 
2025-07-08 08:28:44.1005|INFO|Microsoft.Hosting.Lifetime|Hosting environment: Development|duration: 
2025-07-08 08:28:44.1005|INFO|Microsoft.Hosting.Lifetime|Content root path: C:\Repositories\WebProjects\BitacoraResiduosESH\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend.Backend.WebAPI|duration: 
2025-07-08 08:28:44.1515|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar - - -|duration: 3.240
2025-07-08 08:28:44.2170|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 72.299
2025-07-08 08:28:44.2297|INFO|Microsoft.AspNetCore.Http.Result.RedirectResult|Executing RedirectResult, redirecting to scalar/.|duration: 84.359
2025-07-08 08:28:44.2297|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 84.696
2025-07-08 08:28:44.2297|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar - 302 0 - 89.4231ms|duration: 92.310
2025-07-08 08:28:44.2484|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar/ - - -|duration: 0.197
2025-07-08 08:28:44.2484|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 5.290
2025-07-08 08:28:44.2886|INFO|Microsoft.AspNetCore.Http.Result.ContentResult|Write content with HTTP Response ContentType of text/html|duration: 42.982
2025-07-08 08:28:44.2886|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 43.643
2025-07-08 08:28:44.2886|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar/ - 200 924 text/html 43.8950ms|duration: 44.102
2025-07-08 08:28:44.2886|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar/scalar.js - - -|duration: 0.131
2025-07-08 08:28:44.2886|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar/scalar.aspnetcore.js - - -|duration: 0.104
2025-07-08 08:28:44.3044|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/scalar.js'|duration: 3.149
2025-07-08 08:28:44.3044|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 3.079
2025-07-08 08:28:44.3044|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/scalar.aspnetcore.js'|duration: 3.704
2025-07-08 08:28:44.3044|INFO|Microsoft.AspNetCore.Http.Result.StatusCodeResult|Setting HTTP status code 304.|duration: 14.613
2025-07-08 08:28:44.3044|INFO|Microsoft.AspNetCore.Http.Result.StatusCodeResult|Setting HTTP status code 304.|duration: 14.042
2025-07-08 08:28:44.3044|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/scalar.js'|duration: 14.917
2025-07-08 08:28:44.3044|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/scalar.aspnetcore.js'|duration: 14.441
2025-07-08 08:28:44.3044|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar/scalar.js - 304 - - 15.2623ms|duration: 15.361
2025-07-08 08:28:44.3044|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar/scalar.aspnetcore.js - 304 - - 15.3630ms|duration: 15.446
2025-07-08 08:28:44.4806|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/openapi/v1.json - - -|duration: 0.161
2025-07-08 08:28:44.4806|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /openapi/{documentName}.json'|duration: 3.101
2025-07-08 08:28:44.7058|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /openapi/{documentName}.json'|duration: 225.338
2025-07-08 08:28:44.7058|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/openapi/v1.json - 200 - application/json;charset=utf-8 227.2896ms|duration: 227.483
2025-07-08 08:28:45.2665|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/favicon.ico - - -|duration: 0.747
2025-07-08 08:28:45.2665|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/favicon.ico - 404 0 - 2.8278ms|duration: 2.960
2025-07-08 08:28:45.2665|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5001/favicon.ico, Response status code: 404|duration: 3.395
2025-07-08 08:28:56.6808|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 OPTIONS http://localhost:5001/api/auth/login - - -|duration: 0.301
2025-07-08 08:28:56.6808|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 4.154
2025-07-08 08:28:56.6808|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 OPTIONS http://localhost:5001/api/auth/login - 204 - - 6.3960ms|duration: 6.742
2025-07-08 08:28:56.6808|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 POST http://localhost:5001/api/auth/login - application/json 47|duration: 0.292
2025-07-08 08:28:56.6971|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 7.536
2025-07-08 08:28:56.6971|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AuthController.Login (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 9.117
2025-07-08 08:28:56.7913|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[BitacoraResiduosESH.Backend.Backend.Application.DTOs.Auth.AuthResponseDto]] Login(BitacoraResiduosESH.Backend.Backend.Application.DTOs.Auth.LoginDto) on controller BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AuthController (BitacoraResiduosESH.Backend.Backend.WebAPI).|duration: 98.425
2025-07-08 08:28:57.1408|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executing action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AuthController.Login (BitacoraResiduosESH.Backend.Backend.WebAPI) - Validation state: Valid|duration: 447.805
2025-07-08 08:28:57.7953|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (17ms) [Parameters=[@__ToLower_0='?' (Size = 6)], CommandType='Text', CommandTimeout='30']
SELECT "u"."Id", "u"."Active", "u"."Created", "u"."CreatedBy", "u"."Deleted", "u"."DeletedBy", "u"."Email", "u"."EmployeeNumber", "u"."IsDeleted", "u"."Name", "u"."Password", "u"."RoleId", "u"."Updated", "u"."UpdatedBy", "r"."Id", "r"."Active", "r"."Created", "r"."CreatedBy", "r"."Deleted", "r"."DeletedBy", "r"."Description", "r"."IsDeleted", "r"."Name", "r"."Updated", "r"."UpdatedBy"
FROM "User" AS "u"
INNER JOIN "Role" AS "r" ON "u"."RoleId" = "r"."Id"
WHERE NOT ("u"."IsDeleted") AND lower("u"."EmployeeNumber") = @__ToLower_0
LIMIT 1|duration: 1103.601
2025-07-08 08:28:59.6021|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (5ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
SELECT "u"."Id", "u"."Active", "u"."Created", "u"."CreatedBy", "u"."Deleted", "u"."DeletedBy", "u"."Email", "u"."EmployeeNumber", "u"."IsDeleted", "u"."Name", "u"."Password", "u"."RoleId", "u"."Updated", "u"."UpdatedBy", "r"."Id", "r"."Active", "r"."Created", "r"."CreatedBy", "r"."Deleted", "r"."DeletedBy", "r"."Description", "r"."IsDeleted", "r"."Name", "r"."Updated", "r"."UpdatedBy"
FROM "User" AS "u"
INNER JOIN "Role" AS "r" ON "u"."RoleId" = "r"."Id"
WHERE NOT ("u"."IsDeleted") AND "u"."Id" = @__id_0
LIMIT 1|duration: 2910.271
2025-07-08 08:28:59.6021|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AuthController|Login exitoso para usuario: "<EMAIL>"/"123456"|duration: 2918.991
2025-07-08 08:28:59.6281|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AuthController.Login (BitacoraResiduosESH.Backend.Backend.WebAPI), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 2471.7343ms.|duration: 2935.146
2025-07-08 08:28:59.6820|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor|Executing OkObjectResult, writing value of type 'BitacoraResiduosESH.Backend.Backend.Application.DTOs.Auth.AuthResponseDto'.|duration: 2992.600
2025-07-08 08:28:59.7156|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AuthController.Login (BitacoraResiduosESH.Backend.Backend.WebAPI) in 2903.6608ms|duration: 3022.559
2025-07-08 08:28:59.7156|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AuthController.Login (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 3023.227
2025-07-08 08:28:59.7268|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 POST http://localhost:5001/api/auth/login - 200 - application/json;+charset=utf-8 3036.6796ms|duration: 3037.007
2025-07-08 08:29:22.1023|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 OPTIONS http://localhost:5001/api/area?pageNumber=1&pageSize=10&includeDeleted=false - - -|duration: 0.650
2025-07-08 08:29:22.1023|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 3.607
2025-07-08 08:29:22.1023|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 OPTIONS http://localhost:5001/api/area?pageNumber=1&pageSize=10&includeDeleted=false - 204 - - 4.9596ms|duration: 5.261
2025-07-08 08:29:22.1023|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/api/area?pageNumber=1&pageSize=10&includeDeleted=false - application/json -|duration: 0.170
2025-07-08 08:29:22.1727|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 69.557
2025-07-08 08:29:22.1791|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/api/area?pageNumber=1&pageSize=10&includeDeleted=false - 404 0 - 71.2127ms|duration: 71.436
2025-07-08 08:29:22.1791|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5001/api/area, Response status code: 404|duration: 71.957
2025-07-08 08:29:23.1959|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/api/area?pageNumber=1&pageSize=10&includeDeleted=false - application/json -|duration: 0.452
2025-07-08 08:29:23.1959|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 5.870
2025-07-08 08:29:23.1959|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/api/area?pageNumber=1&pageSize=10&includeDeleted=false - 404 0 - 7.3483ms|duration: 7.858
2025-07-08 08:29:23.1959|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5001/api/area, Response status code: 404|duration: 8.977
2025-07-08 08:29:25.2172|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/api/area?pageNumber=1&pageSize=10&includeDeleted=false - application/json -|duration: 0.666
2025-07-08 08:29:25.2172|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 8.619
2025-07-08 08:29:25.2264|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/api/area?pageNumber=1&pageSize=10&includeDeleted=false - 404 0 - 11.1463ms|duration: 11.538
2025-07-08 08:29:25.2264|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5001/api/area, Response status code: 404|duration: 12.967
2025-07-08 08:29:29.2323|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 OPTIONS http://localhost:5001/api/area?pageNumber=1&pageSize=10&includeDeleted=false - - -|duration: 0.603
2025-07-08 08:29:29.2323|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 4.609
2025-07-08 08:29:29.2323|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 OPTIONS http://localhost:5001/api/area?pageNumber=1&pageSize=10&includeDeleted=false - 204 - - 6.3250ms|duration: 6.546
2025-07-08 08:29:29.2323|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/api/area?pageNumber=1&pageSize=10&includeDeleted=false - application/json -|duration: 0.147
2025-07-08 08:29:29.2421|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 6.054
2025-07-08 08:29:29.2421|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/api/area?pageNumber=1&pageSize=10&includeDeleted=false - 404 0 - 8.1707ms|duration: 8.367
2025-07-08 08:29:29.2421|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5001/api/area, Response status code: 404|duration: 9.243
2025-07-08 08:29:33.3213|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 OPTIONS http://localhost:5001/api/area - - -|duration: 0.670
2025-07-08 08:29:33.3213|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 3.134
2025-07-08 08:29:33.3213|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 OPTIONS http://localhost:5001/api/area - 204 - - 4.6416ms|duration: 4.909
2025-07-08 08:29:33.3213|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 POST http://localhost:5001/api/area - application/json 36|duration: 0.201
2025-07-08 08:29:33.3213|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 3.557
2025-07-08 08:29:33.3213|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 POST http://localhost:5001/api/area - 404 0 - 4.4640ms|duration: 4.755
2025-07-08 08:29:33.3213|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request reached the end of the middleware pipeline without being handled by application code. Request path: POST http://localhost:5001/api/area, Response status code: 404|duration: 5.379
2025-07-08 08:29:34.3554|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 POST http://localhost:5001/api/area - application/json 36|duration: 0.549
2025-07-08 08:29:34.3554|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 5.329
2025-07-08 08:29:34.3554|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 POST http://localhost:5001/api/area - 404 0 - 7.1005ms|duration: 7.412
2025-07-08 08:29:34.3554|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request reached the end of the middleware pipeline without being handled by application code. Request path: POST http://localhost:5001/api/area, Response status code: 404|duration: 7.936
2025-07-08 08:29:36.3705|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 POST http://localhost:5001/api/area - application/json 36|duration: 0.581
2025-07-08 08:29:36.3705|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 6.767
2025-07-08 08:29:36.3705|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 POST http://localhost:5001/api/area - 404 0 - 8.4128ms|duration: 8.620
2025-07-08 08:29:36.3705|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request reached the end of the middleware pipeline without being handled by application code. Request path: POST http://localhost:5001/api/area, Response status code: 404|duration: 9.021
2025-07-08 08:30:24.5303|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 OPTIONS http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - - -|duration: 0.413
2025-07-08 08:30:24.5303|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 1.400
2025-07-08 08:30:24.5303|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 OPTIONS http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - 204 - - 1.7288ms|duration: 1.927
2025-07-08 08:30:24.5303|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - application/json -|duration: 0.081
2025-07-08 08:30:24.5386|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 5.087
2025-07-08 08:30:24.5386|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 5.420
2025-07-08 08:30:24.5386|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Route matched with {action = "GetAll", controller = "Areas"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[BitacoraResiduosESH.Backend.Backend.Application.Common.PagedResponse`1[BitacoraResiduosESH.Backend.Backend.Application.DTOs.Area.AreaDto]]] GetAll(Int32, Int32, Boolean) on controller BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController (BitacoraResiduosESH.Backend.Backend.WebAPI).|duration: 16.843
2025-07-08 08:30:24.5547|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI) in 1.1377ms|duration: 20.672
2025-07-08 08:30:24.5547|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 20.842
2025-07-08 08:30:24.5547|ERROR|Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware|An unhandled exception has occurred while executing the request.|duration: 30.167
2025-07-08 08:30:24.5547|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - 500 - text/plain;+charset=utf-8 33.3185ms|duration: 33.402
2025-07-08 08:30:26.3644|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - application/json -|duration: 0.271
2025-07-08 08:30:26.3644|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 1.756
2025-07-08 08:30:26.3663|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 1.996
2025-07-08 08:30:26.3663|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Route matched with {action = "GetAll", controller = "Areas"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[BitacoraResiduosESH.Backend.Backend.Application.Common.PagedResponse`1[BitacoraResiduosESH.Backend.Backend.Application.DTOs.Area.AreaDto]]] GetAll(Int32, Int32, Boolean) on controller BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController (BitacoraResiduosESH.Backend.Backend.WebAPI).|duration: 2.154
2025-07-08 08:30:26.3663|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI) in 0.1626ms|duration: 2.952
2025-07-08 08:30:26.3663|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 3.046
2025-07-08 08:30:26.3663|ERROR|Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware|An unhandled exception has occurred while executing the request.|duration: 4.668
2025-07-08 08:30:26.3663|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - 500 - text/plain;+charset=utf-8 5.2990ms|duration: 5.363
2025-07-08 08:30:28.0559|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - application/json -|duration: 0.216
2025-07-08 08:30:28.0559|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 1.433
2025-07-08 08:30:28.0559|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 1.571
2025-07-08 08:30:28.0559|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Route matched with {action = "GetAll", controller = "Areas"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[BitacoraResiduosESH.Backend.Backend.Application.Common.PagedResponse`1[BitacoraResiduosESH.Backend.Backend.Application.DTOs.Area.AreaDto]]] GetAll(Int32, Int32, Boolean) on controller BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController (BitacoraResiduosESH.Backend.Backend.WebAPI).|duration: 1.687
2025-07-08 08:30:28.0559|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI) in 0.175ms|duration: 2.303
2025-07-08 08:30:28.0559|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 2.379
2025-07-08 08:30:28.0559|ERROR|Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware|An unhandled exception has occurred while executing the request.|duration: 3.496
2025-07-08 08:30:28.0559|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - 500 - text/plain;+charset=utf-8 3.7973ms|duration: 3.848
2025-07-08 08:30:29.1744|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - application/json -|duration: 0.303
2025-07-08 08:30:29.1744|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 2.065
2025-07-08 08:30:29.1744|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 2.390
2025-07-08 08:30:29.1744|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Route matched with {action = "GetAll", controller = "Areas"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[BitacoraResiduosESH.Backend.Backend.Application.Common.PagedResponse`1[BitacoraResiduosESH.Backend.Backend.Application.DTOs.Area.AreaDto]]] GetAll(Int32, Int32, Boolean) on controller BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController (BitacoraResiduosESH.Backend.Backend.WebAPI).|duration: 2.589
2025-07-08 08:30:29.1744|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI) in 0.2925ms|duration: 4.068
2025-07-08 08:30:29.1785|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 4.149
2025-07-08 08:30:29.1785|ERROR|Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware|An unhandled exception has occurred while executing the request.|duration: 5.200
2025-07-08 08:30:29.1785|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - 500 - text/plain;+charset=utf-8 5.4721ms|duration: 5.524
2025-07-08 08:30:30.0519|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 OPTIONS http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - - -|duration: 0.110
2025-07-08 08:30:30.0519|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 0.720
2025-07-08 08:30:30.0519|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 OPTIONS http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - 204 - - 0.9496ms|duration: 1.012
2025-07-08 08:30:30.0519|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - application/json -|duration: 0.111
2025-07-08 08:30:30.0544|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 0.950
2025-07-08 08:30:30.0544|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 1.077
2025-07-08 08:30:30.0544|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Route matched with {action = "GetAll", controller = "Areas"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[BitacoraResiduosESH.Backend.Backend.Application.Common.PagedResponse`1[BitacoraResiduosESH.Backend.Backend.Application.DTOs.Area.AreaDto]]] GetAll(Int32, Int32, Boolean) on controller BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController (BitacoraResiduosESH.Backend.Backend.WebAPI).|duration: 1.241
2025-07-08 08:30:30.0544|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI) in 0.2421ms|duration: 4.204
2025-07-08 08:30:30.0544|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 4.300
2025-07-08 08:30:30.0544|ERROR|Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware|An unhandled exception has occurred while executing the request.|duration: 5.501
2025-07-08 08:30:30.0544|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - 500 - text/plain;+charset=utf-8 5.8098ms|duration: 5.869
2025-07-08 08:30:31.1270|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - application/json -|duration: 0.270
2025-07-08 08:30:31.1270|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 1.978
2025-07-08 08:30:31.1270|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 2.111
2025-07-08 08:30:31.1270|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Route matched with {action = "GetAll", controller = "Areas"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[BitacoraResiduosESH.Backend.Backend.Application.Common.PagedResponse`1[BitacoraResiduosESH.Backend.Backend.Application.DTOs.Area.AreaDto]]] GetAll(Int32, Int32, Boolean) on controller BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController (BitacoraResiduosESH.Backend.Backend.WebAPI).|duration: 2.212
2025-07-08 08:30:31.1270|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI) in 0.118ms|duration: 2.765
2025-07-08 08:30:31.1270|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 2.822
2025-07-08 08:30:31.1270|ERROR|Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware|An unhandled exception has occurred while executing the request.|duration: 3.861
2025-07-08 08:30:31.1315|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - 500 - text/plain;+charset=utf-8 4.4781ms|duration: 4.565
2025-07-08 08:30:37.8232|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 OPTIONS http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - - -|duration: 0.347
2025-07-08 08:30:37.8232|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 1.235
2025-07-08 08:30:37.8232|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 OPTIONS http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - 204 - - 1.5622ms|duration: 1.785
2025-07-08 08:30:37.8232|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - application/json -|duration: 0.134
2025-07-08 08:30:37.8232|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 1.171
2025-07-08 08:30:37.8232|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 1.293
2025-07-08 08:30:37.8232|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Route matched with {action = "GetAll", controller = "Areas"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[BitacoraResiduosESH.Backend.Backend.Application.Common.PagedResponse`1[BitacoraResiduosESH.Backend.Backend.Application.DTOs.Area.AreaDto]]] GetAll(Int32, Int32, Boolean) on controller BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController (BitacoraResiduosESH.Backend.Backend.WebAPI).|duration: 1.471
2025-07-08 08:30:37.8232|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI) in 0.2371ms|duration: 2.597
2025-07-08 08:30:37.8232|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 2.767
2025-07-08 08:30:37.8232|ERROR|Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware|An unhandled exception has occurred while executing the request.|duration: 4.470
2025-07-08 08:30:37.8232|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - 500 - text/plain;+charset=utf-8 4.7797ms|duration: 4.921
2025-07-08 08:30:41.8378|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - application/json -|duration: 0.273
2025-07-08 08:30:41.8378|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 1.499
2025-07-08 08:30:41.8378|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 1.746
2025-07-08 08:30:41.8378|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Route matched with {action = "GetAll", controller = "Areas"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[BitacoraResiduosESH.Backend.Backend.Application.Common.PagedResponse`1[BitacoraResiduosESH.Backend.Backend.Application.DTOs.Area.AreaDto]]] GetAll(Int32, Int32, Boolean) on controller BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController (BitacoraResiduosESH.Backend.Backend.WebAPI).|duration: 1.912
2025-07-08 08:30:41.8378|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI) in 0.1675ms|duration: 2.672
2025-07-08 08:30:41.8378|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 2.789
2025-07-08 08:30:41.8378|ERROR|Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware|An unhandled exception has occurred while executing the request.|duration: 4.068
2025-07-08 08:30:41.8378|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - 500 - text/plain;+charset=utf-8 4.4089ms|duration: 4.473
2025-07-08 08:30:52.2014|INFO|Microsoft.Hosting.Lifetime|Application is shutting down...|duration: 
2025-07-08 08:32:00.7073|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (15ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;|duration: 
2025-07-08 08:32:00.7136|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Program|Base de datos inicializada correctamente|duration: 
2025-07-08 08:32:00.7284|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|Inicializando base de datos con datos de prueba...|duration: 
2025-07-08 08:32:00.9450|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (2ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM "Role" AS "r")|duration: 
2025-07-08 08:32:00.9609|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|La base de datos ya contiene datos. Saltando inicialización.|duration: 
2025-07-08 08:32:00.9609|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Program|Datos de prueba inicializados correctamente|duration: 
2025-07-08 08:32:01.0460|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Program|Aplicación iniciada correctamente|duration: 
2025-07-08 08:32:01.0856|INFO|Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager|User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.|duration: 
2025-07-08 08:32:01.1474|WARN|Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware|The WebRootPath was not found: C:\Repositories\WebProjects\BitacoraResiduosESH\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend.Backend.WebAPI\wwwroot. Static files may be unavailable.|duration: 
2025-07-08 08:32:01.2262|INFO|Microsoft.Hosting.Lifetime|Now listening on: http://localhost:5001|duration: 
2025-07-08 08:32:01.2262|INFO|Microsoft.Hosting.Lifetime|Application started. Press Ctrl+C to shut down.|duration: 
2025-07-08 08:32:01.2262|INFO|Microsoft.Hosting.Lifetime|Hosting environment: Development|duration: 
2025-07-08 08:32:01.2262|INFO|Microsoft.Hosting.Lifetime|Content root path: C:\Repositories\WebProjects\BitacoraResiduosESH\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend.Backend.WebAPI|duration: 
2025-07-08 08:32:01.6977|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar - - -|duration: 5.592
2025-07-08 08:32:01.7733|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 82.806
2025-07-08 08:32:01.7733|INFO|Microsoft.AspNetCore.Http.Result.RedirectResult|Executing RedirectResult, redirecting to scalar/.|duration: 91.279
2025-07-08 08:32:01.7733|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 91.714
2025-07-08 08:32:01.7892|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar - 302 0 - 92.9965ms|duration: 96.734
2025-07-08 08:32:01.7892|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar/ - - -|duration: 0.151
2025-07-08 08:32:01.7892|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 2.994
2025-07-08 08:32:01.8284|INFO|Microsoft.AspNetCore.Http.Result.ContentResult|Write content with HTTP Response ContentType of text/html|duration: 41.621
2025-07-08 08:32:01.8284|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 42.262
2025-07-08 08:32:01.8284|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar/ - 200 924 text/html 42.6534ms|duration: 42.825
2025-07-08 08:32:01.8456|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar/scalar.js - - -|duration: 0.137
2025-07-08 08:32:01.8456|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar/scalar.aspnetcore.js - - -|duration: 0.108
2025-07-08 08:32:01.8456|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/scalar.js'|duration: 4.085
2025-07-08 08:32:01.8456|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 3.902
2025-07-08 08:32:01.8506|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/scalar.aspnetcore.js'|duration: 4.509
2025-07-08 08:32:01.8506|INFO|Microsoft.AspNetCore.Http.Result.StatusCodeResult|Setting HTTP status code 304.|duration: 12.814
2025-07-08 08:32:01.8506|INFO|Microsoft.AspNetCore.Http.Result.StatusCodeResult|Setting HTTP status code 304.|duration: 13.657
2025-07-08 08:32:01.8506|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/scalar.aspnetcore.js'|duration: 13.008
2025-07-08 08:32:01.8506|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/scalar.js'|duration: 13.962
2025-07-08 08:32:01.8506|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar/scalar.js - 304 - - 14.2740ms|duration: 14.398
2025-07-08 08:32:01.8506|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar/scalar.aspnetcore.js - 304 - - 14.1790ms|duration: 14.285
2025-07-08 08:32:02.1118|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/openapi/v1.json - - -|duration: 0.251
2025-07-08 08:32:02.1118|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /openapi/{documentName}.json'|duration: 1.623
2025-07-08 08:32:02.3674|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /openapi/{documentName}.json'|duration: 255.825
2025-07-08 08:32:02.3674|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/openapi/v1.json - 200 - application/json;charset=utf-8 260.9613ms|duration: 261.127
2025-07-08 08:32:05.7142|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 OPTIONS http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - - -|duration: 0.239
2025-07-08 08:32:05.7142|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 1.864
2025-07-08 08:32:05.7142|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 OPTIONS http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - 204 - - 2.8462ms|duration: 2.978
2025-07-08 08:32:05.7142|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - application/json -|duration: 0.091
2025-07-08 08:32:05.8046|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 85.363
2025-07-08 08:32:05.8046|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 85.923
2025-07-08 08:32:05.8245|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Route matched with {action = "GetAll", controller = "Areas"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[BitacoraResiduosESH.Backend.Backend.Application.Common.PagedResponse`1[BitacoraResiduosESH.Backend.Backend.Application.DTOs.Area.AreaDto]]] GetAll(Int32, Int32, Boolean) on controller BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController (BitacoraResiduosESH.Backend.Backend.WebAPI).|duration: 114.338
2025-07-08 08:32:05.8981|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executing action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI) - Validation state: Valid|duration: 190.445
2025-07-08 08:32:05.9965|ERROR|Microsoft.EntityFrameworkCore.Database.Command|Failed executing DbCommand (3ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT COUNT(*)
FROM "Areas" AS "a"
WHERE NOT ("a"."IsDeleted")|duration: 288.760
2025-07-08 08:32:06.0080|ERROR|Microsoft.EntityFrameworkCore.Query|An exception occurred while iterating over the results of a query for context type 'BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext'.
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such table: Areas'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()|duration: ************-07-08 08:32:06.0080|ERROR|BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5|Error al obtener todos los "Area"|duration: ************-07-08 08:32:06.0350|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 119.4566ms.|duration: ************-07-08 08:32:06.0381|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor|Executing BadRequestObjectResult, writing value of type 'System.String'.|duration: ************-07-08 08:32:06.0542|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI) in 217.473ms|duration: 337.387
2025-07-08 08:32:06.0542|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 337.686
2025-07-08 08:32:06.0542|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - 400 - application/json;+charset=utf-8 341.1407ms|duration: 341.263
2025-07-08 08:32:07.1717|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - application/json -|duration: 0.815
2025-07-08 08:32:07.1791|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 7.922
2025-07-08 08:32:07.1791|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 8.945
2025-07-08 08:32:07.1791|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Route matched with {action = "GetAll", controller = "Areas"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[BitacoraResiduosESH.Backend.Backend.Application.Common.PagedResponse`1[BitacoraResiduosESH.Backend.Backend.Application.DTOs.Area.AreaDto]]] GetAll(Int32, Int32, Boolean) on controller BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController (BitacoraResiduosESH.Backend.Backend.WebAPI).|duration: 9.821
2025-07-08 08:32:07.1945|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executing action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI) - Validation state: Valid|duration: 28.476
2025-07-08 08:32:07.2121|ERROR|Microsoft.EntityFrameworkCore.Database.Command|Failed executing DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT COUNT(*)
FROM "Areas" AS "a"
WHERE NOT ("a"."IsDeleted")|duration: 48.246
2025-07-08 08:32:07.2121|ERROR|Microsoft.EntityFrameworkCore.Query|An exception occurred while iterating over the results of a query for context type 'BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext'.
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such table: Areas'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()|duration: 63.505
2025-07-08 08:32:07.2356|ERROR|BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5|Error al obtener todos los "Area"|duration: 74.325
2025-07-08 08:32:07.2489|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 48.1699ms.|duration: 77.361
2025-07-08 08:32:07.2489|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor|Executing BadRequestObjectResult, writing value of type 'System.String'.|duration: 81.030
2025-07-08 08:32:07.2489|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI) in 68.9289ms|duration: 82.156
2025-07-08 08:32:07.2489|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 82.598
2025-07-08 08:32:07.2566|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - 400 - application/json;+charset=utf-8 84.9033ms|duration: 85.167
2025-07-08 08:32:09.2656|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - application/json -|duration: 0.754
2025-07-08 08:32:09.2656|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 6.434
2025-07-08 08:32:09.2724|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 7.386
2025-07-08 08:32:09.2724|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Route matched with {action = "GetAll", controller = "Areas"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[BitacoraResiduosESH.Backend.Backend.Application.Common.PagedResponse`1[BitacoraResiduosESH.Backend.Backend.Application.DTOs.Area.AreaDto]]] GetAll(Int32, Int32, Boolean) on controller BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController (BitacoraResiduosESH.Backend.Backend.WebAPI).|duration: 8.099
2025-07-08 08:32:09.2885|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executing action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI) - Validation state: Valid|duration: 30.455
2025-07-08 08:32:09.3043|ERROR|Microsoft.EntityFrameworkCore.Database.Command|Failed executing DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT COUNT(*)
FROM "Areas" AS "a"
WHERE NOT ("a"."IsDeleted")|duration: 41.181
2025-07-08 08:32:09.3043|ERROR|Microsoft.EntityFrameworkCore.Query|An exception occurred while iterating over the results of a query for context type 'BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext'.
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such table: Areas'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()|duration: 53.885
2025-07-08 08:32:09.3197|ERROR|BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5|Error al obtener todos los "Area"|duration: 63.451
2025-07-08 08:32:09.3197|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 34.6737ms.|duration: 66.052
2025-07-08 08:32:09.3352|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor|Executing BadRequestObjectResult, writing value of type 'System.String'.|duration: 69.803
2025-07-08 08:32:09.3352|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI) in 58.8879ms|duration: 70.871
2025-07-08 08:32:09.3352|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 71.360
2025-07-08 08:32:09.3352|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - 400 - application/json;+charset=utf-8 73.9631ms|duration: 74.208
2025-07-08 08:32:17.7472|INFO|Microsoft.Hosting.Lifetime|Application is shutting down...|duration: 
2025-07-08 08:32:27.7372|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (19ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
PRAGMA journal_mode = 'wal';|duration: 
2025-07-08 08:32:27.8394|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE TABLE "Areas" (
    "Id" INTEGER NOT NULL CONSTRAINT "AreasPK" PRIMARY KEY AUTOINCREMENT,
    "Created" datetime2(3) NOT NULL DEFAULT (getutcdate()),
    "CreatedBy" nvarchar(50) NOT NULL DEFAULT (suser_name()),
    "IsDeleted" bit NOT NULL DEFAULT 0,
    "Deleted" datetime2(3) NULL,
    "DeletedBy" nvarchar(50) NULL,
    "Updated" datetime2(3) NULL,
    "UpdatedBy" nvarchar(50) NULL,
    "Active" bit NOT NULL DEFAULT 1,
    "Name" nvarchar(200) NOT NULL,
    "Description" nvarchar(1000) NULL
);|duration: 
2025-07-08 08:32:27.8394|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE TABLE "ContainerTypes" (
    "Id" INTEGER NOT NULL CONSTRAINT "ContainerTypesPK" PRIMARY KEY AUTOINCREMENT,
    "Created" datetime2(3) NOT NULL DEFAULT (getutcdate()),
    "CreatedBy" nvarchar(50) NOT NULL DEFAULT (suser_name()),
    "IsDeleted" bit NOT NULL DEFAULT 0,
    "Deleted" datetime2(3) NULL,
    "DeletedBy" nvarchar(50) NULL,
    "Updated" datetime2(3) NULL,
    "UpdatedBy" nvarchar(50) NULL,
    "Active" bit NOT NULL DEFAULT 1,
    "Name" nvarchar(200) NOT NULL,
    "Description" nvarchar(1000) NULL
);|duration: 
2025-07-08 08:32:27.8509|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE TABLE "Role" (
    "Id" INTEGER NOT NULL CONSTRAINT "RolePK" PRIMARY KEY AUTOINCREMENT,
    "Created" datetime2(3) NOT NULL DEFAULT (getutcdate()),
    "CreatedBy" nvarchar(50) NOT NULL DEFAULT (suser_name()),
    "IsDeleted" bit NOT NULL DEFAULT 0,
    "Deleted" datetime2(3) NULL,
    "DeletedBy" nvarchar(50) NULL,
    "Updated" datetime2(3) NULL,
    "UpdatedBy" nvarchar(50) NULL,
    "Active" bit NOT NULL DEFAULT 1,
    "Name" nvarchar(200) NOT NULL,
    "Description" nvarchar(1000) NULL
);|duration: 
2025-07-08 08:32:27.8509|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE TABLE "User" (
    "Id" INTEGER NOT NULL CONSTRAINT "UserPK" PRIMARY KEY AUTOINCREMENT,
    "Name" nvarchar(200) NOT NULL,
    "EmployeeNumber" nvarchar(30) NOT NULL,
    "Email" nvarchar(255) NULL,
    "Password" nvarchar(255) NOT NULL,
    "RoleId" INTEGER NOT NULL,
    "Created" datetime2(3) NOT NULL DEFAULT (getutcdate()),
    "CreatedBy" nvarchar(50) NOT NULL DEFAULT (suser_name()),
    "IsDeleted" bit NOT NULL DEFAULT 0,
    "Deleted" datetime2(3) NULL,
    "DeletedBy" nvarchar(50) NULL,
    "Updated" datetime2(3) NULL,
    "UpdatedBy" nvarchar(50) NULL,
    "Active" bit NOT NULL DEFAULT 1,
    CONSTRAINT "FK_User_RoleId_Role_Id" FOREIGN KEY ("RoleId") REFERENCES "Role" ("Id") ON DELETE RESTRICT
);|duration: 
2025-07-08 08:32:27.8509|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Areas_Active" ON "Areas" ("Active");|duration: 
2025-07-08 08:32:27.8509|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Areas_Active_IsDeleted" ON "Areas" ("Active", "IsDeleted");|duration: 
2025-07-08 08:32:27.8509|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Areas_Created" ON "Areas" ("Created");|duration: 
2025-07-08 08:32:27.8509|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Areas_IsDeleted" ON "Areas" ("IsDeleted");|duration: 
2025-07-08 08:32:27.8509|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Areas_Name_Active" ON "Areas" ("Name", "Active");|duration: 
2025-07-08 08:32:27.8509|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Areas_Name_Created" ON "Areas" ("Name", "Created");|duration: 
2025-07-08 08:32:27.8509|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE UNIQUE INDEX "UQ_Areas_Name_NotDeleted" ON "Areas" ("Name") WHERE [IsDeleted] = 0;|duration: 
2025-07-08 08:32:27.8509|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_ContainerTypes_Active" ON "ContainerTypes" ("Active");|duration: 
2025-07-08 08:32:27.8509|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_ContainerTypes_Active_IsDeleted" ON "ContainerTypes" ("Active", "IsDeleted");|duration: 
2025-07-08 08:32:27.8509|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_ContainerTypes_Created" ON "ContainerTypes" ("Created");|duration: 
2025-07-08 08:32:27.8673|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_ContainerTypes_IsDeleted" ON "ContainerTypes" ("IsDeleted");|duration: 
2025-07-08 08:32:27.8673|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_ContainerTypes_Name_Active" ON "ContainerTypes" ("Name", "Active");|duration: 
2025-07-08 08:32:27.8673|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_ContainerTypes_Name_Created" ON "ContainerTypes" ("Name", "Created");|duration: 
2025-07-08 08:32:27.8673|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE UNIQUE INDEX "UQ_ContainerTypes_Name_NotDeleted" ON "ContainerTypes" ("Name") WHERE [IsDeleted] = 0;|duration: 
2025-07-08 08:32:27.8673|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Role_Active" ON "Role" ("Active");|duration: 
2025-07-08 08:32:27.8673|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Role_Active_IsDeleted" ON "Role" ("Active", "IsDeleted");|duration: 
2025-07-08 08:32:27.8673|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Role_Created" ON "Role" ("Created");|duration: 
2025-07-08 08:32:27.8673|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Role_IsDeleted" ON "Role" ("IsDeleted");|duration: 
2025-07-08 08:32:27.8673|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Role_Name_Active" ON "Role" ("Name", "Active");|duration: 
2025-07-08 08:32:27.8673|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Role_Name_Created" ON "Role" ("Name", "Created");|duration: 
2025-07-08 08:32:27.8816|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE UNIQUE INDEX "UQ_Role_Name_NotDeleted" ON "Role" ("Name") WHERE [IsDeleted] = 0;|duration: 
2025-07-08 08:32:27.8816|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_Active" ON "User" ("Active");|duration: 
2025-07-08 08:32:27.8816|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_Active_IsDeleted" ON "User" ("Active", "IsDeleted");|duration: 
2025-07-08 08:32:27.8816|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_Created" ON "User" ("Created");|duration: 
2025-07-08 08:32:27.8816|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_Email_Active" ON "User" ("Email", "Active");|duration: 
2025-07-08 08:32:27.8816|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_IsDeleted" ON "User" ("IsDeleted");|duration: 
2025-07-08 08:32:27.8816|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_Name" ON "User" ("Name");|duration: 
2025-07-08 08:32:27.8816|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_Name_Created" ON "User" ("Name", "Created");|duration: 
2025-07-08 08:32:27.8816|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_RoleId" ON "User" ("RoleId");|duration: 
2025-07-08 08:32:27.8816|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_RoleId_Active" ON "User" ("RoleId", "Active");|duration: 
2025-07-08 08:32:27.8816|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE UNIQUE INDEX "UQ_User_Email_NotDeleted" ON "User" ("Email") WHERE [IsDeleted] = 0;|duration: 
2025-07-08 08:32:27.8816|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE UNIQUE INDEX "UQ_User_EmployeeNumber_NotDeleted" ON "User" ("EmployeeNumber") WHERE [IsDeleted] = 0;|duration: 
2025-07-08 08:32:27.8988|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Program|Base de datos inicializada correctamente|duration: 
2025-07-08 08:32:27.9135|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|Inicializando base de datos con datos de prueba...|duration: 
2025-07-08 08:32:28.1028|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (3ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM "Role" AS "r")|duration: 
2025-07-08 08:32:28.2604|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (4ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (Size = 6), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (Size = 45), @p5='?' (Size = 5), @p6='?' (DbType = DateTime), @p7='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "Role" ("Created", "CreatedBy", "Deleted", "DeletedBy", "Description", "Name", "Updated", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 08:32:28.2731|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|Rol Admin creado exitosamente|duration: 
2025-07-08 08:32:28.6043|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (2ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (Size = 6), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (Size = 27), @p5='?' (Size = 6), @p6='?' (Size = 17), @p7='?' (Size = 60), @p8='?' (DbType = Int32), @p9='?' (DbType = DateTime), @p10='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "User" ("Created", "CreatedBy", "Deleted", "DeletedBy", "Email", "EmployeeNumber", "Name", "Password", "RoleId", "Updated", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 08:32:28.6176|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|Usuario administrador creado exitosamente|duration: 
2025-07-08 08:32:28.6176|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|Inicialización de base de datos completada exitosamente|duration: 
2025-07-08 08:32:28.6176|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Program|Datos de prueba inicializados correctamente|duration: 
2025-07-08 08:32:28.7126|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Program|Aplicación iniciada correctamente|duration: 
2025-07-08 08:32:28.7622|INFO|Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager|User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.|duration: 
2025-07-08 08:32:28.8199|WARN|Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware|The WebRootPath was not found: C:\Repositories\WebProjects\BitacoraResiduosESH\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend.Backend.WebAPI\wwwroot. Static files may be unavailable.|duration: 
2025-07-08 08:32:28.8835|INFO|Microsoft.Hosting.Lifetime|Now listening on: http://localhost:5001|duration: 
2025-07-08 08:32:28.8835|INFO|Microsoft.Hosting.Lifetime|Application started. Press Ctrl+C to shut down.|duration: 
2025-07-08 08:32:28.8835|INFO|Microsoft.Hosting.Lifetime|Hosting environment: Development|duration: 
2025-07-08 08:32:28.8835|INFO|Microsoft.Hosting.Lifetime|Content root path: C:\Repositories\WebProjects\BitacoraResiduosESH\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend.Backend.WebAPI|duration: 
2025-07-08 08:32:29.4766|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar - - -|duration: 3.073
2025-07-08 08:32:29.5297|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 57.842
2025-07-08 08:32:29.5380|INFO|Microsoft.AspNetCore.Http.Result.RedirectResult|Executing RedirectResult, redirecting to scalar/.|duration: 66.860
2025-07-08 08:32:29.5380|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 67.237
2025-07-08 08:32:29.5380|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar - 302 0 - 69.4619ms|duration: 72.629
2025-07-08 08:32:29.5380|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar/ - - -|duration: 0.158
2025-07-08 08:32:29.5380|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 2.688
2025-07-08 08:32:29.5878|INFO|Microsoft.AspNetCore.Http.Result.ContentResult|Write content with HTTP Response ContentType of text/html|duration: 45.632
2025-07-08 08:32:29.5878|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 46.421
2025-07-08 08:32:29.5878|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar/ - 200 924 text/html 47.0054ms|duration: 47.247
2025-07-08 08:32:29.6065|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar/scalar.js - - -|duration: 0.148
2025-07-08 08:32:29.6065|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar/scalar.aspnetcore.js - - -|duration: 0.162
2025-07-08 08:32:29.6065|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/scalar.js'|duration: 3.085
2025-07-08 08:32:29.6065|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 3.664
2025-07-08 08:32:29.6065|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/scalar.aspnetcore.js'|duration: 4.683
2025-07-08 08:32:29.6164|INFO|Microsoft.AspNetCore.Http.Result.StatusCodeResult|Setting HTTP status code 304.|duration: 12.905
2025-07-08 08:32:29.6164|INFO|Microsoft.AspNetCore.Http.Result.StatusCodeResult|Setting HTTP status code 304.|duration: 13.265
2025-07-08 08:32:29.6164|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/scalar.aspnetcore.js'|duration: 13.170
2025-07-08 08:32:29.6164|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/scalar.js'|duration: 13.573
2025-07-08 08:32:29.6164|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar/scalar.js - 304 - - 13.8394ms|duration: 14.004
2025-07-08 08:32:29.6164|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar/scalar.aspnetcore.js - 304 - - 14.2005ms|duration: 14.296
2025-07-08 08:32:29.8027|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/openapi/v1.json - - -|duration: 0.124
2025-07-08 08:32:29.8043|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /openapi/{documentName}.json'|duration: 1.954
2025-07-08 08:32:30.0757|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /openapi/{documentName}.json'|duration: 273.090
2025-07-08 08:32:30.0757|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/openapi/v1.json - 200 - application/json;charset=utf-8 275.6673ms|duration: 275.851
2025-07-08 08:32:31.6012|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 OPTIONS http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - - -|duration: 0.216
2025-07-08 08:32:31.6012|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 2.076
2025-07-08 08:32:31.6012|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 OPTIONS http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - 204 - - 3.8207ms|duration: 4.032
2025-07-08 08:32:31.6012|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - application/json -|duration: 0.166
2025-07-08 08:32:31.6517|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 50.772
2025-07-08 08:32:31.6517|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 51.515
2025-07-08 08:32:31.6789|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Route matched with {action = "GetAll", controller = "Areas"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[BitacoraResiduosESH.Backend.Backend.Application.Common.PagedResponse`1[BitacoraResiduosESH.Backend.Backend.Application.DTOs.Area.AreaDto]]] GetAll(Int32, Int32, Boolean) on controller BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController (BitacoraResiduosESH.Backend.Backend.WebAPI).|duration: 76.357
2025-07-08 08:32:31.7288|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executing action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI) - Validation state: Valid|duration: 131.598
2025-07-08 08:32:31.7912|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT COUNT(*)
FROM "Areas" AS "a"
WHERE NOT ("a"."IsDeleted")|duration: 192.111
2025-07-08 08:32:31.8359|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
SELECT "a"."Id", "a"."Active", "a"."Created", "a"."CreatedBy", "a"."Deleted", "a"."DeletedBy", "a"."Description", "a"."IsDeleted", "a"."Name", "a"."Updated", "a"."UpdatedBy"
FROM "Areas" AS "a"
WHERE NOT ("a"."IsDeleted")
ORDER BY "a"."Id"
LIMIT @__p_1 OFFSET @__p_0|duration: 240.496
2025-07-08 08:32:31.8510|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 110.9812ms.|duration: 247.283
2025-07-08 08:32:31.8660|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor|Executing OkObjectResult, writing value of type 'BitacoraResiduosESH.Backend.Backend.Application.Common.PagedResponse`1[[BitacoraResiduosESH.Backend.Backend.Application.DTOs.Area.AreaDto, BitacoraResiduosESH.Backend.Backend.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]'.|duration: 267.452
2025-07-08 08:32:31.8837|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI) in 193.7461ms|duration: 275.527
2025-07-08 08:32:31.8837|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 275.792
2025-07-08 08:32:31.8837|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - 200 - application/json;+charset=utf-8 281.1515ms|duration: 281.470
2025-07-08 08:32:40.2062|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 OPTIONS http://localhost:5001/api/areas - - -|duration: 0.924
2025-07-08 08:32:40.2062|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 4.188
2025-07-08 08:32:40.2107|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 OPTIONS http://localhost:5001/api/areas - 204 - - 5.3224ms|duration: 5.585
2025-07-08 08:32:40.2107|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 POST http://localhost:5001/api/areas - application/json 36|duration: 0.278
2025-07-08 08:32:40.2107|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 6.244
2025-07-08 08:32:40.2107|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.Create (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 7.049
2025-07-08 08:32:40.2412|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Route matched with {action = "Create", controller = "Areas"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[BitacoraResiduosESH.Backend.Backend.Application.DTOs.Area.AreaDto]] Create(BitacoraResiduosESH.Backend.Backend.Application.DTOs.Area.CreateAreaDto) on controller BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController (BitacoraResiduosESH.Backend.Backend.WebAPI).|duration: 31.495
2025-07-08 08:32:40.2979|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executing action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.Create (BitacoraResiduosESH.Backend.Backend.WebAPI) - Validation state: Valid|duration: 86.550
2025-07-08 08:32:40.4613|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[@__ToLower_0='?' (Size = 4)], CommandType='Text', CommandTimeout='30']
SELECT COUNT(*)
FROM "Areas" AS "a"
WHERE NOT ("a"."IsDeleted") AND instr(lower("a"."Name"), @__ToLower_0) > 0|duration: 261.797
2025-07-08 08:32:40.5232|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (2ms) [Parameters=[@__ToLower_0='?' (Size = 4), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
SELECT "a"."Id", "a"."Active", "a"."Created", "a"."CreatedBy", "a"."Deleted", "a"."DeletedBy", "a"."Description", "a"."IsDeleted", "a"."Name", "a"."Updated", "a"."UpdatedBy"
FROM "Areas" AS "a"
WHERE NOT ("a"."IsDeleted") AND instr(lower("a"."Name"), @__ToLower_0) > 0
ORDER BY "a"."Id"
LIMIT @__p_2 OFFSET @__p_1|duration: 315.601
2025-07-08 08:32:40.6508|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (13ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (Size = 17), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (Size = 4), @p5='?' (Size = 4), @p6='?' (DbType = DateTime), @p7='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "Areas" ("Created", "CreatedBy", "Deleted", "DeletedBy", "Description", "Name", "Updated", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING "Id", "Active", "IsDeleted";|duration: 440.238
2025-07-08 08:32:40.6643|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Repositories.GenericRepository`1|Entidad "Area" agregada exitosamente con ID: 1|duration: 456.158
2025-07-08 08:32:40.6813|INFO|BitacoraResiduosESH.Backend.Backend.Application.Services.SimpleEntityService`5|"Area" creada exitosamente con ID: 1 por usuario: "Maximiliano Ponce"|duration: 468.143
2025-07-08 08:32:40.6813|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5|"Area" creado exitosamente con ID: 1 por usuario: "Maximiliano Ponce"|duration: 468.263
2025-07-08 08:32:40.6813|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.Create (BitacoraResiduosESH.Backend.Backend.WebAPI), returned result Microsoft.AspNetCore.Mvc.CreatedAtActionResult in 382.0824ms.|duration: 469.565
2025-07-08 08:32:40.6813|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor|Executing CreatedAtActionResult, writing value of type 'BitacoraResiduosESH.Backend.Backend.Application.DTOs.Area.AreaDto'.|duration: 474.472
2025-07-08 08:32:40.7650|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.Create (BitacoraResiduosESH.Backend.Backend.WebAPI) in 517.7157ms|duration: 552.234
2025-07-08 08:32:40.7650|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.Create (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 552.785
2025-07-08 08:32:40.7650|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 POST http://localhost:5001/api/areas - 201 - application/json;+charset=utf-8 554.5788ms|duration: 554.851
2025-07-08 08:32:40.7650|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 OPTIONS http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - - -|duration: 0.238
2025-07-08 08:32:40.7726|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 2.154
2025-07-08 08:32:40.7726|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 OPTIONS http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - 204 - - 3.2822ms|duration: 3.500
2025-07-08 08:32:40.7726|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - application/json -|duration: 0.126
2025-07-08 08:32:40.7726|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 4.362
2025-07-08 08:32:40.7726|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 5.348
2025-07-08 08:32:40.7726|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Route matched with {action = "GetAll", controller = "Areas"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[BitacoraResiduosESH.Backend.Backend.Application.Common.PagedResponse`1[BitacoraResiduosESH.Backend.Backend.Application.DTOs.Area.AreaDto]]] GetAll(Int32, Int32, Boolean) on controller BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController (BitacoraResiduosESH.Backend.Backend.WebAPI).|duration: 6.121
2025-07-08 08:32:40.7928|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executing action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI) - Validation state: Valid|duration: 27.977
2025-07-08 08:32:40.8045|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT COUNT(*)
FROM "Areas" AS "a"
WHERE NOT ("a"."IsDeleted")|duration: 41.619
2025-07-08 08:32:40.8194|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (2ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
SELECT "a"."Id", "a"."Active", "a"."Created", "a"."CreatedBy", "a"."Deleted", "a"."DeletedBy", "a"."Description", "a"."IsDeleted", "a"."Name", "a"."Updated", "a"."UpdatedBy"
FROM "Areas" AS "a"
WHERE NOT ("a"."IsDeleted")
ORDER BY "a"."Id"
LIMIT @__p_1 OFFSET @__p_0|duration: 51.348
2025-07-08 08:32:40.8389|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 40.8691ms.|duration: 69.783
2025-07-08 08:32:40.8389|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor|Executing OkObjectResult, writing value of type 'BitacoraResiduosESH.Backend.Backend.Application.Common.PagedResponse`1[[BitacoraResiduosESH.Backend.Backend.Application.DTOs.Area.AreaDto, BitacoraResiduosESH.Backend.Backend.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]'.|duration: 73.589
2025-07-08 08:32:40.8507|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI) in 65.5616ms|duration: 75.452
2025-07-08 08:32:40.8507|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 76.043
2025-07-08 08:32:40.8507|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - 200 - application/json;+charset=utf-8 78.1387ms|duration: 78.346
2025-07-08 08:32:42.4084|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 OPTIONS http://localhost:5001/api/areas/1/deactivate - - -|duration: 0.481
2025-07-08 08:32:42.4084|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 3.164
2025-07-08 08:32:42.4084|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 OPTIONS http://localhost:5001/api/areas/1/deactivate - 204 - - 4.1643ms|duration: 4.426
2025-07-08 08:32:42.4137|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 PATCH http://localhost:5001/api/areas/1/deactivate - application/json -|duration: 0.175
2025-07-08 08:32:42.4137|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 8.087
2025-07-08 08:32:42.4137|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.Deactivate (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 9.698
2025-07-08 08:32:42.4306|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Route matched with {action = "Deactivate", controller = "Areas"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Deactivate(Int32) on controller BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController (BitacoraResiduosESH.Backend.Backend.WebAPI).|duration: 17.161
2025-07-08 08:32:42.4306|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executing action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.Deactivate (BitacoraResiduosESH.Backend.Backend.WebAPI) - Validation state: Valid|duration: 27.108
2025-07-08 08:32:42.5135|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
SELECT "a"."Id", "a"."Active", "a"."Created", "a"."CreatedBy", "a"."Deleted", "a"."DeletedBy", "a"."Description", "a"."IsDeleted", "a"."Name", "a"."Updated", "a"."UpdatedBy"
FROM "Areas" AS "a"
WHERE "a"."Id" = @__id_0
LIMIT 1|duration: 106.430
2025-07-08 08:32:42.5545|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (2ms) [Parameters=[@p10='?' (DbType = Int32), @p0='?' (DbType = Boolean), @p1='?' (DbType = DateTime), @p2='?' (Size = 17), @p3='?' (DbType = DateTime), @p4='?', @p5='?' (Size = 4), @p6='?' (DbType = Boolean), @p7='?' (Size = 4), @p8='?' (DbType = DateTime), @p9='?' (Size = 17)], CommandType='Text', CommandTimeout='30']
UPDATE "Areas" SET "Active" = @p0, "Created" = @p1, "CreatedBy" = @p2, "Deleted" = @p3, "DeletedBy" = @p4, "Description" = @p5, "IsDeleted" = @p6, "Name" = @p7, "Updated" = @p8, "UpdatedBy" = @p9
WHERE "Id" = @p10
RETURNING 1;|duration: 147.777
2025-07-08 08:32:42.5713|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Repositories.GenericRepository`1|Entidad "Area" con ID 1 desactivada exitosamente por usuario: "Maximiliano Ponce"|duration: 164.456
2025-07-08 08:32:42.5713|INFO|BitacoraResiduosESH.Backend.Backend.Application.Services.SimpleEntityService`5|"Area" desactivada exitosamente con ID: 1 por usuario: "Maximiliano Ponce"|duration: 164.561
2025-07-08 08:32:42.5713|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5|"Area" con ID 1 desactivado exitosamente por usuario: "Maximiliano Ponce"|duration: 164.656
2025-07-08 08:32:42.5713|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.Deactivate (BitacoraResiduosESH.Backend.Backend.WebAPI), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 137.9051ms.|duration: 165.502
2025-07-08 08:32:42.5713|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor|Executing OkObjectResult, writing value of type '<>f__AnonymousType1`1[[System.String, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.|duration: 168.873
2025-07-08 08:32:42.5871|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.Deactivate (BitacoraResiduosESH.Backend.Backend.WebAPI) in 153.9881ms|duration: 173.984
2025-07-08 08:32:42.5871|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.Deactivate (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 174.397
2025-07-08 08:32:42.5871|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 PATCH http://localhost:5001/api/areas/1/deactivate - 200 - application/json;+charset=utf-8 176.5962ms|duration: 176.845
2025-07-08 08:32:42.5871|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - application/json -|duration: 0.205
2025-07-08 08:32:42.5871|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 4.529
2025-07-08 08:32:42.5871|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 5.407
2025-07-08 08:32:42.5871|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Route matched with {action = "GetAll", controller = "Areas"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[BitacoraResiduosESH.Backend.Backend.Application.Common.PagedResponse`1[BitacoraResiduosESH.Backend.Backend.Application.DTOs.Area.AreaDto]]] GetAll(Int32, Int32, Boolean) on controller BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController (BitacoraResiduosESH.Backend.Backend.WebAPI).|duration: 6.036
2025-07-08 08:32:42.6019|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executing action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI) - Validation state: Valid|duration: 19.828
2025-07-08 08:32:42.6162|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT COUNT(*)
FROM "Areas" AS "a"
WHERE NOT ("a"."IsDeleted")|duration: 25.463
2025-07-08 08:32:42.6162|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (2ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
SELECT "a"."Id", "a"."Active", "a"."Created", "a"."CreatedBy", "a"."Deleted", "a"."DeletedBy", "a"."Description", "a"."IsDeleted", "a"."Name", "a"."Updated", "a"."UpdatedBy"
FROM "Areas" AS "a"
WHERE NOT ("a"."IsDeleted")
ORDER BY "a"."Id"
LIMIT @__p_1 OFFSET @__p_0|duration: 38.101
2025-07-08 08:32:42.6326|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 22.8863ms.|duration: 43.342
2025-07-08 08:32:42.6326|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor|Executing OkObjectResult, writing value of type 'BitacoraResiduosESH.Backend.Backend.Application.Common.PagedResponse`1[[BitacoraResiduosESH.Backend.Backend.Application.DTOs.Area.AreaDto, BitacoraResiduosESH.Backend.Backend.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]'.|duration: 55.056
2025-07-08 08:32:42.6488|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI) in 48.7746ms|duration: 58.033
2025-07-08 08:32:42.6488|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 58.428
2025-07-08 08:32:42.6488|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - 200 - application/json;+charset=utf-8 60.1200ms|duration: 60.380
2025-07-08 08:32:43.0676|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 OPTIONS http://localhost:5001/api/areas/1/activate - - -|duration: 0.178
2025-07-08 08:32:43.0691|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 2.373
2025-07-08 08:32:43.0691|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 OPTIONS http://localhost:5001/api/areas/1/activate - 204 - - 3.5199ms|duration: 3.720
2025-07-08 08:32:43.0691|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 PATCH http://localhost:5001/api/areas/1/activate - application/json -|duration: 0.206
2025-07-08 08:32:43.0691|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 4.050
2025-07-08 08:32:43.0691|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.Activate (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 4.893
2025-07-08 08:32:43.0691|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Route matched with {action = "Activate", controller = "Areas"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Activate(Int32) on controller BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController (BitacoraResiduosESH.Backend.Backend.WebAPI).|duration: 12.169
2025-07-08 08:32:43.0851|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executing action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.Activate (BitacoraResiduosESH.Backend.Backend.WebAPI) - Validation state: Valid|duration: 21.702
2025-07-08 08:32:43.1022|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
SELECT "a"."Id", "a"."Active", "a"."Created", "a"."CreatedBy", "a"."Deleted", "a"."DeletedBy", "a"."Description", "a"."IsDeleted", "a"."Name", "a"."Updated", "a"."UpdatedBy"
FROM "Areas" AS "a"
WHERE "a"."Id" = @__id_0
LIMIT 1|duration: 41.023
2025-07-08 08:32:43.1167|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (2ms) [Parameters=[@p10='?' (DbType = Int32), @p0='?' (DbType = Boolean), @p1='?' (DbType = DateTime), @p2='?' (Size = 17), @p3='?' (DbType = DateTime), @p4='?', @p5='?' (Size = 4), @p6='?' (DbType = Boolean), @p7='?' (Size = 4), @p8='?' (DbType = DateTime), @p9='?' (Size = 17)], CommandType='Text', CommandTimeout='30']
UPDATE "Areas" SET "Active" = @p0, "Created" = @p1, "CreatedBy" = @p2, "Deleted" = @p3, "DeletedBy" = @p4, "Description" = @p5, "IsDeleted" = @p6, "Name" = @p7, "Updated" = @p8, "UpdatedBy" = @p9
WHERE "Id" = @p10
RETURNING 1;|duration: 55.942
2025-07-08 08:32:43.1326|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Repositories.GenericRepository`1|Entidad "Area" con ID 1 activada exitosamente por usuario: "Maximiliano Ponce"|duration: 63.689
2025-07-08 08:32:43.1326|INFO|BitacoraResiduosESH.Backend.Backend.Application.Services.SimpleEntityService`5|"Area" activada exitosamente con ID: 1 por usuario: "Maximiliano Ponce"|duration: 63.804
2025-07-08 08:32:43.1326|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5|"Area" con ID 1 activado exitosamente por usuario: "Maximiliano Ponce"|duration: 63.914
2025-07-08 08:32:43.1326|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.Activate (BitacoraResiduosESH.Backend.Backend.WebAPI), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 41.7174ms.|duration: 64.153
2025-07-08 08:32:43.1326|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor|Executing OkObjectResult, writing value of type '<>f__AnonymousType1`1[[System.String, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.|duration: 68.259
2025-07-08 08:32:43.1326|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.Activate (BitacoraResiduosESH.Backend.Backend.WebAPI) in 54.0531ms|duration: 69.672
2025-07-08 08:32:43.1326|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.Activate (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 70.153
2025-07-08 08:32:43.1326|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 PATCH http://localhost:5001/api/areas/1/activate - 200 - application/json;+charset=utf-8 72.0601ms|duration: 72.306
2025-07-08 08:32:43.1326|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - application/json -|duration: 0.156
2025-07-08 08:32:43.1483|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 3.296
2025-07-08 08:32:43.1483|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 4.350
2025-07-08 08:32:43.1483|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Route matched with {action = "GetAll", controller = "Areas"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[BitacoraResiduosESH.Backend.Backend.Application.Common.PagedResponse`1[BitacoraResiduosESH.Backend.Backend.Application.DTOs.Area.AreaDto]]] GetAll(Int32, Int32, Boolean) on controller BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController (BitacoraResiduosESH.Backend.Backend.WebAPI).|duration: 4.884
2025-07-08 08:32:43.1483|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executing action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI) - Validation state: Valid|duration: 17.686
2025-07-08 08:32:43.1637|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT COUNT(*)
FROM "Areas" AS "a"
WHERE NOT ("a"."IsDeleted")|duration: 23.330
2025-07-08 08:32:43.1637|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
SELECT "a"."Id", "a"."Active", "a"."Created", "a"."CreatedBy", "a"."Deleted", "a"."DeletedBy", "a"."Description", "a"."IsDeleted", "a"."Name", "a"."Updated", "a"."UpdatedBy"
FROM "Areas" AS "a"
WHERE NOT ("a"."IsDeleted")
ORDER BY "a"."Id"
LIMIT @__p_1 OFFSET @__p_0|duration: 30.117
2025-07-08 08:32:43.1807|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 18.2373ms.|duration: 36.779
2025-07-08 08:32:43.1807|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor|Executing OkObjectResult, writing value of type 'BitacoraResiduosESH.Backend.Backend.Application.Common.PagedResponse`1[[BitacoraResiduosESH.Backend.Backend.Application.DTOs.Area.AreaDto, BitacoraResiduosESH.Backend.Backend.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]'.|duration: 42.376
2025-07-08 08:32:43.1807|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI) in 35.9807ms|duration: 43.456
2025-07-08 08:32:43.1807|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 43.773
2025-07-08 08:32:43.1807|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - 200 - application/json;+charset=utf-8 45.0153ms|duration: 45.141
2025-07-08 08:40:02.2668|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 OPTIONS http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - - -|duration: 0.224
2025-07-08 08:40:02.2721|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 3.214
2025-07-08 08:40:02.2721|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 OPTIONS http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - 204 - - 4.2748ms|duration: 4.858
2025-07-08 08:40:02.2721|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - application/json -|duration: 0.220
2025-07-08 08:40:02.2721|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 4.258
2025-07-08 08:40:02.2721|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 4.968
2025-07-08 08:40:02.2721|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Route matched with {action = "GetAll", controller = "Areas"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[BitacoraResiduosESH.Backend.Backend.Application.Common.PagedResponse`1[BitacoraResiduosESH.Backend.Backend.Application.DTOs.Area.AreaDto]]] GetAll(Int32, Int32, Boolean) on controller BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController (BitacoraResiduosESH.Backend.Backend.WebAPI).|duration: 5.480
2025-07-08 08:40:02.2891|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executing action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI) - Validation state: Valid|duration: 20.670
2025-07-08 08:40:02.3159|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (12ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT COUNT(*)
FROM "Areas" AS "a"
WHERE NOT ("a"."IsDeleted")|duration: 39.994
2025-07-08 08:40:02.3198|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
SELECT "a"."Id", "a"."Active", "a"."Created", "a"."CreatedBy", "a"."Deleted", "a"."DeletedBy", "a"."Description", "a"."IsDeleted", "a"."Name", "a"."Updated", "a"."UpdatedBy"
FROM "Areas" AS "a"
WHERE NOT ("a"."IsDeleted")
ORDER BY "a"."Id"
LIMIT @__p_1 OFFSET @__p_0|duration: 48.132
2025-07-08 08:40:02.3198|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 30.5277ms.|duration: 51.821
2025-07-08 08:40:02.3198|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor|Executing OkObjectResult, writing value of type 'BitacoraResiduosESH.Backend.Backend.Application.Common.PagedResponse`1[[BitacoraResiduosESH.Backend.Backend.Application.DTOs.Area.AreaDto, BitacoraResiduosESH.Backend.Backend.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]'.|duration: 54.495
2025-07-08 08:40:02.3198|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI) in 46.9128ms|duration: 55.722
2025-07-08 08:40:02.3198|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 56.075
2025-07-08 08:40:02.3198|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - 200 - application/json;+charset=utf-8 57.7081ms|duration: 58.055
2025-07-08 08:40:18.0446|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 OPTIONS http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - - -|duration: 1.109
2025-07-08 08:40:18.0446|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 6.820
2025-07-08 08:40:18.0446|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 OPTIONS http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - 204 - - 8.8069ms|duration: 9.051
2025-07-08 08:40:18.0558|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - application/json -|duration: 0.358
2025-07-08 08:40:18.0558|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 6.115
2025-07-08 08:40:18.0558|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 6.938
2025-07-08 08:40:18.0558|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Route matched with {action = "GetAll", controller = "Areas"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[BitacoraResiduosESH.Backend.Backend.Application.Common.PagedResponse`1[BitacoraResiduosESH.Backend.Backend.Application.DTOs.Area.AreaDto]]] GetAll(Int32, Int32, Boolean) on controller BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController (BitacoraResiduosESH.Backend.Backend.WebAPI).|duration: 7.815
2025-07-08 08:40:18.0691|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executing action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI) - Validation state: Valid|duration: 27.074
2025-07-08 08:40:18.0860|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT COUNT(*)
FROM "Areas" AS "a"
WHERE NOT ("a"."IsDeleted")|duration: 34.113
2025-07-08 08:40:18.0860|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (3ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
SELECT "a"."Id", "a"."Active", "a"."Created", "a"."CreatedBy", "a"."Deleted", "a"."DeletedBy", "a"."Description", "a"."IsDeleted", "a"."Name", "a"."Updated", "a"."UpdatedBy"
FROM "Areas" AS "a"
WHERE NOT ("a"."IsDeleted")
ORDER BY "a"."Id"
LIMIT @__p_1 OFFSET @__p_0|duration: 44.310
2025-07-08 08:40:18.1005|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 20.4382ms.|duration: 48.214
2025-07-08 08:40:18.1005|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor|Executing OkObjectResult, writing value of type 'BitacoraResiduosESH.Backend.Backend.Application.Common.PagedResponse`1[[BitacoraResiduosESH.Backend.Backend.Application.DTOs.Area.AreaDto, BitacoraResiduosESH.Backend.Backend.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]'.|duration: 51.421
2025-07-08 08:40:18.1005|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI) in 39.0697ms|duration: 52.590
2025-07-08 08:40:18.1005|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 53.110
2025-07-08 08:40:18.1005|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - 200 - application/json;+charset=utf-8 55.5034ms|duration: 55.866
2025-07-08 08:40:24.0726|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 OPTIONS http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - - -|duration: 0.620
2025-07-08 08:40:24.0726|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 4.713
2025-07-08 08:40:24.0726|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 OPTIONS http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - 204 - - 6.1601ms|duration: 6.395
2025-07-08 08:40:24.0726|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - application/json -|duration: 0.229
2025-07-08 08:40:24.0854|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 5.689
2025-07-08 08:40:24.0854|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 6.992
2025-07-08 08:40:24.0854|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Route matched with {action = "GetAll", controller = "Areas"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[BitacoraResiduosESH.Backend.Backend.Application.Common.PagedResponse`1[BitacoraResiduosESH.Backend.Backend.Application.DTOs.Area.AreaDto]]] GetAll(Int32, Int32, Boolean) on controller BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController (BitacoraResiduosESH.Backend.Backend.WebAPI).|duration: 7.733
2025-07-08 08:40:24.1007|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executing action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI) - Validation state: Valid|duration: 23.442
2025-07-08 08:40:24.1007|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT COUNT(*)
FROM "Areas" AS "a"
WHERE NOT ("a"."IsDeleted")|duration: 33.270
2025-07-08 08:40:24.1167|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
SELECT "a"."Id", "a"."Active", "a"."Created", "a"."CreatedBy", "a"."Deleted", "a"."DeletedBy", "a"."Description", "a"."IsDeleted", "a"."Name", "a"."Updated", "a"."UpdatedBy"
FROM "Areas" AS "a"
WHERE NOT ("a"."IsDeleted")
ORDER BY "a"."Id"
LIMIT @__p_1 OFFSET @__p_0|duration: 41.623
2025-07-08 08:40:24.1167|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 20.8247ms.|duration: 45.211
2025-07-08 08:40:24.1167|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor|Executing OkObjectResult, writing value of type 'BitacoraResiduosESH.Backend.Backend.Application.Common.PagedResponse`1[[BitacoraResiduosESH.Backend.Backend.Application.DTOs.Area.AreaDto, BitacoraResiduosESH.Backend.Backend.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]'.|duration: 48.524
2025-07-08 08:40:24.1167|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI) in 38.7343ms|duration: 49.799
2025-07-08 08:40:24.1167|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 50.214
2025-07-08 08:40:24.1321|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - 200 - application/json;+charset=utf-8 52.5318ms|duration: 52.836
2025-07-08 08:40:36.0206|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 OPTIONS http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - - -|duration: 0.685
2025-07-08 08:40:36.0229|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 3.775
2025-07-08 08:40:36.0229|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 OPTIONS http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - 204 - - 5.0939ms|duration: 5.497
2025-07-08 08:40:36.0229|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - application/json -|duration: 0.211
2025-07-08 08:40:36.0229|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 4.559
2025-07-08 08:40:36.0229|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 5.210
2025-07-08 08:40:36.0229|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Route matched with {action = "GetAll", controller = "Areas"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[BitacoraResiduosESH.Backend.Backend.Application.Common.PagedResponse`1[BitacoraResiduosESH.Backend.Backend.Application.DTOs.Area.AreaDto]]] GetAll(Int32, Int32, Boolean) on controller BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController (BitacoraResiduosESH.Backend.Backend.WebAPI).|duration: 5.703
2025-07-08 08:40:36.0387|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executing action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI) - Validation state: Valid|duration: 22.442
2025-07-08 08:40:36.0534|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT COUNT(*)
FROM "Areas" AS "a"
WHERE NOT ("a"."IsDeleted")|duration: 30.690
2025-07-08 08:40:36.0534|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
SELECT "a"."Id", "a"."Active", "a"."Created", "a"."CreatedBy", "a"."Deleted", "a"."DeletedBy", "a"."Description", "a"."IsDeleted", "a"."Name", "a"."Updated", "a"."UpdatedBy"
FROM "Areas" AS "a"
WHERE NOT ("a"."IsDeleted")
ORDER BY "a"."Id"
LIMIT @__p_1 OFFSET @__p_0|duration: 40.219
2025-07-08 08:40:36.0694|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 21.6095ms.|duration: 44.692
2025-07-08 08:40:36.0694|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor|Executing OkObjectResult, writing value of type 'BitacoraResiduosESH.Backend.Backend.Application.Common.PagedResponse`1[[BitacoraResiduosESH.Backend.Backend.Application.DTOs.Area.AreaDto, BitacoraResiduosESH.Backend.Backend.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]'.|duration: 48.0
2025-07-08 08:40:36.0694|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI) in 41.0068ms|duration: 49.491
2025-07-08 08:40:36.0694|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 50.057
2025-07-08 08:40:36.0694|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/api/areas?pageNumber=1&pageSize=10&includeDeleted=false - 200 - application/json;+charset=utf-8 52.7475ms|duration: 52.965
2025-07-08 08:53:43.4340|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 OPTIONS http://localhost:5001/api/auth/login - - -|duration: 0.093
2025-07-08 08:53:43.4340|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 2.151
2025-07-08 08:53:43.4340|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 OPTIONS http://localhost:5001/api/auth/login - 204 - - 2.5840ms|duration: 2.654
2025-07-08 08:53:43.4340|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 POST http://localhost:5001/api/auth/login - application/json 47|duration: 0.074
2025-07-08 08:53:43.4340|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 1.024
2025-07-08 08:53:43.4340|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AuthController.Login (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 1.124
2025-07-08 08:53:43.4340|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[BitacoraResiduosESH.Backend.Backend.Application.DTOs.Auth.AuthResponseDto]] Login(BitacoraResiduosESH.Backend.Backend.Application.DTOs.Auth.LoginDto) on controller BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AuthController (BitacoraResiduosESH.Backend.Backend.WebAPI).|duration: 3.191
2025-07-08 08:53:43.4601|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executing action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AuthController.Login (BitacoraResiduosESH.Backend.Backend.WebAPI) - Validation state: Valid|duration: 25.673
2025-07-08 08:53:43.5101|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (2ms) [Parameters=[@__ToLower_0='?' (Size = 6)], CommandType='Text', CommandTimeout='30']
SELECT "u"."Id", "u"."Active", "u"."Created", "u"."CreatedBy", "u"."Deleted", "u"."DeletedBy", "u"."Email", "u"."EmployeeNumber", "u"."IsDeleted", "u"."Name", "u"."Password", "u"."RoleId", "u"."Updated", "u"."UpdatedBy", "r"."Id", "r"."Active", "r"."Created", "r"."CreatedBy", "r"."Deleted", "r"."DeletedBy", "r"."Description", "r"."IsDeleted", "r"."Name", "r"."Updated", "r"."UpdatedBy"
FROM "User" AS "u"
INNER JOIN "Role" AS "r" ON "u"."RoleId" = "r"."Id"
WHERE NOT ("u"."IsDeleted") AND lower("u"."EmployeeNumber") = @__ToLower_0
LIMIT 1|duration: 74.198
2025-07-08 08:53:43.7804|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
SELECT "u"."Id", "u"."Active", "u"."Created", "u"."CreatedBy", "u"."Deleted", "u"."DeletedBy", "u"."Email", "u"."EmployeeNumber", "u"."IsDeleted", "u"."Name", "u"."Password", "u"."RoleId", "u"."Updated", "u"."UpdatedBy", "r"."Id", "r"."Active", "r"."Created", "r"."CreatedBy", "r"."Deleted", "r"."DeletedBy", "r"."Description", "r"."IsDeleted", "r"."Name", "r"."Updated", "r"."UpdatedBy"
FROM "User" AS "u"
INNER JOIN "Role" AS "r" ON "u"."RoleId" = "r"."Id"
WHERE NOT ("u"."IsDeleted") AND "u"."Id" = @__id_0
LIMIT 1|duration: 343.211
2025-07-08 08:53:43.7804|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AuthController|Login exitoso para usuario: "<EMAIL>"/"123456"|duration: 344.864
2025-07-08 08:53:43.7804|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AuthController.Login (BitacoraResiduosESH.Backend.Backend.WebAPI), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 319.2652ms.|duration: 345.086
2025-07-08 08:53:43.7804|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor|Executing OkObjectResult, writing value of type 'BitacoraResiduosESH.Backend.Backend.Application.DTOs.Auth.AuthResponseDto'.|duration: 346.217
2025-07-08 08:53:43.7804|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AuthController.Login (BitacoraResiduosESH.Backend.Backend.WebAPI) in 342.8317ms|duration: 348.070
2025-07-08 08:53:43.7804|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AuthController.Login (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 348.351
2025-07-08 08:53:43.7881|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 POST http://localhost:5001/api/auth/login - 200 - application/json;+charset=utf-8 349.6052ms|duration: 349.716
2025-07-08 08:53:58.3231|INFO|Microsoft.Hosting.Lifetime|Application is shutting down...|duration: 
2025-07-08 10:08:27.4447|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (17ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
PRAGMA journal_mode = 'wal';|duration: 
2025-07-08 10:08:27.5542|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (4ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE TABLE "Areas" (
    "Id" INTEGER NOT NULL CONSTRAINT "AreasPK" PRIMARY KEY AUTOINCREMENT,
    "Created" datetime2(3) NOT NULL DEFAULT (getutcdate()),
    "CreatedBy" nvarchar(50) NOT NULL DEFAULT (suser_name()),
    "IsDeleted" bit NOT NULL DEFAULT 0,
    "Deleted" datetime2(3) NULL,
    "DeletedBy" nvarchar(50) NULL,
    "Updated" datetime2(3) NULL,
    "UpdatedBy" nvarchar(50) NULL,
    "Active" bit NOT NULL DEFAULT 1,
    "Name" nvarchar(200) NOT NULL,
    "Description" nvarchar(1000) NULL
);|duration: 
2025-07-08 10:08:27.5725|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (10ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE TABLE "ContainerTypes" (
    "Id" INTEGER NOT NULL CONSTRAINT "ContainerTypesPK" PRIMARY KEY AUTOINCREMENT,
    "Created" datetime2(3) NOT NULL DEFAULT (getutcdate()),
    "CreatedBy" nvarchar(50) NOT NULL DEFAULT (suser_name()),
    "IsDeleted" bit NOT NULL DEFAULT 0,
    "Deleted" datetime2(3) NULL,
    "DeletedBy" nvarchar(50) NULL,
    "Updated" datetime2(3) NULL,
    "UpdatedBy" nvarchar(50) NULL,
    "Active" bit NOT NULL DEFAULT 1,
    "Name" nvarchar(200) NOT NULL,
    "Description" nvarchar(1000) NULL
);|duration: 
2025-07-08 10:08:27.5725|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (4ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE TABLE "Role" (
    "Id" INTEGER NOT NULL CONSTRAINT "RolePK" PRIMARY KEY AUTOINCREMENT,
    "Created" datetime2(3) NOT NULL DEFAULT (getutcdate()),
    "CreatedBy" nvarchar(50) NOT NULL DEFAULT (suser_name()),
    "IsDeleted" bit NOT NULL DEFAULT 0,
    "Deleted" datetime2(3) NULL,
    "DeletedBy" nvarchar(50) NULL,
    "Updated" datetime2(3) NULL,
    "UpdatedBy" nvarchar(50) NULL,
    "Active" bit NOT NULL DEFAULT 1,
    "Name" nvarchar(200) NOT NULL,
    "Description" nvarchar(1000) NULL
);|duration: 
2025-07-08 10:08:27.5853|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (2ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE TABLE "User" (
    "Id" INTEGER NOT NULL CONSTRAINT "UserPK" PRIMARY KEY AUTOINCREMENT,
    "Name" nvarchar(200) NOT NULL,
    "EmployeeNumber" nvarchar(30) NOT NULL,
    "Email" nvarchar(255) NULL,
    "Password" nvarchar(255) NOT NULL,
    "RoleId" INTEGER NOT NULL,
    "Created" datetime2(3) NOT NULL DEFAULT (getutcdate()),
    "CreatedBy" nvarchar(50) NOT NULL DEFAULT (suser_name()),
    "IsDeleted" bit NOT NULL DEFAULT 0,
    "Deleted" datetime2(3) NULL,
    "DeletedBy" nvarchar(50) NULL,
    "Updated" datetime2(3) NULL,
    "UpdatedBy" nvarchar(50) NULL,
    "Active" bit NOT NULL DEFAULT 1,
    CONSTRAINT "FK_User_RoleId_Role_Id" FOREIGN KEY ("RoleId") REFERENCES "Role" ("Id") ON DELETE RESTRICT
);|duration: 
2025-07-08 10:08:27.5853|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Areas_Active" ON "Areas" ("Active");|duration: 
2025-07-08 10:08:27.5853|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (2ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Areas_Active_IsDeleted" ON "Areas" ("Active", "IsDeleted");|duration: 
2025-07-08 10:08:27.6019|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Areas_Created" ON "Areas" ("Created");|duration: 
2025-07-08 10:08:27.6019|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Areas_IsDeleted" ON "Areas" ("IsDeleted");|duration: 
2025-07-08 10:08:27.6019|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Areas_Name_Active" ON "Areas" ("Name", "Active");|duration: 
2025-07-08 10:08:27.6019|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Areas_Name_Created" ON "Areas" ("Name", "Created");|duration: 
2025-07-08 10:08:27.6019|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE UNIQUE INDEX "UQ_Areas_Name_NotDeleted" ON "Areas" ("Name") WHERE [IsDeleted] = 0;|duration: 
2025-07-08 10:08:27.6019|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_ContainerTypes_Active" ON "ContainerTypes" ("Active");|duration: 
2025-07-08 10:08:27.6170|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_ContainerTypes_Active_IsDeleted" ON "ContainerTypes" ("Active", "IsDeleted");|duration: 
2025-07-08 10:08:27.6170|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_ContainerTypes_Created" ON "ContainerTypes" ("Created");|duration: 
2025-07-08 10:08:27.6170|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (2ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_ContainerTypes_IsDeleted" ON "ContainerTypes" ("IsDeleted");|duration: 
2025-07-08 10:08:27.6170|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_ContainerTypes_Name_Active" ON "ContainerTypes" ("Name", "Active");|duration: 
2025-07-08 10:08:27.6170|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_ContainerTypes_Name_Created" ON "ContainerTypes" ("Name", "Created");|duration: 
2025-07-08 10:08:27.6170|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE UNIQUE INDEX "UQ_ContainerTypes_Name_NotDeleted" ON "ContainerTypes" ("Name") WHERE [IsDeleted] = 0;|duration: 
2025-07-08 10:08:27.6315|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Role_Active" ON "Role" ("Active");|duration: 
2025-07-08 10:08:27.6315|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Role_Active_IsDeleted" ON "Role" ("Active", "IsDeleted");|duration: 
2025-07-08 10:08:27.6315|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Role_Created" ON "Role" ("Created");|duration: 
2025-07-08 10:08:27.6315|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Role_IsDeleted" ON "Role" ("IsDeleted");|duration: 
2025-07-08 10:08:27.6315|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Role_Name_Active" ON "Role" ("Name", "Active");|duration: 
2025-07-08 10:08:27.6315|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Role_Name_Created" ON "Role" ("Name", "Created");|duration: 
2025-07-08 10:08:27.6315|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE UNIQUE INDEX "UQ_Role_Name_NotDeleted" ON "Role" ("Name") WHERE [IsDeleted] = 0;|duration: 
2025-07-08 10:08:27.6473|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_Active" ON "User" ("Active");|duration: 
2025-07-08 10:08:27.6473|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_Active_IsDeleted" ON "User" ("Active", "IsDeleted");|duration: 
2025-07-08 10:08:27.6473|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_Created" ON "User" ("Created");|duration: 
2025-07-08 10:08:27.6473|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_Email_Active" ON "User" ("Email", "Active");|duration: 
2025-07-08 10:08:27.6473|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_IsDeleted" ON "User" ("IsDeleted");|duration: 
2025-07-08 10:08:27.6473|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_Name" ON "User" ("Name");|duration: 
2025-07-08 10:08:27.6473|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_Name_Created" ON "User" ("Name", "Created");|duration: 
2025-07-08 10:08:27.6640|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_RoleId" ON "User" ("RoleId");|duration: 
2025-07-08 10:08:27.6640|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_RoleId_Active" ON "User" ("RoleId", "Active");|duration: 
2025-07-08 10:08:27.6640|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE UNIQUE INDEX "UQ_User_Email_NotDeleted" ON "User" ("Email") WHERE [IsDeleted] = 0;|duration: 
2025-07-08 10:08:27.6640|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE UNIQUE INDEX "UQ_User_EmployeeNumber_NotDeleted" ON "User" ("EmployeeNumber") WHERE [IsDeleted] = 0;|duration: 
2025-07-08 10:08:27.6640|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Program|Base de datos inicializada correctamente|duration: 
2025-07-08 10:08:27.6785|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|Inicializando base de datos con datos de prueba...|duration: 
2025-07-08 10:08:27.8667|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (3ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM "Role" AS "r")|duration: 
2025-07-08 10:08:28.0291|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (5ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (Size = 6), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (Size = 45), @p5='?' (Size = 5), @p6='?' (DbType = DateTime), @p7='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "Role" ("Created", "CreatedBy", "Deleted", "DeletedBy", "Description", "Name", "Updated", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 10:08:28.0600|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|Rol Admin creado exitosamente|duration: 
2025-07-08 10:08:28.4455|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (Size = 6), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (Size = 27), @p5='?' (Size = 6), @p6='?' (Size = 17), @p7='?' (Size = 60), @p8='?' (DbType = Int32), @p9='?' (DbType = DateTime), @p10='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "User" ("Created", "CreatedBy", "Deleted", "DeletedBy", "Email", "EmployeeNumber", "Name", "Password", "RoleId", "Updated", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 10:08:28.4455|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|Usuario administrador creado exitosamente|duration: 
2025-07-08 10:08:28.4455|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|Inicialización de base de datos completada exitosamente|duration: 
2025-07-08 10:08:28.4455|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Program|Datos de prueba inicializados correctamente|duration: 
2025-07-08 10:08:28.5543|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Program|Aplicación iniciada correctamente|duration: 
2025-07-08 10:08:28.5873|INFO|Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager|User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.|duration: 
2025-07-08 10:08:28.6630|WARN|Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware|The WebRootPath was not found: C:\Repositories\WebProjects\BitacoraResiduosESH\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend.Backend.WebAPI\wwwroot. Static files may be unavailable.|duration: 
2025-07-08 10:08:28.7287|INFO|Microsoft.Hosting.Lifetime|Now listening on: http://localhost:5001|duration: 
2025-07-08 10:08:28.7287|INFO|Microsoft.Hosting.Lifetime|Application started. Press Ctrl+C to shut down.|duration: 
2025-07-08 10:08:28.7287|INFO|Microsoft.Hosting.Lifetime|Hosting environment: Development|duration: 
2025-07-08 10:08:28.7287|INFO|Microsoft.Hosting.Lifetime|Content root path: C:\Repositories\WebProjects\BitacoraResiduosESH\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend.Backend.WebAPI|duration: 
2025-07-08 10:08:34.6838|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar - - -|duration: 10.493
2025-07-08 10:08:34.8995|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 226.805
2025-07-08 10:08:34.9299|INFO|Microsoft.AspNetCore.Http.Result.RedirectResult|Executing RedirectResult, redirecting to scalar/.|duration: 259.159
2025-07-08 10:08:34.9299|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 260.095
2025-07-08 10:08:34.9442|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar - 302 0 - 267.9784ms|duration: 280.072
2025-07-08 10:08:34.9627|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar/ - - -|duration: 0.403
2025-07-08 10:08:34.9627|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 11.027
2025-07-08 10:08:35.1317|INFO|Microsoft.AspNetCore.Http.Result.ContentResult|Write content with HTTP Response ContentType of text/html|duration: 178.448
2025-07-08 10:08:35.1317|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 180.566
2025-07-08 10:08:35.1317|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar/ - 200 924 text/html 181.5283ms|duration: 181.999
2025-07-08 10:08:35.1572|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar/scalar.js - - -|duration: 0.317
2025-07-08 10:08:35.1572|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar/scalar.aspnetcore.js - - -|duration: 0.481
2025-07-08 10:08:35.1660|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/scalar.js'|duration: 11.003
2025-07-08 10:08:35.1660|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 12.101
2025-07-08 10:08:35.1660|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/scalar.aspnetcore.js'|duration: 14.299
2025-07-08 10:08:35.1960|INFO|Microsoft.AspNetCore.Http.Result.StatusCodeResult|Setting HTTP status code 304.|duration: 44.530
2025-07-08 10:08:35.1960|INFO|Microsoft.AspNetCore.Http.Result.StatusCodeResult|Setting HTTP status code 304.|duration: 44.137
2025-07-08 10:08:35.1960|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/scalar.js'|duration: 45.271
2025-07-08 10:08:35.1960|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/scalar.aspnetcore.js'|duration: 44.796
2025-07-08 10:08:35.1960|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar/scalar.js - 304 - - 46.0935ms|duration: 46.450
2025-07-08 10:08:35.1960|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar/scalar.aspnetcore.js - 304 - - 47.0797ms|duration: 47.409
2025-07-08 10:08:35.3489|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/openapi/v1.json - - -|duration: 0.307
2025-07-08 10:08:35.3509|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /openapi/{documentName}.json'|duration: 3.712
2025-07-08 10:08:36.1095|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /openapi/{documentName}.json'|duration: 762.132
2025-07-08 10:08:36.1095|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/openapi/v1.json - 200 - application/json;charset=utf-8 764.0996ms|duration: 764.366
2025-07-08 10:08:43.9493|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 OPTIONS http://localhost:5001/api/auth/login - - -|duration: 0.289
2025-07-08 10:08:43.9493|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 3.600
2025-07-08 10:08:43.9493|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 OPTIONS http://localhost:5001/api/auth/login - 204 - - 6.3239ms|duration: 6.541
2025-07-08 10:08:43.9622|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 POST http://localhost:5001/api/auth/login - application/json 47|duration: 0.191
2025-07-08 10:08:43.9622|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 6.674
2025-07-08 10:08:43.9622|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AuthController.Login (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 7.620
2025-07-08 10:08:44.0438|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[BitacoraResiduosESH.Backend.Backend.Application.DTOs.Auth.AuthResponseDto]] Login(BitacoraResiduosESH.Backend.Backend.Application.DTOs.Auth.LoginDto) on controller BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AuthController (BitacoraResiduosESH.Backend.Backend.WebAPI).|duration: 81.747
2025-07-08 10:08:44.3119|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executing action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AuthController.Login (BitacoraResiduosESH.Backend.Backend.WebAPI) - Validation state: Valid|duration: 349.963
2025-07-08 10:08:44.8123|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (2ms) [Parameters=[@__ToLower_0='?' (Size = 6)], CommandType='Text', CommandTimeout='30']
SELECT "u"."Id", "u"."Active", "u"."Created", "u"."CreatedBy", "u"."Deleted", "u"."DeletedBy", "u"."Email", "u"."EmployeeNumber", "u"."IsDeleted", "u"."Name", "u"."Password", "u"."RoleId", "u"."Updated", "u"."UpdatedBy", "r"."Id", "r"."Active", "r"."Created", "r"."CreatedBy", "r"."Deleted", "r"."DeletedBy", "r"."Description", "r"."IsDeleted", "r"."Name", "r"."Updated", "r"."UpdatedBy"
FROM "User" AS "u"
INNER JOIN "Role" AS "r" ON "u"."RoleId" = "r"."Id"
WHERE NOT ("u"."IsDeleted") AND lower("u"."EmployeeNumber") = @__ToLower_0
LIMIT 1|duration: 858.060
2025-07-08 10:08:46.1482|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (2ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
SELECT "u"."Id", "u"."Active", "u"."Created", "u"."CreatedBy", "u"."Deleted", "u"."DeletedBy", "u"."Email", "u"."EmployeeNumber", "u"."IsDeleted", "u"."Name", "u"."Password", "u"."RoleId", "u"."Updated", "u"."UpdatedBy", "r"."Id", "r"."Active", "r"."Created", "r"."CreatedBy", "r"."Deleted", "r"."DeletedBy", "r"."Description", "r"."IsDeleted", "r"."Name", "r"."Updated", "r"."UpdatedBy"
FROM "User" AS "u"
INNER JOIN "Role" AS "r" ON "u"."RoleId" = "r"."Id"
WHERE NOT ("u"."IsDeleted") AND "u"."Id" = @__id_0
LIMIT 1|duration: 2193.301
2025-07-08 10:08:46.1667|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AuthController|Login exitoso para usuario: "<EMAIL>"/"123456"|duration: 2204.618
2025-07-08 10:08:46.1830|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AuthController.Login (BitacoraResiduosESH.Backend.Backend.WebAPI), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 1854.7436ms.|duration: 2220.976
2025-07-08 10:08:46.2770|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor|Executing OkObjectResult, writing value of type 'BitacoraResiduosESH.Backend.Backend.Application.DTOs.Auth.AuthResponseDto'.|duration: 2318.987
2025-07-08 10:08:46.3085|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AuthController.Login (BitacoraResiduosESH.Backend.Backend.WebAPI) in 2246.9565ms|duration: 2346.438
2025-07-08 10:08:46.3085|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AuthController.Login (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 2347.146
2025-07-08 10:08:46.3207|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 POST http://localhost:5001/api/auth/login - 200 - application/json;+charset=utf-8 2358.3899ms|duration: 2358.631
2025-07-08 10:08:54.1690|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 OPTIONS http://localhost:5001/api/role?pageNumber=1&pageSize=10&includeDeleted=false - - -|duration: 0.782
2025-07-08 10:08:54.1690|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 4.096
2025-07-08 10:08:54.1690|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 OPTIONS http://localhost:5001/api/role?pageNumber=1&pageSize=10&includeDeleted=false - 204 - - 5.3528ms|duration: 5.524
2025-07-08 10:08:54.1690|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/api/role?pageNumber=1&pageSize=10&includeDeleted=false - application/json -|duration: 0.150
2025-07-08 10:08:54.3040|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 128.819
2025-07-08 10:08:54.3040|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.RoleController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 129.872
2025-07-08 10:08:54.3369|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Route matched with {action = "GetAll", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[BitacoraResiduosESH.Backend.Backend.Application.Common.PagedResponse`1[BitacoraResiduosESH.Backend.Backend.Application.DTOs.Role.RoleDto]]] GetAll(Int32, Int32, Boolean) on controller BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.RoleController (BitacoraResiduosESH.Backend.Backend.WebAPI).|duration: 161.697
2025-07-08 10:08:54.3663|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executing action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.RoleController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI) - Validation state: Valid|duration: 196.729
2025-07-08 10:08:54.5084|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT COUNT(*)
FROM "Role" AS "r"
WHERE NOT ("r"."IsDeleted")|duration: 338.864
2025-07-08 10:08:54.5720|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (2ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
SELECT "r"."Id", "r"."Active", "r"."Created", "r"."CreatedBy", "r"."Deleted", "r"."DeletedBy", "r"."Description", "r"."IsDeleted", "r"."Name", "r"."Updated", "r"."UpdatedBy"
FROM "Role" AS "r"
WHERE NOT ("r"."IsDeleted")
ORDER BY "r"."Id"
LIMIT @__p_1 OFFSET @__p_0|duration: 402.902
2025-07-08 10:08:54.6024|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.RoleController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 230.0924ms.|duration: 428.014
2025-07-08 10:08:54.6024|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor|Executing OkObjectResult, writing value of type 'BitacoraResiduosESH.Backend.Backend.Application.Common.PagedResponse`1[[BitacoraResiduosESH.Backend.Backend.Application.DTOs.Role.RoleDto, BitacoraResiduosESH.Backend.Backend.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]'.|duration: 432.892
2025-07-08 10:08:54.6285|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.RoleController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI) in 288.6163ms|duration: 453.766
2025-07-08 10:08:54.6285|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.RoleController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 454.337
2025-07-08 10:08:54.6322|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/api/role?pageNumber=1&pageSize=10&includeDeleted=false - 200 - application/json;+charset=utf-8 457.2038ms|duration: 457.441
2025-07-08 10:08:57.0059|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 OPTIONS http://localhost:5001/api/role/1/deactivate - - -|duration: 0.648
2025-07-08 10:08:57.0072|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 3.723
2025-07-08 10:08:57.0072|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 OPTIONS http://localhost:5001/api/role/1/deactivate - 204 - - 5.1803ms|duration: 5.486
2025-07-08 10:08:57.0072|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 PATCH http://localhost:5001/api/role/1/deactivate - application/json -|duration: 0.163
2025-07-08 10:08:57.0243|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 14.334
2025-07-08 10:08:57.0243|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.RoleController.Deactivate (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 15.255
2025-07-08 10:08:57.0243|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Route matched with {action = "Deactivate", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Deactivate(Int32) on controller BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.RoleController (BitacoraResiduosESH.Backend.Backend.WebAPI).|duration: 22.234
2025-07-08 10:08:57.0382|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executing action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.RoleController.Deactivate (BitacoraResiduosESH.Backend.Backend.WebAPI) - Validation state: Valid|duration: 37.099
2025-07-08 10:08:57.0942|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
SELECT "r"."Id", "r"."Active", "r"."Created", "r"."CreatedBy", "r"."Deleted", "r"."DeletedBy", "r"."Description", "r"."IsDeleted", "r"."Name", "r"."Updated", "r"."UpdatedBy"
FROM "Role" AS "r"
WHERE "r"."Id" = @__id_0
LIMIT 1|duration: 90.499
2025-07-08 10:08:57.1387|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (2ms) [Parameters=[@p10='?' (DbType = Int32), @p0='?' (DbType = Boolean), @p1='?' (DbType = DateTime), @p2='?' (Size = 6), @p3='?' (DbType = DateTime), @p4='?', @p5='?' (Size = 45), @p6='?' (DbType = Boolean), @p7='?' (Size = 5), @p8='?' (DbType = DateTime), @p9='?' (Size = 17)], CommandType='Text', CommandTimeout='30']
UPDATE "Role" SET "Active" = @p0, "Created" = @p1, "CreatedBy" = @p2, "Deleted" = @p3, "DeletedBy" = @p4, "Description" = @p5, "IsDeleted" = @p6, "Name" = @p7, "Updated" = @p8, "UpdatedBy" = @p9
WHERE "Id" = @p10
RETURNING 1;|duration: 136.184
2025-07-08 10:08:57.1634|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Repositories.GenericRepository`1|Entidad "Role" con ID 1 desactivada exitosamente por usuario: "Maximiliano Ponce"|duration: 152.999
2025-07-08 10:08:57.1634|INFO|BitacoraResiduosESH.Backend.Backend.Application.Services.SimpleEntityService`5|"Role" desactivada exitosamente con ID: 1 por usuario: "Maximiliano Ponce"|duration: 153.119
2025-07-08 10:08:57.1634|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5|"Role" con ID 1 desactivado exitosamente por usuario: "Maximiliano Ponce"|duration: 153.227
2025-07-08 10:08:57.1634|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.RoleController.Deactivate (BitacoraResiduosESH.Backend.Backend.WebAPI), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 116.2298ms.|duration: 154.087
2025-07-08 10:08:57.1634|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor|Executing OkObjectResult, writing value of type '<>f__AnonymousType1`1[[System.String, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.|duration: 158.565
2025-07-08 10:08:57.1634|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.RoleController.Deactivate (BitacoraResiduosESH.Backend.Backend.WebAPI) in 136.5867ms|duration: 164.234
2025-07-08 10:08:57.1634|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.RoleController.Deactivate (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 165.496
2025-07-08 10:08:57.1789|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 PATCH http://localhost:5001/api/role/1/deactivate - 200 - application/json;+charset=utf-8 167.8340ms|duration: 167.963
2025-07-08 10:08:57.1789|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/api/role?pageNumber=1&pageSize=10&includeDeleted=false - application/json -|duration: 0.138
2025-07-08 10:08:57.1789|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 8.087
2025-07-08 10:08:57.1789|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.RoleController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 9.609
2025-07-08 10:08:57.1789|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Route matched with {action = "GetAll", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[BitacoraResiduosESH.Backend.Backend.Application.Common.PagedResponse`1[BitacoraResiduosESH.Backend.Backend.Application.DTOs.Role.RoleDto]]] GetAll(Int32, Int32, Boolean) on controller BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.RoleController (BitacoraResiduosESH.Backend.Backend.WebAPI).|duration: 10.850
2025-07-08 10:08:57.2099|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executing action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.RoleController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI) - Validation state: Valid|duration: 34.526
2025-07-08 10:08:57.2264|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT COUNT(*)
FROM "Role" AS "r"
WHERE NOT ("r"."IsDeleted")|duration: 48.991
2025-07-08 10:08:57.2264|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
SELECT "r"."Id", "r"."Active", "r"."Created", "r"."CreatedBy", "r"."Deleted", "r"."DeletedBy", "r"."Description", "r"."IsDeleted", "r"."Name", "r"."Updated", "r"."UpdatedBy"
FROM "Role" AS "r"
WHERE NOT ("r"."IsDeleted")
ORDER BY "r"."Id"
LIMIT @__p_1 OFFSET @__p_0|duration: 57.556
2025-07-08 10:08:57.2412|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.RoleController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 27.2122ms.|duration: 62.318
2025-07-08 10:08:57.2412|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor|Executing OkObjectResult, writing value of type 'BitacoraResiduosESH.Backend.Backend.Application.Common.PagedResponse`1[[BitacoraResiduosESH.Backend.Backend.Application.DTOs.Role.RoleDto, BitacoraResiduosESH.Backend.Backend.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]'.|duration: 64.922
2025-07-08 10:08:57.2412|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.RoleController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI) in 50.0816ms|duration: 68.845
2025-07-08 10:08:57.2412|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.RoleController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 70.168
2025-07-08 10:08:57.2412|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/api/role?pageNumber=1&pageSize=10&includeDeleted=false - 200 - application/json;+charset=utf-8 72.5793ms|duration: 72.791
2025-07-08 10:08:57.9972|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 OPTIONS http://localhost:5001/api/role/1/activate - - -|duration: 0.304
2025-07-08 10:08:57.9972|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 2.808
2025-07-08 10:08:57.9972|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 OPTIONS http://localhost:5001/api/role/1/activate - 204 - - 3.9674ms|duration: 4.393
2025-07-08 10:08:57.9972|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 PATCH http://localhost:5001/api/role/1/activate - application/json -|duration: 0.169
2025-07-08 10:08:58.0074|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 5.089
2025-07-08 10:08:58.0074|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.RoleController.Activate (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 6.211
2025-07-08 10:08:58.0074|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Route matched with {action = "Activate", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Activate(Int32) on controller BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.RoleController (BitacoraResiduosESH.Backend.Backend.WebAPI).|duration: 14.133
2025-07-08 10:08:58.0242|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executing action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.RoleController.Activate (BitacoraResiduosESH.Backend.Backend.WebAPI) - Validation state: Valid|duration: 27.580
2025-07-08 10:08:58.0397|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
SELECT "r"."Id", "r"."Active", "r"."Created", "r"."CreatedBy", "r"."Deleted", "r"."DeletedBy", "r"."Description", "r"."IsDeleted", "r"."Name", "r"."Updated", "r"."UpdatedBy"
FROM "Role" AS "r"
WHERE "r"."Id" = @__id_0
LIMIT 1|duration: 46.551
2025-07-08 10:08:58.0544|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (2ms) [Parameters=[@p10='?' (DbType = Int32), @p0='?' (DbType = Boolean), @p1='?' (DbType = DateTime), @p2='?' (Size = 6), @p3='?' (DbType = DateTime), @p4='?', @p5='?' (Size = 45), @p6='?' (DbType = Boolean), @p7='?' (Size = 5), @p8='?' (DbType = DateTime), @p9='?' (Size = 17)], CommandType='Text', CommandTimeout='30']
UPDATE "Role" SET "Active" = @p0, "Created" = @p1, "CreatedBy" = @p2, "Deleted" = @p3, "DeletedBy" = @p4, "Description" = @p5, "IsDeleted" = @p6, "Name" = @p7, "Updated" = @p8, "UpdatedBy" = @p9
WHERE "Id" = @p10
RETURNING 1;|duration: 62.551
2025-07-08 10:08:58.0710|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Repositories.GenericRepository`1|Entidad "Role" con ID 1 activada exitosamente por usuario: "Maximiliano Ponce"|duration: 72.213
2025-07-08 10:08:58.0710|INFO|BitacoraResiduosESH.Backend.Backend.Application.Services.SimpleEntityService`5|"Role" activada exitosamente con ID: 1 por usuario: "Maximiliano Ponce"|duration: 72.315
2025-07-08 10:08:58.0710|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5|"Role" con ID 1 activado exitosamente por usuario: "Maximiliano Ponce"|duration: 72.397
2025-07-08 10:08:58.0710|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.RoleController.Activate (BitacoraResiduosESH.Backend.Backend.WebAPI), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 44.3789ms.|duration: 72.794
2025-07-08 10:08:58.0710|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor|Executing OkObjectResult, writing value of type '<>f__AnonymousType1`1[[System.String, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.|duration: 76.941
2025-07-08 10:08:58.0710|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.RoleController.Activate (BitacoraResiduosESH.Backend.Backend.WebAPI) in 58.4972ms|duration: 78.356
2025-07-08 10:08:58.0710|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.RoleController.Activate (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 79.027
2025-07-08 10:08:58.0860|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 PATCH http://localhost:5001/api/role/1/activate - 200 - application/json;+charset=utf-8 84.4395ms|duration: 84.636
2025-07-08 10:08:58.0860|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/api/role?pageNumber=1&pageSize=10&includeDeleted=false - application/json -|duration: 0.225
2025-07-08 10:08:58.0860|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 5.679
2025-07-08 10:08:58.0860|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.RoleController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 6.654
2025-07-08 10:08:58.0860|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Route matched with {action = "GetAll", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[BitacoraResiduosESH.Backend.Backend.Application.Common.PagedResponse`1[BitacoraResiduosESH.Backend.Backend.Application.DTOs.Role.RoleDto]]] GetAll(Int32, Int32, Boolean) on controller BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.RoleController (BitacoraResiduosESH.Backend.Backend.WebAPI).|duration: 7.136
2025-07-08 10:08:58.1161|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executing action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.RoleController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI) - Validation state: Valid|duration: 27.899
2025-07-08 10:08:58.1161|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT COUNT(*)
FROM "Role" AS "r"
WHERE NOT ("r"."IsDeleted")|duration: 34.222
2025-07-08 10:08:58.1316|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (2ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
SELECT "r"."Id", "r"."Active", "r"."Created", "r"."CreatedBy", "r"."Deleted", "r"."DeletedBy", "r"."Description", "r"."IsDeleted", "r"."Name", "r"."Updated", "r"."UpdatedBy"
FROM "Role" AS "r"
WHERE NOT ("r"."IsDeleted")
ORDER BY "r"."Id"
LIMIT @__p_1 OFFSET @__p_0|duration: 43.667
2025-07-08 10:08:58.1316|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.RoleController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 19.3672ms.|duration: 47.857
2025-07-08 10:08:58.1316|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor|Executing OkObjectResult, writing value of type 'BitacoraResiduosESH.Backend.Backend.Application.Common.PagedResponse`1[[BitacoraResiduosESH.Backend.Backend.Application.DTOs.Role.RoleDto, BitacoraResiduosESH.Backend.Backend.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]'.|duration: 51.721
2025-07-08 10:08:58.1316|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.RoleController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI) in 38.8782ms|duration: 53.232
2025-07-08 10:08:58.1316|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.RoleController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 53.731
2025-07-08 10:08:58.1316|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/api/role?pageNumber=1&pageSize=10&includeDeleted=false - 200 - application/json;+charset=utf-8 55.6030ms|duration: 55.856
2025-07-08 10:30:18.2195|INFO|Microsoft.Hosting.Lifetime|Application is shutting down...|duration: 
2025-07-08 10:39:17.9078|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (21ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
PRAGMA journal_mode = 'wal';|duration: 
2025-07-08 10:39:18.0082|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE TABLE "Areas" (
    "Id" INTEGER NOT NULL CONSTRAINT "AreasPK" PRIMARY KEY AUTOINCREMENT,
    "Created" datetime2(3) NOT NULL DEFAULT (getutcdate()),
    "CreatedBy" nvarchar(50) NOT NULL DEFAULT (suser_name()),
    "IsDeleted" bit NOT NULL DEFAULT 0,
    "Deleted" datetime2(3) NULL,
    "DeletedBy" nvarchar(50) NULL,
    "Updated" datetime2(3) NULL,
    "UpdatedBy" nvarchar(50) NULL,
    "Active" bit NOT NULL DEFAULT 1,
    "Name" nvarchar(200) NOT NULL,
    "Description" nvarchar(1000) NULL
);|duration: 
2025-07-08 10:39:18.0082|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE TABLE "BitacoraEntries" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_BitacoraEntries" PRIMARY KEY AUTOINCREMENT,
    "Created" TEXT NOT NULL,
    "CreatedBy" TEXT NOT NULL,
    "IsDeleted" INTEGER NOT NULL,
    "Deleted" TEXT NULL,
    "DeletedBy" TEXT NULL,
    "Updated" TEXT NULL,
    "UpdatedBy" TEXT NULL,
    "Active" INTEGER NOT NULL,
    "Name" TEXT NOT NULL,
    "Description" TEXT NULL
);|duration: 
2025-07-08 10:39:18.0082|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE TABLE "ContainerTypes" (
    "Id" INTEGER NOT NULL CONSTRAINT "ContainerTypesPK" PRIMARY KEY AUTOINCREMENT,
    "Created" datetime2(3) NOT NULL DEFAULT (getutcdate()),
    "CreatedBy" nvarchar(50) NOT NULL DEFAULT (suser_name()),
    "IsDeleted" bit NOT NULL DEFAULT 0,
    "Deleted" datetime2(3) NULL,
    "DeletedBy" nvarchar(50) NULL,
    "Updated" datetime2(3) NULL,
    "UpdatedBy" nvarchar(50) NULL,
    "Active" bit NOT NULL DEFAULT 1,
    "Name" nvarchar(200) NOT NULL,
    "Description" nvarchar(1000) NULL
);|duration: 
2025-07-08 10:39:18.0228|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE TABLE "Role" (
    "Id" INTEGER NOT NULL CONSTRAINT "RolePK" PRIMARY KEY AUTOINCREMENT,
    "Created" datetime2(3) NOT NULL DEFAULT (getutcdate()),
    "CreatedBy" nvarchar(50) NOT NULL DEFAULT (suser_name()),
    "IsDeleted" bit NOT NULL DEFAULT 0,
    "Deleted" datetime2(3) NULL,
    "DeletedBy" nvarchar(50) NULL,
    "Updated" datetime2(3) NULL,
    "UpdatedBy" nvarchar(50) NULL,
    "Active" bit NOT NULL DEFAULT 1,
    "Name" nvarchar(200) NOT NULL,
    "Description" nvarchar(1000) NULL
);|duration: 
2025-07-08 10:39:18.0228|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE TABLE "User" (
    "Id" INTEGER NOT NULL CONSTRAINT "UserPK" PRIMARY KEY AUTOINCREMENT,
    "Name" nvarchar(200) NOT NULL,
    "EmployeeNumber" nvarchar(30) NOT NULL,
    "Email" nvarchar(255) NULL,
    "Password" nvarchar(255) NOT NULL,
    "RoleId" INTEGER NOT NULL,
    "Created" datetime2(3) NOT NULL DEFAULT (getutcdate()),
    "CreatedBy" nvarchar(50) NOT NULL DEFAULT (suser_name()),
    "IsDeleted" bit NOT NULL DEFAULT 0,
    "Deleted" datetime2(3) NULL,
    "DeletedBy" nvarchar(50) NULL,
    "Updated" datetime2(3) NULL,
    "UpdatedBy" nvarchar(50) NULL,
    "Active" bit NOT NULL DEFAULT 1,
    CONSTRAINT "FK_User_RoleId_Role_Id" FOREIGN KEY ("RoleId") REFERENCES "Role" ("Id") ON DELETE RESTRICT
);|duration: 
2025-07-08 10:39:18.0228|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Areas_Active" ON "Areas" ("Active");|duration: 
2025-07-08 10:39:18.0228|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Areas_Active_IsDeleted" ON "Areas" ("Active", "IsDeleted");|duration: 
2025-07-08 10:39:18.0228|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Areas_Created" ON "Areas" ("Created");|duration: 
2025-07-08 10:39:18.0228|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Areas_IsDeleted" ON "Areas" ("IsDeleted");|duration: 
2025-07-08 10:39:18.0228|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Areas_Name_Active" ON "Areas" ("Name", "Active");|duration: 
2025-07-08 10:39:18.0228|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Areas_Name_Created" ON "Areas" ("Name", "Created");|duration: 
2025-07-08 10:39:18.0228|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE UNIQUE INDEX "UQ_Areas_Name_NotDeleted" ON "Areas" ("Name") WHERE [IsDeleted] = 0;|duration: 
2025-07-08 10:39:18.0228|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_ContainerTypes_Active" ON "ContainerTypes" ("Active");|duration: 
2025-07-08 10:39:18.0228|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_ContainerTypes_Active_IsDeleted" ON "ContainerTypes" ("Active", "IsDeleted");|duration: 
2025-07-08 10:39:18.0378|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_ContainerTypes_Created" ON "ContainerTypes" ("Created");|duration: 
2025-07-08 10:39:18.0378|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_ContainerTypes_IsDeleted" ON "ContainerTypes" ("IsDeleted");|duration: 
2025-07-08 10:39:18.0378|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_ContainerTypes_Name_Active" ON "ContainerTypes" ("Name", "Active");|duration: 
2025-07-08 10:39:18.0378|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_ContainerTypes_Name_Created" ON "ContainerTypes" ("Name", "Created");|duration: 
2025-07-08 10:39:18.0378|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE UNIQUE INDEX "UQ_ContainerTypes_Name_NotDeleted" ON "ContainerTypes" ("Name") WHERE [IsDeleted] = 0;|duration: 
2025-07-08 10:39:18.0378|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Role_Active" ON "Role" ("Active");|duration: 
2025-07-08 10:39:18.0378|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (2ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Role_Active_IsDeleted" ON "Role" ("Active", "IsDeleted");|duration: 
2025-07-08 10:39:18.0378|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Role_Created" ON "Role" ("Created");|duration: 
2025-07-08 10:39:18.0378|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Role_IsDeleted" ON "Role" ("IsDeleted");|duration: 
2025-07-08 10:39:18.0537|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Role_Name_Active" ON "Role" ("Name", "Active");|duration: 
2025-07-08 10:39:18.0537|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Role_Name_Created" ON "Role" ("Name", "Created");|duration: 
2025-07-08 10:39:18.0537|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE UNIQUE INDEX "UQ_Role_Name_NotDeleted" ON "Role" ("Name") WHERE [IsDeleted] = 0;|duration: 
2025-07-08 10:39:18.0537|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_Active" ON "User" ("Active");|duration: 
2025-07-08 10:39:18.0537|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_Active_IsDeleted" ON "User" ("Active", "IsDeleted");|duration: 
2025-07-08 10:39:18.0537|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_Created" ON "User" ("Created");|duration: 
2025-07-08 10:39:18.0537|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_Email_Active" ON "User" ("Email", "Active");|duration: 
2025-07-08 10:39:18.0537|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_IsDeleted" ON "User" ("IsDeleted");|duration: 
2025-07-08 10:39:18.0537|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_Name" ON "User" ("Name");|duration: 
2025-07-08 10:39:18.0537|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_Name_Created" ON "User" ("Name", "Created");|duration: 
2025-07-08 10:39:18.0537|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_RoleId" ON "User" ("RoleId");|duration: 
2025-07-08 10:39:18.0698|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_RoleId_Active" ON "User" ("RoleId", "Active");|duration: 
2025-07-08 10:39:18.0698|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE UNIQUE INDEX "UQ_User_Email_NotDeleted" ON "User" ("Email") WHERE [IsDeleted] = 0;|duration: 
2025-07-08 10:39:18.0698|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE UNIQUE INDEX "UQ_User_EmployeeNumber_NotDeleted" ON "User" ("EmployeeNumber") WHERE [IsDeleted] = 0;|duration: 
2025-07-08 10:39:18.0698|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Program|Base de datos inicializada correctamente|duration: 
2025-07-08 10:39:18.0854|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|Inicializando base de datos con datos de prueba...|duration: 
2025-07-08 10:39:18.2722|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (2ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM "Role" AS "r")|duration: 
2025-07-08 10:39:18.4461|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (7ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (Size = 6), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (Size = 45), @p5='?' (Size = 5), @p6='?' (DbType = DateTime), @p7='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "Role" ("Created", "CreatedBy", "Deleted", "DeletedBy", "Description", "Name", "Updated", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 10:39:18.4816|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|Rol Admin creado exitosamente|duration: 
2025-07-08 10:39:18.9345|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (Size = 6), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (Size = 27), @p5='?' (Size = 6), @p6='?' (Size = 17), @p7='?' (Size = 60), @p8='?' (DbType = Int32), @p9='?' (DbType = DateTime), @p10='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "User" ("Created", "CreatedBy", "Deleted", "DeletedBy", "Email", "EmployeeNumber", "Name", "Password", "RoleId", "Updated", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 10:39:18.9462|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|Usuario administrador creado exitosamente|duration: 
2025-07-08 10:39:18.9462|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|Inicialización de base de datos completada exitosamente|duration: 
2025-07-08 10:39:18.9462|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Program|Datos de prueba inicializados correctamente|duration: 
2025-07-08 10:39:19.0341|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Program|Aplicación iniciada correctamente|duration: 
2025-07-08 10:39:19.0870|INFO|Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager|User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.|duration: 
2025-07-08 10:39:19.1641|WARN|Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware|The WebRootPath was not found: C:\Repositories\WebProjects\BitacoraResiduosESH\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend.Backend.WebAPI\wwwroot. Static files may be unavailable.|duration: 
2025-07-08 10:39:19.2337|INFO|Microsoft.Hosting.Lifetime|Now listening on: http://localhost:5001|duration: 
2025-07-08 10:39:19.2429|INFO|Microsoft.Hosting.Lifetime|Application started. Press Ctrl+C to shut down.|duration: 
2025-07-08 10:39:19.2429|INFO|Microsoft.Hosting.Lifetime|Hosting environment: Development|duration: 
2025-07-08 10:39:19.2429|INFO|Microsoft.Hosting.Lifetime|Content root path: C:\Repositories\WebProjects\BitacoraResiduosESH\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend.Backend.WebAPI|duration: 
2025-07-08 10:39:20.1777|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar - - -|duration: 2.851
2025-07-08 10:39:20.2286|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 55.390
2025-07-08 10:39:20.2286|INFO|Microsoft.AspNetCore.Http.Result.RedirectResult|Executing RedirectResult, redirecting to scalar/.|duration: 63.320
2025-07-08 10:39:20.2286|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 63.737
2025-07-08 10:39:20.2415|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar - 302 0 - 65.6849ms|duration: 68.427
2025-07-08 10:39:20.2415|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar/ - - -|duration: 0.162
2025-07-08 10:39:20.2415|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 3.006
2025-07-08 10:39:20.2886|INFO|Microsoft.AspNetCore.Http.Result.ContentResult|Write content with HTTP Response ContentType of text/html|duration: 42.523
2025-07-08 10:39:20.2886|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 43.111
2025-07-08 10:39:20.2886|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar/ - 200 924 text/html 43.3476ms|duration: 43.530
2025-07-08 10:39:20.2886|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar/scalar.js - - -|duration: 0.148
2025-07-08 10:39:20.3037|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar/scalar.aspnetcore.js - - -|duration: 0.082
2025-07-08 10:39:20.3037|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/scalar.js'|duration: 3.052
2025-07-08 10:39:20.3037|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 1.867
2025-07-08 10:39:20.3037|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/scalar.aspnetcore.js'|duration: 2.515
2025-07-08 10:39:20.3037|INFO|Microsoft.AspNetCore.Http.Result.StatusCodeResult|Setting HTTP status code 304.|duration: 10.426
2025-07-08 10:39:20.3037|INFO|Microsoft.AspNetCore.Http.Result.StatusCodeResult|Setting HTTP status code 304.|duration: 13.083
2025-07-08 10:39:20.3037|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/scalar.aspnetcore.js'|duration: 10.627
2025-07-08 10:39:20.3037|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/scalar.js'|duration: 13.278
2025-07-08 10:39:20.3037|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar/scalar.js - 304 - - 13.5087ms|duration: 13.628
2025-07-08 10:39:20.3037|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar/scalar.aspnetcore.js - 304 - - 11.3279ms|duration: 11.405
2025-07-08 10:39:20.4837|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/openapi/v1.json - - -|duration: 0.145
2025-07-08 10:39:20.4837|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /openapi/{documentName}.json'|duration: 1.092
2025-07-08 10:39:20.6635|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /openapi/{documentName}.json'|duration: 179.959
2025-07-08 10:39:20.6646|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/openapi/v1.json - 200 - application/json;charset=utf-8 181.1795ms|duration: 181.340
2025-07-08 10:44:00.5863|INFO|Microsoft.Hosting.Lifetime|Application is shutting down...|duration: 
2025-07-08 11:16:02.9099|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (16ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
PRAGMA journal_mode = 'wal';|duration: 
2025-07-08 11:16:03.0252|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE TABLE "Areas" (
    "Id" INTEGER NOT NULL CONSTRAINT "AreasPK" PRIMARY KEY AUTOINCREMENT,
    "Created" datetime2(3) NOT NULL DEFAULT (getutcdate()),
    "CreatedBy" nvarchar(50) NOT NULL DEFAULT (suser_name()),
    "IsDeleted" bit NOT NULL DEFAULT 0,
    "Deleted" datetime2(3) NULL,
    "DeletedBy" nvarchar(50) NULL,
    "Updated" datetime2(3) NULL,
    "UpdatedBy" nvarchar(50) NULL,
    "Active" bit NOT NULL DEFAULT 1,
    "Name" nvarchar(200) NOT NULL,
    "Description" nvarchar(1000) NULL
);|duration: 
2025-07-08 11:16:03.0252|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE TABLE "ContainerTypes" (
    "Id" INTEGER NOT NULL CONSTRAINT "ContainerTypesPK" PRIMARY KEY AUTOINCREMENT,
    "Created" datetime2(3) NOT NULL DEFAULT (getutcdate()),
    "CreatedBy" nvarchar(50) NOT NULL DEFAULT (suser_name()),
    "IsDeleted" bit NOT NULL DEFAULT 0,
    "Deleted" datetime2(3) NULL,
    "DeletedBy" nvarchar(50) NULL,
    "Updated" datetime2(3) NULL,
    "UpdatedBy" nvarchar(50) NULL,
    "Active" bit NOT NULL DEFAULT 1,
    "Name" nvarchar(200) NOT NULL,
    "Description" nvarchar(1000) NULL
);|duration: 
2025-07-08 11:16:03.0252|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE TABLE "Role" (
    "Id" INTEGER NOT NULL CONSTRAINT "RolePK" PRIMARY KEY AUTOINCREMENT,
    "Created" datetime2(3) NOT NULL DEFAULT (getutcdate()),
    "CreatedBy" nvarchar(50) NOT NULL DEFAULT (suser_name()),
    "IsDeleted" bit NOT NULL DEFAULT 0,
    "Deleted" datetime2(3) NULL,
    "DeletedBy" nvarchar(50) NULL,
    "Updated" datetime2(3) NULL,
    "UpdatedBy" nvarchar(50) NULL,
    "Active" bit NOT NULL DEFAULT 1,
    "Name" nvarchar(200) NOT NULL,
    "Description" nvarchar(1000) NULL
);|duration: 
2025-07-08 11:16:03.0391|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE TABLE "WasteType" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_WasteType" PRIMARY KEY AUTOINCREMENT,
    "Created" TEXT NOT NULL,
    "CreatedBy" TEXT NOT NULL,
    "IsDeleted" INTEGER NOT NULL,
    "Deleted" TEXT NULL,
    "DeletedBy" TEXT NULL,
    "Updated" TEXT NULL,
    "UpdatedBy" TEXT NULL,
    "Active" INTEGER NOT NULL,
    "Name" TEXT NOT NULL,
    "Description" TEXT NULL
);|duration: 
2025-07-08 11:16:03.0391|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE TABLE "User" (
    "Id" INTEGER NOT NULL CONSTRAINT "UserPK" PRIMARY KEY AUTOINCREMENT,
    "Name" nvarchar(200) NOT NULL,
    "EmployeeNumber" nvarchar(30) NOT NULL,
    "Email" nvarchar(255) NULL,
    "Password" nvarchar(255) NOT NULL,
    "RoleId" INTEGER NOT NULL,
    "Created" datetime2(3) NOT NULL DEFAULT (getutcdate()),
    "CreatedBy" nvarchar(50) NOT NULL DEFAULT (suser_name()),
    "IsDeleted" bit NOT NULL DEFAULT 0,
    "Deleted" datetime2(3) NULL,
    "DeletedBy" nvarchar(50) NULL,
    "Updated" datetime2(3) NULL,
    "UpdatedBy" nvarchar(50) NULL,
    "Active" bit NOT NULL DEFAULT 1,
    CONSTRAINT "FK_User_RoleId_Role_Id" FOREIGN KEY ("RoleId") REFERENCES "Role" ("Id") ON DELETE RESTRICT
);|duration: 
2025-07-08 11:16:03.0391|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE TABLE "BitacoraEntries" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_BitacoraEntries" PRIMARY KEY AUTOINCREMENT,
    "WasteTypeId" INTEGER NOT NULL,
    "GrossWeight" TEXT NOT NULL,
    "Tare" TEXT NOT NULL,
    "NetWeightLB" TEXT NOT NULL,
    "NetWeightKG" TEXT NOT NULL,
    "UnitPrice" TEXT NOT NULL,
    "ContainerTypeId" INTEGER NOT NULL,
    "AreaId" INTEGER NOT NULL,
    "EntryDate" TEXT NOT NULL,
    "DepartureDate" TEXT NULL,
    "EnteredBy" TEXT NOT NULL,
    "Created" TEXT NOT NULL,
    "CreatedBy" TEXT NOT NULL,
    "IsDeleted" INTEGER NOT NULL,
    "Deleted" TEXT NULL,
    "DeletedBy" TEXT NULL,
    "Updated" TEXT NULL,
    "UpdatedBy" TEXT NULL,
    "Active" INTEGER NOT NULL,
    "Name" TEXT NOT NULL,
    "Description" TEXT NULL,
    CONSTRAINT "FK_BitacoraEntries_Areas_AreaId" FOREIGN KEY ("AreaId") REFERENCES "Areas" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_BitacoraEntries_ContainerTypes_ContainerTypeId" FOREIGN KEY ("ContainerTypeId") REFERENCES "ContainerTypes" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_BitacoraEntries_WasteType_WasteTypeId" FOREIGN KEY ("WasteTypeId") REFERENCES "WasteType" ("Id") ON DELETE CASCADE
);|duration: 
2025-07-08 11:16:03.0391|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Areas_Active" ON "Areas" ("Active");|duration: 
2025-07-08 11:16:03.0391|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Areas_Active_IsDeleted" ON "Areas" ("Active", "IsDeleted");|duration: 
2025-07-08 11:16:03.0391|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Areas_Created" ON "Areas" ("Created");|duration: 
2025-07-08 11:16:03.0391|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Areas_IsDeleted" ON "Areas" ("IsDeleted");|duration: 
2025-07-08 11:16:03.0391|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Areas_Name_Active" ON "Areas" ("Name", "Active");|duration: 
2025-07-08 11:16:03.0391|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Areas_Name_Created" ON "Areas" ("Name", "Created");|duration: 
2025-07-08 11:16:03.0542|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE UNIQUE INDEX "UQ_Areas_Name_NotDeleted" ON "Areas" ("Name") WHERE [IsDeleted] = 0;|duration: 
2025-07-08 11:16:03.0542|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_BitacoraEntries_AreaId" ON "BitacoraEntries" ("AreaId");|duration: 
2025-07-08 11:16:03.0542|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_BitacoraEntries_ContainerTypeId" ON "BitacoraEntries" ("ContainerTypeId");|duration: 
2025-07-08 11:16:03.0542|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_BitacoraEntries_WasteTypeId" ON "BitacoraEntries" ("WasteTypeId");|duration: 
2025-07-08 11:16:03.0542|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_ContainerTypes_Active" ON "ContainerTypes" ("Active");|duration: 
2025-07-08 11:16:03.0542|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_ContainerTypes_Active_IsDeleted" ON "ContainerTypes" ("Active", "IsDeleted");|duration: 
2025-07-08 11:16:03.0542|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_ContainerTypes_Created" ON "ContainerTypes" ("Created");|duration: 
2025-07-08 11:16:03.0542|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_ContainerTypes_IsDeleted" ON "ContainerTypes" ("IsDeleted");|duration: 
2025-07-08 11:16:03.0542|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_ContainerTypes_Name_Active" ON "ContainerTypes" ("Name", "Active");|duration: 
2025-07-08 11:16:03.0691|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_ContainerTypes_Name_Created" ON "ContainerTypes" ("Name", "Created");|duration: 
2025-07-08 11:16:03.0691|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE UNIQUE INDEX "UQ_ContainerTypes_Name_NotDeleted" ON "ContainerTypes" ("Name") WHERE [IsDeleted] = 0;|duration: 
2025-07-08 11:16:03.0691|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Role_Active" ON "Role" ("Active");|duration: 
2025-07-08 11:16:03.0691|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Role_Active_IsDeleted" ON "Role" ("Active", "IsDeleted");|duration: 
2025-07-08 11:16:03.0691|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Role_Created" ON "Role" ("Created");|duration: 
2025-07-08 11:16:03.0691|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Role_IsDeleted" ON "Role" ("IsDeleted");|duration: 
2025-07-08 11:16:03.0691|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Role_Name_Active" ON "Role" ("Name", "Active");|duration: 
2025-07-08 11:16:03.0691|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Role_Name_Created" ON "Role" ("Name", "Created");|duration: 
2025-07-08 11:16:03.0691|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE UNIQUE INDEX "UQ_Role_Name_NotDeleted" ON "Role" ("Name") WHERE [IsDeleted] = 0;|duration: 
2025-07-08 11:16:03.0691|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_Active" ON "User" ("Active");|duration: 
2025-07-08 11:16:03.0852|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_Active_IsDeleted" ON "User" ("Active", "IsDeleted");|duration: 
2025-07-08 11:16:03.0852|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_Created" ON "User" ("Created");|duration: 
2025-07-08 11:16:03.0852|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_Email_Active" ON "User" ("Email", "Active");|duration: 
2025-07-08 11:16:03.0852|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_IsDeleted" ON "User" ("IsDeleted");|duration: 
2025-07-08 11:16:03.0852|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_Name" ON "User" ("Name");|duration: 
2025-07-08 11:16:03.0852|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_Name_Created" ON "User" ("Name", "Created");|duration: 
2025-07-08 11:16:03.0852|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_RoleId" ON "User" ("RoleId");|duration: 
2025-07-08 11:16:03.1009|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_RoleId_Active" ON "User" ("RoleId", "Active");|duration: 
2025-07-08 11:16:03.1009|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE UNIQUE INDEX "UQ_User_Email_NotDeleted" ON "User" ("Email") WHERE [IsDeleted] = 0;|duration: 
2025-07-08 11:16:03.1009|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE UNIQUE INDEX "UQ_User_EmployeeNumber_NotDeleted" ON "User" ("EmployeeNumber") WHERE [IsDeleted] = 0;|duration: 
2025-07-08 11:16:03.1196|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Program|Base de datos inicializada correctamente|duration: 
2025-07-08 11:16:03.1349|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|Inicializando base de datos con datos de prueba...|duration: 
2025-07-08 11:16:03.3503|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (2ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM "Role" AS "r")|duration: 
2025-07-08 11:16:03.5552|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (5ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (Size = 6), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (Size = 45), @p5='?' (Size = 5), @p6='?' (DbType = DateTime), @p7='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "Role" ("Created", "CreatedBy", "Deleted", "DeletedBy", "Description", "Name", "Updated", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 11:16:03.5870|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|Rol Admin creado exitosamente|duration: 
2025-07-08 11:16:04.0253|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (Size = 6), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (Size = 27), @p5='?' (Size = 6), @p6='?' (Size = 17), @p7='?' (Size = 60), @p8='?' (DbType = Int32), @p9='?' (DbType = DateTime), @p10='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "User" ("Created", "CreatedBy", "Deleted", "DeletedBy", "Email", "EmployeeNumber", "Name", "Password", "RoleId", "Updated", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 11:16:04.0383|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|Usuario administrador creado exitosamente|duration: 
2025-07-08 11:16:04.0383|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|Inicialización de base de datos completada exitosamente|duration: 
2025-07-08 11:16:04.0383|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Program|Datos de prueba inicializados correctamente|duration: 
2025-07-08 11:16:04.1393|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Program|Aplicación iniciada correctamente|duration: 
2025-07-08 11:16:04.1986|INFO|Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager|User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.|duration: 
2025-07-08 11:16:04.2615|WARN|Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware|The WebRootPath was not found: C:\Repositories\WebProjects\BitacoraResiduosESH\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend.Backend.WebAPI\wwwroot. Static files may be unavailable.|duration: 
2025-07-08 11:16:04.3408|INFO|Microsoft.Hosting.Lifetime|Now listening on: http://localhost:5001|duration: 
2025-07-08 11:16:04.3408|INFO|Microsoft.Hosting.Lifetime|Application started. Press Ctrl+C to shut down.|duration: 
2025-07-08 11:16:04.3506|INFO|Microsoft.Hosting.Lifetime|Hosting environment: Development|duration: 
2025-07-08 11:16:04.3506|INFO|Microsoft.Hosting.Lifetime|Content root path: C:\Repositories\WebProjects\BitacoraResiduosESH\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend.Backend.WebAPI|duration: 
2025-07-08 11:16:11.6453|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar - - -|duration: 9.148
2025-07-08 11:16:11.8665|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 231.199
2025-07-08 11:16:11.8976|INFO|Microsoft.AspNetCore.Http.Result.RedirectResult|Executing RedirectResult, redirecting to scalar/.|duration: 263.093
2025-07-08 11:16:11.8976|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 264.651
2025-07-08 11:16:11.9227|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar - 302 0 - 274.0629ms|duration: 285.452
2025-07-08 11:16:11.9227|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar/ - - -|duration: 0.435
2025-07-08 11:16:11.9289|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 9.587
2025-07-08 11:16:12.1012|INFO|Microsoft.AspNetCore.Http.Result.ContentResult|Write content with HTTP Response ContentType of text/html|duration: 173.383
2025-07-08 11:16:12.1012|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 174.936
2025-07-08 11:16:12.1012|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar/ - 200 924 text/html 175.7539ms|duration: 176.258
2025-07-08 11:16:12.1238|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar/scalar.js - - -|duration: 0.299
2025-07-08 11:16:12.1238|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar/scalar.aspnetcore.js - - -|duration: 0.341
2025-07-08 11:16:12.1321|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/scalar.js'|duration: 9.052
2025-07-08 11:16:12.1321|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 7.400
2025-07-08 11:16:12.1321|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/scalar.aspnetcore.js'|duration: 9.634
2025-07-08 11:16:12.1639|INFO|Microsoft.AspNetCore.Http.Result.StatusCodeResult|Setting HTTP status code 304.|duration: 44.999
2025-07-08 11:16:12.1639|INFO|Microsoft.AspNetCore.Http.Result.StatusCodeResult|Setting HTTP status code 304.|duration: 41.714
2025-07-08 11:16:12.1639|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/scalar.js'|duration: 45.776
2025-07-08 11:16:12.1639|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/scalar.aspnetcore.js'|duration: 42.521
2025-07-08 11:16:12.1639|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar/scalar.js - 304 - - 46.6839ms|duration: 47.111
2025-07-08 11:16:12.1639|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar/scalar.aspnetcore.js - 304 - - 45.3114ms|duration: 45.624
2025-07-08 11:16:12.4439|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/openapi/v1.json - - -|duration: 0.297
2025-07-08 11:16:12.4446|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /openapi/{documentName}.json'|duration: 3.904
2025-07-08 11:16:13.2790|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /openapi/{documentName}.json'|duration: 835.818
2025-07-08 11:16:13.2790|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/openapi/v1.json - 200 - application/json;charset=utf-8 842.2845ms|duration: 842.602
2025-07-08 11:17:16.4341|INFO|Microsoft.Hosting.Lifetime|Application is shutting down...|duration: 
2025-07-08 11:17:33.0164|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (19ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;|duration: 
2025-07-08 11:17:33.0247|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Program|Base de datos inicializada correctamente|duration: 
2025-07-08 11:17:33.0428|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|Inicializando base de datos con datos de prueba...|duration: 
2025-07-08 11:17:33.2748|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (2ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM "Role" AS "r")|duration: 
2025-07-08 11:17:33.2748|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|La base de datos ya contiene datos. Saltando inicialización.|duration: 
2025-07-08 11:17:33.2901|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Program|Datos de prueba inicializados correctamente|duration: 
2025-07-08 11:17:33.3883|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Program|Aplicación iniciada correctamente|duration: 
2025-07-08 11:17:33.4607|INFO|Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager|User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.|duration: 
2025-07-08 11:17:33.5228|WARN|Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware|The WebRootPath was not found: C:\Repositories\WebProjects\BitacoraResiduosESH\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend.Backend.WebAPI\wwwroot. Static files may be unavailable.|duration: 
2025-07-08 11:17:33.6190|INFO|Microsoft.Hosting.Lifetime|Now listening on: http://localhost:5001|duration: 
2025-07-08 11:17:33.6190|INFO|Microsoft.Hosting.Lifetime|Application started. Press Ctrl+C to shut down.|duration: 
2025-07-08 11:17:33.6190|INFO|Microsoft.Hosting.Lifetime|Hosting environment: Development|duration: 
2025-07-08 11:17:33.6190|INFO|Microsoft.Hosting.Lifetime|Content root path: C:\Repositories\WebProjects\BitacoraResiduosESH\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend.Backend.WebAPI|duration: 
2025-07-08 11:17:34.0523|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar - - -|duration: 3.721
2025-07-08 11:17:34.1233|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 77.001
2025-07-08 11:17:34.1333|INFO|Microsoft.AspNetCore.Http.Result.RedirectResult|Executing RedirectResult, redirecting to scalar/.|duration: 87.095
2025-07-08 11:17:34.1333|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 87.622
2025-07-08 11:17:34.1333|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar - 302 0 - 89.5524ms|duration: 93.696
2025-07-08 11:17:34.1333|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar/ - - -|duration: 0.193
2025-07-08 11:17:34.1477|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 2.750
2025-07-08 11:17:34.1996|INFO|Microsoft.AspNetCore.Http.Result.ContentResult|Write content with HTTP Response ContentType of text/html|duration: 61.192
2025-07-08 11:17:34.1996|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 61.923
2025-07-08 11:17:34.1996|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar/ - 200 924 text/html 62.1643ms|duration: 62.361
2025-07-08 11:17:34.2357|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar/scalar.js - - -|duration: 0.188
2025-07-08 11:17:34.2357|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar/scalar.aspnetcore.js - - -|duration: 0.182
2025-07-08 11:17:34.2357|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/scalar.js'|duration: 4.416
2025-07-08 11:17:34.2357|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 4.654
2025-07-08 11:17:34.2357|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/scalar.aspnetcore.js'|duration: 5.263
2025-07-08 11:17:34.2472|INFO|Microsoft.AspNetCore.Http.Result.StatusCodeResult|Setting HTTP status code 304.|duration: 19.419
2025-07-08 11:17:34.2472|INFO|Microsoft.AspNetCore.Http.Result.StatusCodeResult|Setting HTTP status code 304.|duration: 19.258
2025-07-08 11:17:34.2472|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/scalar.js'|duration: 19.876
2025-07-08 11:17:34.2472|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/scalar.aspnetcore.js'|duration: 19.953
2025-07-08 11:17:34.2472|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar/scalar.js - 304 - - 20.4370ms|duration: 20.642
2025-07-08 11:17:34.2472|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar/scalar.aspnetcore.js - 304 - - 21.0744ms|duration: 21.204
2025-07-08 11:17:34.7539|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/openapi/v1.json - - -|duration: 0.181
2025-07-08 11:17:34.7539|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /openapi/{documentName}.json'|duration: 2.031
2025-07-08 11:17:35.0232|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /openapi/{documentName}.json'|duration: 269.443
2025-07-08 11:17:35.0232|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/openapi/v1.json - 200 - application/json;charset=utf-8 271.2033ms|duration: 271.379
2025-07-08 11:17:37.0998|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar/ - - -|duration: 0.236
2025-07-08 11:17:37.1009|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 2.013
2025-07-08 11:17:37.1009|INFO|Microsoft.AspNetCore.Http.Result.ContentResult|Write content with HTTP Response ContentType of text/html|duration: 2.604
2025-07-08 11:17:37.1009|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 2.904
2025-07-08 11:17:37.1009|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar/ - 200 924 text/html 3.4429ms|duration: 3.609
2025-07-08 11:17:37.1277|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar/scalar.js - - -|duration: 0.129
2025-07-08 11:17:37.1277|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar/scalar.aspnetcore.js - - -|duration: 0.139
2025-07-08 11:17:37.1277|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/scalar.js'|duration: 2.883
2025-07-08 11:17:37.1277|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 2.495
2025-07-08 11:17:37.1277|INFO|Microsoft.AspNetCore.Http.Result.StatusCodeResult|Setting HTTP status code 304.|duration: 3.215
2025-07-08 11:17:37.1277|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/scalar.js'|duration: 3.737
2025-07-08 11:17:37.1277|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/scalar.aspnetcore.js'|duration: 3.186
2025-07-08 11:17:37.1318|INFO|Microsoft.AspNetCore.Http.Result.StatusCodeResult|Setting HTTP status code 304.|duration: 3.593
2025-07-08 11:17:37.1318|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar/scalar.js - 304 - - 4.2837ms|duration: 4.425
2025-07-08 11:17:37.1318|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/scalar.aspnetcore.js'|duration: 3.852
2025-07-08 11:17:37.1318|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar/scalar.aspnetcore.js - 304 - - 4.2401ms|duration: 4.356
2025-07-08 11:17:37.2400|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/openapi/v1.json - - -|duration: 0.141
2025-07-08 11:17:37.2400|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /openapi/{documentName}.json'|duration: 1.319
2025-07-08 11:17:37.3034|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /openapi/{documentName}.json'|duration: 63.462
2025-07-08 11:17:37.3034|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/openapi/v1.json - 200 - application/json;charset=utf-8 63.8605ms|duration: 63.976
2025-07-08 11:17:44.1910|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/api/BitacoraEntry - - -|duration: 0.406
2025-07-08 11:17:44.1910|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 2.450
2025-07-08 11:17:44.2125|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Route matched with {action = "GetAll", controller = "BitacoraEntry"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[BitacoraResiduosESH.Backend.Backend.Application.Common.PagedResponse`1[BitacoraResiduosESH.Backend.Backend.Application.DTOs.BitacoraEntry.BitacoraEntryDto]]] GetAll(Int32, Int32, Boolean) on controller BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController (BitacoraResiduosESH.Backend.Backend.WebAPI).|duration: 29.532
2025-07-08 11:17:44.2741|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executing action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI) - Validation state: Valid|duration: 83.216
2025-07-08 11:17:44.3522|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (4ms) [Parameters=[@__filter_IncludeDeleted_0='?' (DbType = Boolean)], CommandType='Text', CommandTimeout='30']
SELECT COUNT(*)
FROM "BitacoraEntries" AS "b"
WHERE "b"."IsDeleted" = @__filter_IncludeDeleted_0|duration: 168.863
2025-07-08 11:17:44.4481|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[@__filter_IncludeDeleted_0='?' (DbType = Boolean), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
SELECT "b0"."Id", "b0"."Active", "b0"."AreaId", "b0"."ContainerTypeId", "b0"."Created", "b0"."CreatedBy", "b0"."Deleted", "b0"."DeletedBy", "b0"."DepartureDate", "b0"."Description", "b0"."EnteredBy", "b0"."EntryDate", "b0"."GrossWeight", "b0"."IsDeleted", "b0"."Name", "b0"."NetWeightKG", "b0"."NetWeightLB", "b0"."Tare", "b0"."UnitPrice", "b0"."Updated", "b0"."UpdatedBy", "b0"."WasteTypeId", "w"."Id", "w"."Active", "w"."Created", "w"."CreatedBy", "w"."Deleted", "w"."DeletedBy", "w"."Description", "w"."IsDeleted", "w"."Name", "w"."Updated", "w"."UpdatedBy", "c"."Id", "c"."Active", "c"."Created", "c"."CreatedBy", "c"."Deleted", "c"."DeletedBy", "c"."Description", "c"."IsDeleted", "c"."Name", "c"."Updated", "c"."UpdatedBy", "a"."Id", "a"."Active", "a"."Created", "a"."CreatedBy", "a"."Deleted", "a"."DeletedBy", "a"."Description", "a"."IsDeleted", "a"."Name", "a"."Updated", "a"."UpdatedBy"
FROM (
    SELECT "b"."Id", "b"."Active", "b"."AreaId", "b"."ContainerTypeId", "b"."Created", "b"."CreatedBy", "b"."Deleted", "b"."DeletedBy", "b"."DepartureDate", "b"."Description", "b"."EnteredBy", "b"."EntryDate", "b"."GrossWeight", "b"."IsDeleted", "b"."Name", "b"."NetWeightKG", "b"."NetWeightLB", "b"."Tare", "b"."UnitPrice", "b"."Updated", "b"."UpdatedBy", "b"."WasteTypeId"
    FROM "BitacoraEntries" AS "b"
    WHERE "b"."IsDeleted" = @__filter_IncludeDeleted_0
    ORDER BY "b"."Id"
    LIMIT @__p_2 OFFSET @__p_1
) AS "b0"
INNER JOIN "WasteType" AS "w" ON "b0"."WasteTypeId" = "w"."Id"
INNER JOIN "ContainerTypes" AS "c" ON "b0"."ContainerTypeId" = "c"."Id"
INNER JOIN "Areas" AS "a" ON "b0"."AreaId" = "a"."Id"
ORDER BY "b0"."Id"|duration: 258.945
2025-07-08 11:17:44.4481|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 178.6662ms.|duration: 265.841
2025-07-08 11:17:44.4604|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor|Executing OkObjectResult, writing value of type 'BitacoraResiduosESH.Backend.Backend.Application.Common.PagedResponse`1[[BitacoraResiduosESH.Backend.Backend.Application.DTOs.BitacoraEntry.BitacoraEntryDto, BitacoraResiduosESH.Backend.Backend.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]'.|duration: 281.283
2025-07-08 11:17:44.4767|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI) in 255.3242ms|duration: 288.604
2025-07-08 11:17:44.4767|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 288.992
2025-07-08 11:17:44.4767|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/api/BitacoraEntry - 200 - application/json;+charset=utf-8 292.3725ms|duration: 292.465
2025-07-08 11:20:27.2302|INFO|Microsoft.Hosting.Lifetime|Application is shutting down...|duration: 
2025-07-08 11:21:13.0560|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (15ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;|duration: 
2025-07-08 11:21:13.0699|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Program|Base de datos inicializada correctamente|duration: 
2025-07-08 11:21:13.0699|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|Inicializando base de datos con datos de prueba...|duration: 
2025-07-08 11:21:13.3195|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (3ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM "Role" AS "r")|duration: 
2025-07-08 11:21:13.3195|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|La base de datos ya contiene datos. Saltando inicialización.|duration: 
2025-07-08 11:21:13.3354|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Program|Datos de prueba inicializados correctamente|duration: 
2025-07-08 11:21:13.4378|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Program|Aplicación iniciada correctamente|duration: 
2025-07-08 11:21:13.4929|INFO|Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager|User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.|duration: 
2025-07-08 11:21:13.5539|WARN|Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware|The WebRootPath was not found: C:\Repositories\WebProjects\BitacoraResiduosESH\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend.Backend.WebAPI\wwwroot. Static files may be unavailable.|duration: 
2025-07-08 11:21:13.6360|INFO|Microsoft.Hosting.Lifetime|Now listening on: http://localhost:5001|duration: 
2025-07-08 11:21:13.6360|INFO|Microsoft.Hosting.Lifetime|Application started. Press Ctrl+C to shut down.|duration: 
2025-07-08 11:21:13.6360|INFO|Microsoft.Hosting.Lifetime|Hosting environment: Development|duration: 
2025-07-08 11:21:13.6474|INFO|Microsoft.Hosting.Lifetime|Content root path: C:\Repositories\WebProjects\BitacoraResiduosESH\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend.Backend.WebAPI|duration: 
2025-07-08 11:21:14.1325|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar - - -|duration: 3.328
2025-07-08 11:21:14.1832|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 56.307
2025-07-08 11:21:14.1953|INFO|Microsoft.AspNetCore.Http.Result.RedirectResult|Executing RedirectResult, redirecting to scalar/.|duration: 70.877
2025-07-08 11:21:14.1953|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 71.264
2025-07-08 11:21:14.1953|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar - 302 0 - 73.7376ms|duration: 77.177
2025-07-08 11:21:14.1953|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar/ - - -|duration: 0.156
2025-07-08 11:21:14.2107|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 2.893
2025-07-08 11:21:14.2511|INFO|Microsoft.AspNetCore.Http.Result.ContentResult|Write content with HTTP Response ContentType of text/html|duration: 46.938
2025-07-08 11:21:14.2511|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 47.730
2025-07-08 11:21:14.2567|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar/ - 200 924 text/html 48.0109ms|duration: 48.230
2025-07-08 11:21:14.2567|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar/scalar.js - - -|duration: 0.144
2025-07-08 11:21:14.2567|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar/scalar.aspnetcore.js - - -|duration: 0.096
2025-07-08 11:21:14.2721|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/scalar.js'|duration: 3.255
2025-07-08 11:21:14.2721|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 3.685
2025-07-08 11:21:14.2721|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/scalar.aspnetcore.js'|duration: 4.264
2025-07-08 11:21:14.2721|INFO|Microsoft.AspNetCore.Http.Result.StatusCodeResult|Setting HTTP status code 304.|duration: 15.182
2025-07-08 11:21:14.2721|INFO|Microsoft.AspNetCore.Http.Result.StatusCodeResult|Setting HTTP status code 304.|duration: 15.042
2025-07-08 11:21:14.2721|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/scalar.aspnetcore.js'|duration: 15.356
2025-07-08 11:21:14.2721|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/scalar.js'|duration: 15.608
2025-07-08 11:21:14.2721|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar/scalar.js - 304 - - 16.0015ms|duration: 16.214
2025-07-08 11:21:14.2721|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar/scalar.aspnetcore.js - 304 - - 16.4866ms|duration: 16.588
2025-07-08 11:21:14.4634|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/openapi/v1.json - - -|duration: 0.305
2025-07-08 11:21:14.4634|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /openapi/{documentName}.json'|duration: 1.440
2025-07-08 11:21:14.6635|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /openapi/{documentName}.json'|duration: 200.296
2025-07-08 11:21:14.6635|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/openapi/v1.json - 200 - application/json;charset=utf-8 202.2771ms|duration: 202.454
2025-07-08 11:22:26.8862|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 POST http://localhost:5001/api/Areas - application/json 39|duration: 0.380
2025-07-08 11:22:26.8862|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 2.729
2025-07-08 11:22:26.8862|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.Create (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 3.569
2025-07-08 11:22:26.9140|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Route matched with {action = "Create", controller = "Areas"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[BitacoraResiduosESH.Backend.Backend.Application.DTOs.Area.AreaDto]] Create(BitacoraResiduosESH.Backend.Backend.Application.DTOs.Area.CreateAreaDto) on controller BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController (BitacoraResiduosESH.Backend.Backend.WebAPI).|duration: 27.967
2025-07-08 11:22:26.9796|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executing action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.Create (BitacoraResiduosESH.Backend.Backend.WebAPI) - Validation state: Valid|duration: 93.483
2025-07-08 11:22:27.0711|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (4ms) [Parameters=[@__ToLower_0='?' (Size = 4)], CommandType='Text', CommandTimeout='30']
SELECT COUNT(*)
FROM "Areas" AS "a"
WHERE NOT ("a"."IsDeleted") AND instr(lower("a"."Name"), @__ToLower_0) > 0|duration: 189.793
2025-07-08 11:22:27.1208|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[@__ToLower_0='?' (Size = 4), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
SELECT "a"."Id", "a"."Active", "a"."Created", "a"."CreatedBy", "a"."Deleted", "a"."DeletedBy", "a"."Description", "a"."IsDeleted", "a"."Name", "a"."Updated", "a"."UpdatedBy"
FROM "Areas" AS "a"
WHERE NOT ("a"."IsDeleted") AND instr(lower("a"."Name"), @__ToLower_0) > 0
ORDER BY "a"."Id"
LIMIT @__p_2 OFFSET @__p_1|duration: 237.014
2025-07-08 11:22:27.2591|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (Size = 6), @p2='?' (DbType = DateTime), @p3='?', @p4='?', @p5='?' (Size = 4), @p6='?' (DbType = DateTime), @p7='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "Areas" ("Created", "CreatedBy", "Deleted", "DeletedBy", "Description", "Name", "Updated", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING "Id", "Active", "IsDeleted";|duration: 376.692
2025-07-08 11:22:27.2916|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Repositories.GenericRepository`1|Entidad "Area" agregada exitosamente con ID: 1|duration: 406.680
2025-07-08 11:22:27.2916|INFO|BitacoraResiduosESH.Backend.Backend.Application.Services.SimpleEntityService`5|"Area" creada exitosamente con ID: 1 por usuario: "system"|duration: 410.886
2025-07-08 11:22:27.2916|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5|"Area" creado exitosamente con ID: 1 por usuario: "system"|duration: 410.946
2025-07-08 11:22:27.2916|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.Create (BitacoraResiduosESH.Backend.Backend.WebAPI), returned result Microsoft.AspNetCore.Mvc.CreatedAtActionResult in 317.626ms.|duration: 414.977
2025-07-08 11:22:27.3046|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor|Executing CreatedAtActionResult, writing value of type 'BitacoraResiduosESH.Backend.Backend.Application.DTOs.Area.AreaDto'.|duration: 431.478
2025-07-08 11:22:27.3505|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.Create (BitacoraResiduosESH.Backend.Backend.WebAPI) in 432.1573ms|duration: 464.467
2025-07-08 11:22:27.3505|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.Create (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 464.792
2025-07-08 11:22:27.3505|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 POST http://localhost:5001/api/Areas - 201 - application/json;+charset=utf-8 468.3801ms|duration: 468.509
2025-07-08 11:22:44.0327|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 POST http://localhost:5001/api/ContainerTypes - application/json 39|duration: 0.305
2025-07-08 11:22:44.0327|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 2.332
2025-07-08 11:22:44.0327|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.ContainerTypesController.Create (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 2.834
2025-07-08 11:22:44.0327|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Route matched with {action = "Create", controller = "ContainerTypes"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[BitacoraResiduosESH.Backend.Backend.Application.DTOs.ContainerType.ContainerTypeDto]] Create(BitacoraResiduosESH.Backend.Backend.Application.DTOs.ContainerType.CreateContainerTypeDto) on controller BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.ContainerTypesController (BitacoraResiduosESH.Backend.Backend.WebAPI).|duration: 5.700
2025-07-08 11:22:44.0388|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executing action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.ContainerTypesController.Create (BitacoraResiduosESH.Backend.Backend.WebAPI) - Validation state: Valid|duration: 13.554
2025-07-08 11:22:44.0547|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[@__ToLower_0='?' (Size = 4)], CommandType='Text', CommandTimeout='30']
SELECT COUNT(*)
FROM "ContainerTypes" AS "c"
WHERE NOT ("c"."IsDeleted") AND instr(lower("c"."Name"), @__ToLower_0) > 0|duration: 26.195
2025-07-08 11:22:44.0547|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[@__ToLower_0='?' (Size = 4), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
SELECT "c"."Id", "c"."Active", "c"."Created", "c"."CreatedBy", "c"."Deleted", "c"."DeletedBy", "c"."Description", "c"."IsDeleted", "c"."Name", "c"."Updated", "c"."UpdatedBy"
FROM "ContainerTypes" AS "c"
WHERE NOT ("c"."IsDeleted") AND instr(lower("c"."Name"), @__ToLower_0) > 0
ORDER BY "c"."Id"
LIMIT @__p_2 OFFSET @__p_1|duration: 37.211
2025-07-08 11:22:44.0866|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (Size = 6), @p2='?' (DbType = DateTime), @p3='?', @p4='?', @p5='?' (Size = 4), @p6='?' (DbType = DateTime), @p7='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "ContainerTypes" ("Created", "CreatedBy", "Deleted", "DeletedBy", "Description", "Name", "Updated", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING "Id", "Active", "IsDeleted";|duration: 58.308
2025-07-08 11:22:44.0866|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Repositories.GenericRepository`1|Entidad "ContainerType" agregada exitosamente con ID: 1|duration: 65.432
2025-07-08 11:22:44.0866|INFO|BitacoraResiduosESH.Backend.Backend.Application.Services.SimpleEntityService`5|"ContainerType" creada exitosamente con ID: 1 por usuario: "system"|duration: 65.624
2025-07-08 11:22:44.0866|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5|"ContainerType" creado exitosamente con ID: 1 por usuario: "system"|duration: 65.652
2025-07-08 11:22:44.0866|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.ContainerTypesController.Create (BitacoraResiduosESH.Backend.Backend.WebAPI), returned result Microsoft.AspNetCore.Mvc.CreatedAtActionResult in 52.1523ms.|duration: 65.862
2025-07-08 11:22:44.0866|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor|Executing CreatedAtActionResult, writing value of type 'BitacoraResiduosESH.Backend.Backend.Application.DTOs.ContainerType.ContainerTypeDto'.|duration: 66.716
2025-07-08 11:22:44.1010|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.ContainerTypesController.Create (BitacoraResiduosESH.Backend.Backend.WebAPI) in 61.2633ms|duration: 68.465
2025-07-08 11:22:44.1010|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.ContainerTypesController.Create (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 68.729
2025-07-08 11:22:44.1010|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 POST http://localhost:5001/api/ContainerTypes - 201 - application/json;+charset=utf-8 70.8037ms|duration: 70.909
2025-07-08 11:22:57.5925|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 POST http://localhost:5001/api/WasteType - application/json 39|duration: 0.257
2025-07-08 11:22:57.5925|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 2.607
2025-07-08 11:22:57.5925|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.WasteTypeController.Create (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 3.239
2025-07-08 11:22:57.5925|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Route matched with {action = "Create", controller = "WasteType"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[BitacoraResiduosESH.Backend.Backend.Application.DTOs.WasteType.WasteTypeDto]] Create(BitacoraResiduosESH.Backend.Backend.Application.DTOs.WasteType.CreateWasteTypeDto) on controller BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.WasteTypeController (BitacoraResiduosESH.Backend.Backend.WebAPI).|duration: 5.697
2025-07-08 11:22:57.6013|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executing action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.WasteTypeController.Create (BitacoraResiduosESH.Backend.Backend.WebAPI) - Validation state: Valid|duration: 13.618
2025-07-08 11:22:57.6194|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[@__ToLower_0='?' (Size = 4)], CommandType='Text', CommandTimeout='30']
SELECT COUNT(*)
FROM "WasteType" AS "w"
WHERE NOT ("w"."IsDeleted") AND instr(lower("w"."Name"), @__ToLower_0) > 0|duration: 33.465
2025-07-08 11:22:57.6328|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[@__ToLower_0='?' (Size = 4), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
SELECT "w"."Id", "w"."Active", "w"."Created", "w"."CreatedBy", "w"."Deleted", "w"."DeletedBy", "w"."Description", "w"."IsDeleted", "w"."Name", "w"."Updated", "w"."UpdatedBy"
FROM "WasteType" AS "w"
WHERE NOT ("w"."IsDeleted") AND instr(lower("w"."Name"), @__ToLower_0) > 0
ORDER BY "w"."Id"
LIMIT @__p_2 OFFSET @__p_1|duration: 41.332
2025-07-08 11:22:57.6494|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = Boolean), @p1='?' (DbType = DateTime), @p2='?' (Size = 6), @p3='?' (DbType = DateTime), @p4='?', @p5='?', @p6='?' (DbType = Boolean), @p7='?' (Size = 4), @p8='?' (DbType = DateTime), @p9='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "WasteType" ("Active", "Created", "CreatedBy", "Deleted", "DeletedBy", "Description", "IsDeleted", "Name", "Updated", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9)
RETURNING "Id";|duration: 59.777
2025-07-08 11:22:57.6494|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Repositories.GenericRepository`1|Entidad "WasteType" agregada exitosamente con ID: 1|duration: 63.688
2025-07-08 11:22:57.6494|INFO|BitacoraResiduosESH.Backend.Backend.Application.Services.SimpleEntityService`5|"WasteType" creada exitosamente con ID: 1 por usuario: "system"|duration: 63.866
2025-07-08 11:22:57.6494|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5|"WasteType" creado exitosamente con ID: 1 por usuario: "system"|duration: 63.899
2025-07-08 11:22:57.6494|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.WasteTypeController.Create (BitacoraResiduosESH.Backend.Backend.WebAPI), returned result Microsoft.AspNetCore.Mvc.CreatedAtActionResult in 50.1743ms.|duration: 64.068
2025-07-08 11:22:57.6494|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor|Executing CreatedAtActionResult, writing value of type 'BitacoraResiduosESH.Backend.Backend.Application.DTOs.WasteType.WasteTypeDto'.|duration: 64.651
2025-07-08 11:22:57.6494|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.WasteTypeController.Create (BitacoraResiduosESH.Backend.Backend.WebAPI) in 58.5185ms|duration: 66.576
2025-07-08 11:22:57.6494|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.WasteTypeController.Create (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 66.798
2025-07-08 11:22:57.6494|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 POST http://localhost:5001/api/WasteType - 201 - application/json;+charset=utf-8 67.6684ms|duration: 67.766
2025-07-08 11:23:57.0977|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 POST http://localhost:5001/api/BitacoraEntry - application/json 248|duration: 0.270
2025-07-08 11:23:57.0977|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 2.562
2025-07-08 11:23:57.1020|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController.Create (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 4.499
2025-07-08 11:23:57.1020|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Route matched with {action = "Create", controller = "BitacoraEntry"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[BitacoraResiduosESH.Backend.Backend.Application.DTOs.BitacoraEntry.BitacoraEntryDto]] Create(BitacoraResiduosESH.Backend.Backend.Application.DTOs.BitacoraEntry.CreateBitacoraEntryDto) on controller BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController (BitacoraResiduosESH.Backend.Backend.WebAPI).|duration: 8.619
2025-07-08 11:23:57.1164|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor|Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.|duration: 21.902
2025-07-08 11:23:57.1164|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController.Create (BitacoraResiduosESH.Backend.Backend.WebAPI) in 17.376ms|duration: 27.114
2025-07-08 11:23:57.1164|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController.Create (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 27.980
2025-07-08 11:23:57.1164|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 POST http://localhost:5001/api/BitacoraEntry - 400 - application/json;+charset=utf-8 28.3531ms|duration: 28.514
2025-07-08 11:25:26.5351|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 POST http://localhost:5001/api/BitacoraEntry - application/json 258|duration: 1.117
2025-07-08 11:25:26.5389|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 4.148
2025-07-08 11:25:26.5389|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController.Create (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 5.095
2025-07-08 11:25:26.5389|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Route matched with {action = "Create", controller = "BitacoraEntry"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[BitacoraResiduosESH.Backend.Backend.Application.DTOs.BitacoraEntry.BitacoraEntryDto]] Create(BitacoraResiduosESH.Backend.Backend.Application.DTOs.BitacoraEntry.CreateBitacoraEntryDto) on controller BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController (BitacoraResiduosESH.Backend.Backend.WebAPI).|duration: 5.653
2025-07-08 11:25:26.5546|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor|Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.|duration: 26.250
2025-07-08 11:25:26.5546|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController.Create (BitacoraResiduosESH.Backend.Backend.WebAPI) in 17.3856ms|duration: 27.711
2025-07-08 11:25:26.5546|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController.Create (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 28.480
2025-07-08 11:25:26.5546|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 POST http://localhost:5001/api/BitacoraEntry - 400 - application/json;+charset=utf-8 29.7651ms|duration: 30.082
2025-07-08 11:25:28.7137|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 POST http://localhost:5001/api/BitacoraEntry - application/json 258|duration: 0.548
2025-07-08 11:25:28.7137|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 5.060
2025-07-08 11:25:28.7137|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController.Create (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 6.370
2025-07-08 11:25:28.7137|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Route matched with {action = "Create", controller = "BitacoraEntry"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[BitacoraResiduosESH.Backend.Backend.Application.DTOs.BitacoraEntry.BitacoraEntryDto]] Create(BitacoraResiduosESH.Backend.Backend.Application.DTOs.BitacoraEntry.CreateBitacoraEntryDto) on controller BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController (BitacoraResiduosESH.Backend.Backend.WebAPI).|duration: 7.066
2025-07-08 11:25:28.7417|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor|Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.|duration: 33.679
2025-07-08 11:25:28.7417|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController.Create (BitacoraResiduosESH.Backend.Backend.WebAPI) in 22.4969ms|duration: 34.776
2025-07-08 11:25:28.7417|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController.Create (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 35.442
2025-07-08 11:25:28.7417|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 POST http://localhost:5001/api/BitacoraEntry - 400 - application/json;+charset=utf-8 36.6712ms|duration: 36.876
2025-07-08 11:25:29.2991|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 POST http://localhost:5001/api/BitacoraEntry - application/json 258|duration: 0.303
2025-07-08 11:25:29.2991|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 3.605
2025-07-08 11:25:29.2991|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController.Create (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 4.388
2025-07-08 11:25:29.3038|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Route matched with {action = "Create", controller = "BitacoraEntry"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[BitacoraResiduosESH.Backend.Backend.Application.DTOs.BitacoraEntry.BitacoraEntryDto]] Create(BitacoraResiduosESH.Backend.Backend.Application.DTOs.BitacoraEntry.CreateBitacoraEntryDto) on controller BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController (BitacoraResiduosESH.Backend.Backend.WebAPI).|duration: 4.948
2025-07-08 11:25:29.3200|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor|Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.|duration: 21.734
2025-07-08 11:25:29.3200|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController.Create (BitacoraResiduosESH.Backend.Backend.WebAPI) in 13.8058ms|duration: 22.773
2025-07-08 11:25:29.3200|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController.Create (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 23.581
2025-07-08 11:25:29.3200|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 POST http://localhost:5001/api/BitacoraEntry - 400 - application/json;+charset=utf-8 24.7828ms|duration: 25.001
2025-07-08 11:41:25.7986|INFO|Microsoft.Hosting.Lifetime|Application is shutting down...|duration: 
2025-07-08 11:42:51.9112|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (12ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;|duration: 
2025-07-08 11:42:51.9160|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Program|Base de datos inicializada correctamente|duration: 
2025-07-08 11:42:51.9160|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|Inicializando base de datos con datos de prueba...|duration: 
2025-07-08 11:42:52.1174|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (2ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM "Role" AS "r")|duration: 
2025-07-08 11:42:52.1174|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|La base de datos ya contiene datos. Saltando inicialización.|duration: 
2025-07-08 11:42:52.1174|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Program|Datos de prueba inicializados correctamente|duration: 
2025-07-08 11:42:52.2136|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Program|Aplicación iniciada correctamente|duration: 
2025-07-08 11:42:52.2569|INFO|Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager|User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.|duration: 
2025-07-08 11:42:52.3059|WARN|Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware|The WebRootPath was not found: C:\Repositories\WebProjects\BitacoraResiduosESH\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend.Backend.WebAPI\wwwroot. Static files may be unavailable.|duration: 
2025-07-08 11:42:52.3822|INFO|Microsoft.Hosting.Lifetime|Now listening on: http://localhost:5001|duration: 
2025-07-08 11:42:52.3822|INFO|Microsoft.Hosting.Lifetime|Application started. Press Ctrl+C to shut down.|duration: 
2025-07-08 11:42:52.3822|INFO|Microsoft.Hosting.Lifetime|Hosting environment: Development|duration: 
2025-07-08 11:42:52.3822|INFO|Microsoft.Hosting.Lifetime|Content root path: C:\Repositories\WebProjects\BitacoraResiduosESH\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend.Backend.WebAPI|duration: 
2025-07-08 11:42:52.4507|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar - - -|duration: 2.412
2025-07-08 11:42:52.5024|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 56.594
2025-07-08 11:42:52.5101|INFO|Microsoft.AspNetCore.Http.Result.RedirectResult|Executing RedirectResult, redirecting to scalar/.|duration: 65.982
2025-07-08 11:42:52.5101|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 66.288
2025-07-08 11:42:52.5101|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar - 302 0 - 69.0892ms|duration: 72.350
2025-07-08 11:42:52.5251|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar/ - - -|duration: 0.167
2025-07-08 11:42:52.5251|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 3.160
2025-07-08 11:42:52.5607|INFO|Microsoft.AspNetCore.Http.Result.ContentResult|Write content with HTTP Response ContentType of text/html|duration: 39.984
2025-07-08 11:42:52.5607|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 40.798
2025-07-08 11:42:52.5607|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar/ - 200 924 text/html 41.4024ms|duration: 41.587
2025-07-08 11:42:52.5789|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar/scalar.js - - -|duration: 0.159
2025-07-08 11:42:52.5789|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar/scalar.aspnetcore.js - - -|duration: 0.146
2025-07-08 11:42:52.5789|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/scalar.js'|duration: 3.494
2025-07-08 11:42:52.5789|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 3.714
2025-07-08 11:42:52.5789|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/scalar.aspnetcore.js'|duration: 4.511
2025-07-08 11:42:52.5895|INFO|Microsoft.AspNetCore.Http.Result.StatusCodeResult|Setting HTTP status code 304.|duration: 15.035
2025-07-08 11:42:52.5895|INFO|Microsoft.AspNetCore.Http.Result.StatusCodeResult|Setting HTTP status code 304.|duration: 14.537
2025-07-08 11:42:52.5895|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/scalar.js'|duration: 15.381
2025-07-08 11:42:52.5895|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/scalar.aspnetcore.js'|duration: 14.880
2025-07-08 11:42:52.5895|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar/scalar.js - 304 - - 15.6922ms|duration: 15.823
2025-07-08 11:42:52.5895|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar/scalar.aspnetcore.js - 304 - - 16.0956ms|duration: 16.181
2025-07-08 11:42:52.7620|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/openapi/v1.json - - -|duration: 0.130
2025-07-08 11:42:52.7620|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /openapi/{documentName}.json'|duration: 1.242
2025-07-08 11:42:52.9666|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /openapi/{documentName}.json'|duration: 204.760
2025-07-08 11:42:52.9666|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/openapi/v1.json - 200 - application/json;charset=utf-8 206.6903ms|duration: 206.825
2025-07-08 11:46:01.8368|INFO|Microsoft.Hosting.Lifetime|Application is shutting down...|duration: 
2025-07-08 12:00:01.9970|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (15ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;|duration: 
2025-07-08 12:00:01.9970|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Program|Base de datos inicializada correctamente|duration: 
2025-07-08 12:00:02.0071|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|Inicializando base de datos con datos de prueba...|duration: 
2025-07-08 12:00:02.2104|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (2ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM "Role" AS "r")|duration: 
2025-07-08 12:00:02.2104|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|La base de datos ya contiene datos. Saltando inicialización.|duration: 
2025-07-08 12:00:02.2256|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Program|Datos de prueba inicializados correctamente|duration: 
2025-07-08 12:00:02.3201|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Program|Aplicación iniciada correctamente|duration: 
2025-07-08 12:00:02.3669|INFO|Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager|User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.|duration: 
2025-07-08 12:00:02.4311|WARN|Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware|The WebRootPath was not found: C:\Repositories\WebProjects\BitacoraResiduosESH\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend.Backend.WebAPI\wwwroot. Static files may be unavailable.|duration: 
2025-07-08 12:00:02.4911|INFO|Microsoft.Hosting.Lifetime|Now listening on: http://localhost:5001|duration: 
2025-07-08 12:00:02.4911|INFO|Microsoft.Hosting.Lifetime|Application started. Press Ctrl+C to shut down.|duration: 
2025-07-08 12:00:02.5079|INFO|Microsoft.Hosting.Lifetime|Hosting environment: Development|duration: 
2025-07-08 12:00:02.5079|INFO|Microsoft.Hosting.Lifetime|Content root path: C:\Repositories\WebProjects\BitacoraResiduosESH\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend.Backend.WebAPI|duration: 
2025-07-08 12:00:03.0480|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar - - -|duration: 3.237
2025-07-08 12:00:03.1172|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 72.081
2025-07-08 12:00:03.1172|INFO|Microsoft.AspNetCore.Http.Result.RedirectResult|Executing RedirectResult, redirecting to scalar/.|duration: 82.596
2025-07-08 12:00:03.1172|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 82.942
2025-07-08 12:00:03.1341|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar - 302 0 - 85.0899ms|duration: 88.975
2025-07-08 12:00:03.1341|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar/ - - -|duration: 0.224
2025-07-08 12:00:03.1341|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 3.945
2025-07-08 12:00:03.1826|INFO|Microsoft.AspNetCore.Http.Result.ContentResult|Write content with HTTP Response ContentType of text/html|duration: 50.979
2025-07-08 12:00:03.1826|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 51.540
2025-07-08 12:00:03.1826|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar/ - 200 924 text/html 51.9812ms|duration: 52.176
2025-07-08 12:00:03.2044|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar/scalar.js - - -|duration: 0.375
2025-07-08 12:00:03.2044|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar/scalar.aspnetcore.js - - -|duration: 0.334
2025-07-08 12:00:03.2044|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/scalar.js'|duration: 5.143
2025-07-08 12:00:03.2102|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 5.869
2025-07-08 12:00:03.2102|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/scalar.aspnetcore.js'|duration: 7.155
2025-07-08 12:00:03.2257|INFO|Microsoft.AspNetCore.Http.Result.StatusCodeResult|Setting HTTP status code 304.|duration: 23.206
2025-07-08 12:00:03.2257|INFO|Microsoft.AspNetCore.Http.Result.StatusCodeResult|Setting HTTP status code 304.|duration: 23.359
2025-07-08 12:00:03.2257|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/scalar.aspnetcore.js'|duration: 23.439
2025-07-08 12:00:03.2257|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/scalar.js'|duration: 23.657
2025-07-08 12:00:03.2257|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar/scalar.js - 304 - - 24.1096ms|duration: 24.253
2025-07-08 12:00:03.2257|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar/scalar.aspnetcore.js - 304 - - 24.3562ms|duration: 24.490
2025-07-08 12:00:03.4357|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/openapi/v1.json - - -|duration: 0.222
2025-07-08 12:00:03.4357|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /openapi/{documentName}.json'|duration: 2.126
2025-07-08 12:00:03.7551|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /openapi/{documentName}.json'|duration: 319.636
2025-07-08 12:00:03.7589|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/openapi/v1.json - 200 - application/json;charset=utf-8 323.5847ms|duration: 323.843
2025-07-08 12:07:30.1197|INFO|Microsoft.Hosting.Lifetime|Application is shutting down...|duration: 
2025-07-08 12:10:32.9570|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (54ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;|duration: 
2025-07-08 12:10:32.9771|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Program|Base de datos inicializada correctamente|duration: 
2025-07-08 12:10:33.0285|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|Inicializando base de datos con datos de prueba...|duration: 
2025-07-08 12:10:33.8855|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (7ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM "Role" AS "r")|duration: 
2025-07-08 12:10:33.9310|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|La base de datos ya contiene datos. Saltando inicialización.|duration: 
2025-07-08 12:10:33.9310|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Program|Datos de prueba inicializados correctamente|duration: 
2025-07-08 12:10:34.3093|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Program|Aplicación iniciada correctamente|duration: 
2025-07-08 12:10:34.5060|INFO|Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager|User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.|duration: 
2025-07-08 12:10:34.7262|WARN|Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware|The WebRootPath was not found: C:\Repositories\WebProjects\BitacoraResiduosESH\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend.Backend.WebAPI\wwwroot. Static files may be unavailable.|duration: 
2025-07-08 12:10:35.0245|INFO|Microsoft.Hosting.Lifetime|Now listening on: http://localhost:5001|duration: 
2025-07-08 12:10:35.0245|INFO|Microsoft.Hosting.Lifetime|Application started. Press Ctrl+C to shut down.|duration: 
2025-07-08 12:10:35.0245|INFO|Microsoft.Hosting.Lifetime|Hosting environment: Development|duration: 
2025-07-08 12:10:35.0245|INFO|Microsoft.Hosting.Lifetime|Content root path: C:\Repositories\WebProjects\BitacoraResiduosESH\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend.Backend.WebAPI|duration: 
2025-07-08 12:10:47.7540|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar/ - - -|duration: 7.347
2025-07-08 12:10:47.9470|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 201.112
2025-07-08 12:10:48.1171|INFO|Microsoft.AspNetCore.Http.Result.ContentResult|Write content with HTTP Response ContentType of text/html|duration: 380.362
2025-07-08 12:10:48.1388|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 390.851
2025-07-08 12:10:48.1504|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar/ - 200 924 text/html 393.3700ms|duration: 402.555
2025-07-08 12:10:48.1750|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar/scalar.js - - -|duration: 0.505
2025-07-08 12:10:48.1750|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar/scalar.aspnetcore.js - - -|duration: 0.649
2025-07-08 12:10:48.1789|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/scalar.js'|duration: 9.155
2025-07-08 12:10:48.1789|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 11.151
2025-07-08 12:10:48.1789|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/scalar.aspnetcore.js'|duration: 13.151
2025-07-08 12:10:48.2104|INFO|Microsoft.AspNetCore.Http.Result.StatusCodeResult|Setting HTTP status code 304.|duration: 43.790
2025-07-08 12:10:48.2104|INFO|Microsoft.AspNetCore.Http.Result.StatusCodeResult|Setting HTTP status code 304.|duration: 43.944
2025-07-08 12:10:48.2104|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/scalar.js'|duration: 44.419
2025-07-08 12:10:48.2104|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/scalar.aspnetcore.js'|duration: 44.827
2025-07-08 12:10:48.2104|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar/scalar.js - 304 - - 45.5359ms|duration: 46.023
2025-07-08 12:10:48.2104|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar/scalar.aspnetcore.js - 304 - - 47.0934ms|duration: 47.538
2025-07-08 12:10:48.3717|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/openapi/v1.json - - -|duration: 0.141
2025-07-08 12:10:48.3717|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /openapi/{documentName}.json'|duration: 6.210
2025-07-08 12:10:49.2991|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /openapi/{documentName}.json'|duration: 928.558
2025-07-08 12:10:49.3059|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/openapi/v1.json - 200 - application/json;charset=utf-8 934.8162ms|duration: 935.153
2025-07-08 12:10:54.3641|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/api/BitacoraEntry - - -|duration: 2.907
2025-07-08 12:10:54.3674|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 7.735
2025-07-08 12:10:54.4449|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Route matched with {action = "GetAll", controller = "BitacoraEntry"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[BitacoraResiduosESH.Backend.Backend.Application.Common.PagedResponse`1[BitacoraResiduosESH.Backend.Backend.Application.DTOs.BitacoraEntry.BitacoraEntryDto]]] GetAll(Int32, Int32, Boolean) on controller BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController (BitacoraResiduosESH.Backend.Backend.WebAPI).|duration: 96.066
2025-07-08 12:10:54.6759|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executing action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI) - Validation state: Valid|duration: 314.043
2025-07-08 12:10:55.0068|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (16ms) [Parameters=[@__filter_IncludeDeleted_0='?' (DbType = Boolean)], CommandType='Text', CommandTimeout='30']
SELECT COUNT(*)
FROM "BitacoraEntries" AS "b"
WHERE "b"."IsDeleted" = @__filter_IncludeDeleted_0|duration: 644.210
2025-07-08 12:10:55.4056|ERROR|Microsoft.EntityFrameworkCore.Database.Command|Failed executing DbCommand (10ms) [Parameters=[@__filter_IncludeDeleted_0='?' (DbType = Boolean), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
SELECT "b0"."Id", "b0"."Active", "b0"."AreaId", "b0"."ContainerTypeId", "b0"."Created", "b0"."CreatedBy", "b0"."Deleted", "b0"."DeletedBy", "b0"."DepartureDate", "b0"."Description", "b0"."EnteredBy", "b0"."EntryDate", "b0"."GrossWeight", "b0"."IsDeleted", "b0"."Name", "b0"."NetWeightKG", "b0"."NetWeightLB", "b0"."Tare", "b0"."UnitPrice", "b0"."Updated", "b0"."UpdatedBy", "b0"."WasteTypeId", "w"."Id", "w"."Active", "w"."Created", "w"."CreatedBy", "w"."Deleted", "w"."DeletedBy", "w"."Description", "w"."IsDeleted", "w"."Name", "w"."Updated", "w"."UpdatedBy", "c"."Id", "c"."Active", "c"."Created", "c"."CreatedBy", "c"."Deleted", "c"."DeletedBy", "c"."Description", "c"."IsDeleted", "c"."Name", "c"."Updated", "c"."UpdatedBy", "a"."Id", "a"."Active", "a"."Created", "a"."CreatedBy", "a"."Deleted", "a"."DeletedBy", "a"."Description", "a"."IsDeleted", "a"."Name", "a"."Updated", "a"."UpdatedBy"
FROM (
    SELECT "b"."Id", "b"."Active", "b"."AreaId", "b"."ContainerTypeId", "b"."Created", "b"."CreatedBy", "b"."Deleted", "b"."DeletedBy", "b"."DepartureDate", "b"."Description", "b"."EnteredBy", "b"."EntryDate", "b"."GrossWeight", "b"."IsDeleted", "b"."Name", "b"."NetWeightKG", "b"."NetWeightLB", "b"."Tare", "b"."UnitPrice", "b"."Updated", "b"."UpdatedBy", "b"."WasteTypeId"
    FROM "BitacoraEntries" AS "b"
    WHERE "b"."IsDeleted" = @__filter_IncludeDeleted_0
    ORDER BY "b"."Id"
    LIMIT @__p_2 OFFSET @__p_1
) AS "b0"
INNER JOIN "WasteTypes" AS "w" ON "b0"."WasteTypeId" = "w"."Id"
INNER JOIN "ContainerTypes" AS "c" ON "b0"."ContainerTypeId" = "c"."Id"
INNER JOIN "Areas" AS "a" ON "b0"."AreaId" = "a"."Id"
ORDER BY "b0"."Id"|duration: 1066.995
2025-07-08 12:10:55.4445|ERROR|Microsoft.EntityFrameworkCore.Query|An exception occurred while iterating over the results of a query for context type 'BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext'.
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such table: WasteTypes'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()|duration: 1099.274
2025-07-08 12:10:55.4665|ERROR|BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.GenericController`5|Error al obtener todos los "BitacoraEntry"|duration: 1129.039
2025-07-08 12:10:55.5108|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 816.915ms.|duration: 1147.310
2025-07-08 12:10:55.5697|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor|Executing BadRequestObjectResult, writing value of type 'System.String'.|duration: 1206.151
2025-07-08 12:10:55.5927|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI) in 1111.6645ms|duration: 1229.163
2025-07-08 12:10:55.5927|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 1230.155
2025-07-08 12:10:55.6021|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/api/BitacoraEntry - 400 - application/json;+charset=utf-8 1240.8393ms|duration: 1241.181
2025-07-08 12:11:10.1739|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/api/Areas - - -|duration: 0.829
2025-07-08 12:11:10.1795|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 7.041
2025-07-08 12:11:10.1795|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Route matched with {action = "GetAll", controller = "Areas"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[BitacoraResiduosESH.Backend.Backend.Application.Common.PagedResponse`1[BitacoraResiduosESH.Backend.Backend.Application.DTOs.Area.AreaDto]]] GetAll(Int32, Int32, Boolean) on controller BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController (BitacoraResiduosESH.Backend.Backend.WebAPI).|duration: 19.413
2025-07-08 12:11:10.2103|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executing action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI) - Validation state: Valid|duration: 43.288
2025-07-08 12:11:10.3204|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT COUNT(*)
FROM "Areas" AS "a"
WHERE NOT ("a"."IsDeleted")|duration: 151.144
2025-07-08 12:11:10.3705|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (2ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
SELECT "a"."Id", "a"."Active", "a"."Created", "a"."CreatedBy", "a"."Deleted", "a"."DeletedBy", "a"."Description", "a"."IsDeleted", "a"."Name", "a"."Updated", "a"."UpdatedBy"
FROM "Areas" AS "a"
WHERE NOT ("a"."IsDeleted")
ORDER BY "a"."Id"
LIMIT @__p_1 OFFSET @__p_0|duration: 202.773
2025-07-08 12:11:10.6127|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 395.2836ms.|duration: 439.771
2025-07-08 12:11:10.6166|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor|Executing OkObjectResult, writing value of type 'BitacoraResiduosESH.Backend.Backend.Application.Common.PagedResponse`1[[BitacoraResiduosESH.Backend.Backend.Application.DTOs.Area.AreaDto, BitacoraResiduosESH.Backend.Backend.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]'.|duration: 444.040
2025-07-08 12:11:10.6386|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI) in 441.5823ms|duration: 465.569
2025-07-08 12:11:10.6386|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 466.235
2025-07-08 12:11:10.6386|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/api/Areas - 200 - application/json;+charset=utf-8 468.4232ms|duration: 468.672
2025-07-08 12:11:17.3866|INFO|Microsoft.Hosting.Lifetime|Application is shutting down...|duration: 
2025-07-08 12:11:33.7108|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (21ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
PRAGMA journal_mode = 'wal';|duration: 
2025-07-08 12:11:33.8196|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE TABLE "Areas" (
    "Id" INTEGER NOT NULL CONSTRAINT "AreasPK" PRIMARY KEY AUTOINCREMENT,
    "Created" datetime2(3) NOT NULL DEFAULT (getutcdate()),
    "CreatedBy" nvarchar(50) NOT NULL DEFAULT (suser_name()),
    "IsDeleted" bit NOT NULL DEFAULT 0,
    "Deleted" datetime2(3) NULL,
    "DeletedBy" nvarchar(50) NULL,
    "Updated" datetime2(3) NULL,
    "UpdatedBy" nvarchar(50) NULL,
    "Active" bit NOT NULL DEFAULT 1,
    "Name" nvarchar(200) NOT NULL,
    "Description" nvarchar(1000) NULL
);|duration: 
2025-07-08 12:11:33.8196|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE TABLE "ContainerTypes" (
    "Id" INTEGER NOT NULL CONSTRAINT "ContainerTypesPK" PRIMARY KEY AUTOINCREMENT,
    "Created" datetime2(3) NOT NULL DEFAULT (getutcdate()),
    "CreatedBy" nvarchar(50) NOT NULL DEFAULT (suser_name()),
    "IsDeleted" bit NOT NULL DEFAULT 0,
    "Deleted" datetime2(3) NULL,
    "DeletedBy" nvarchar(50) NULL,
    "Updated" datetime2(3) NULL,
    "UpdatedBy" nvarchar(50) NULL,
    "Active" bit NOT NULL DEFAULT 1,
    "Name" nvarchar(200) NOT NULL,
    "Description" nvarchar(1000) NULL
);|duration: 
2025-07-08 12:11:33.8196|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE TABLE "Role" (
    "Id" INTEGER NOT NULL CONSTRAINT "RolePK" PRIMARY KEY AUTOINCREMENT,
    "Created" datetime2(3) NOT NULL DEFAULT (getutcdate()),
    "CreatedBy" nvarchar(50) NOT NULL DEFAULT (suser_name()),
    "IsDeleted" bit NOT NULL DEFAULT 0,
    "Deleted" datetime2(3) NULL,
    "DeletedBy" nvarchar(50) NULL,
    "Updated" datetime2(3) NULL,
    "UpdatedBy" nvarchar(50) NULL,
    "Active" bit NOT NULL DEFAULT 1,
    "Name" nvarchar(200) NOT NULL,
    "Description" nvarchar(1000) NULL
);|duration: 
2025-07-08 12:11:33.8196|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE TABLE "WasteTypes" (
    "Id" INTEGER NOT NULL CONSTRAINT "WasteTypesPK" PRIMARY KEY AUTOINCREMENT,
    "Created" datetime2(3) NOT NULL DEFAULT (getutcdate()),
    "CreatedBy" nvarchar(50) NOT NULL DEFAULT (suser_name()),
    "IsDeleted" bit NOT NULL DEFAULT 0,
    "Deleted" datetime2(3) NULL,
    "DeletedBy" nvarchar(50) NULL,
    "Updated" datetime2(3) NULL,
    "UpdatedBy" nvarchar(50) NULL,
    "Active" bit NOT NULL DEFAULT 1,
    "Name" nvarchar(200) NOT NULL,
    "Description" nvarchar(1000) NULL
);|duration: 
2025-07-08 12:11:33.8196|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE TABLE "User" (
    "Id" INTEGER NOT NULL CONSTRAINT "UserPK" PRIMARY KEY AUTOINCREMENT,
    "Name" nvarchar(200) NOT NULL,
    "EmployeeNumber" nvarchar(30) NOT NULL,
    "Email" nvarchar(255) NULL,
    "Password" nvarchar(255) NOT NULL,
    "RoleId" INTEGER NOT NULL,
    "Created" datetime2(3) NOT NULL DEFAULT (getutcdate()),
    "CreatedBy" nvarchar(50) NOT NULL DEFAULT (suser_name()),
    "IsDeleted" bit NOT NULL DEFAULT 0,
    "Deleted" datetime2(3) NULL,
    "DeletedBy" nvarchar(50) NULL,
    "Updated" datetime2(3) NULL,
    "UpdatedBy" nvarchar(50) NULL,
    "Active" bit NOT NULL DEFAULT 1,
    CONSTRAINT "FK_User_RoleId_Role_Id" FOREIGN KEY ("RoleId") REFERENCES "Role" ("Id") ON DELETE RESTRICT
);|duration: 
2025-07-08 12:11:33.8196|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE TABLE "BitacoraEntries" (
    "Id" INTEGER NOT NULL CONSTRAINT "BitacoraEntriesPK" PRIMARY KEY AUTOINCREMENT,
    "Name" nvarchar(200) NOT NULL,
    "Description" nvarchar(1000) NULL,
    "WasteTypeId" INTEGER NOT NULL,
    "GrossWeight" decimal(18,4) NOT NULL,
    "Tare" decimal(18,4) NOT NULL,
    "NetWeightLB" decimal(18,4) NOT NULL,
    "NetWeightKG" decimal(18,4) NOT NULL,
    "UnitPrice" decimal(18,4) NOT NULL,
    "ContainerTypeId" INTEGER NOT NULL,
    "AreaId" INTEGER NOT NULL,
    "EntryDate" datetime2(3) NOT NULL,
    "DepartureDate" datetime2(3) NULL,
    "EnteredBy" nvarchar(100) NOT NULL,
    "Created" datetime2(3) NOT NULL DEFAULT (getutcdate()),
    "CreatedBy" nvarchar(50) NOT NULL DEFAULT (suser_name()),
    "IsDeleted" bit NOT NULL DEFAULT 0,
    "Deleted" datetime2(3) NULL,
    "DeletedBy" nvarchar(50) NULL,
    "Updated" datetime2(3) NULL,
    "UpdatedBy" nvarchar(50) NULL,
    "Active" bit NOT NULL DEFAULT 1,
    CONSTRAINT "FK_BitacoraEntries_Areas" FOREIGN KEY ("AreaId") REFERENCES "Areas" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_BitacoraEntries_ContainerTypes" FOREIGN KEY ("ContainerTypeId") REFERENCES "ContainerTypes" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_BitacoraEntries_WasteTypes" FOREIGN KEY ("WasteTypeId") REFERENCES "WasteTypes" ("Id") ON DELETE RESTRICT
);|duration: 
2025-07-08 12:11:33.8196|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Areas_Active" ON "Areas" ("Active");|duration: 
2025-07-08 12:11:33.8347|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Areas_Active_IsDeleted" ON "Areas" ("Active", "IsDeleted");|duration: 
2025-07-08 12:11:33.8347|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Areas_Created" ON "Areas" ("Created");|duration: 
2025-07-08 12:11:33.8347|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Areas_IsDeleted" ON "Areas" ("IsDeleted");|duration: 
2025-07-08 12:11:33.8347|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Areas_Name_Active" ON "Areas" ("Name", "Active");|duration: 
2025-07-08 12:11:33.8347|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Areas_Name_Created" ON "Areas" ("Name", "Created");|duration: 
2025-07-08 12:11:33.8347|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE UNIQUE INDEX "UQ_Areas_Name_NotDeleted" ON "Areas" ("Name") WHERE [IsDeleted] = 0;|duration: 
2025-07-08 12:11:33.8347|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_BitacoraEntries_Active" ON "BitacoraEntries" ("Active");|duration: 
2025-07-08 12:11:33.8347|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_BitacoraEntries_Active_IsDeleted" ON "BitacoraEntries" ("Active", "IsDeleted");|duration: 
2025-07-08 12:11:33.8347|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_BitacoraEntries_AreaId" ON "BitacoraEntries" ("AreaId");|duration: 
2025-07-08 12:11:33.8347|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_BitacoraEntries_ContainerTypeId" ON "BitacoraEntries" ("ContainerTypeId");|duration: 
2025-07-08 12:11:33.8506|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_BitacoraEntries_Created" ON "BitacoraEntries" ("Created");|duration: 
2025-07-08 12:11:33.8506|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_BitacoraEntries_DepartureDate" ON "BitacoraEntries" ("DepartureDate");|duration: 
2025-07-08 12:11:33.8506|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_BitacoraEntries_EnteredBy" ON "BitacoraEntries" ("EnteredBy");|duration: 
2025-07-08 12:11:33.8506|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_BitacoraEntries_EntryDate" ON "BitacoraEntries" ("EntryDate");|duration: 
2025-07-08 12:11:33.8506|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (3ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_BitacoraEntries_IsDeleted" ON "BitacoraEntries" ("IsDeleted");|duration: 
2025-07-08 12:11:33.8506|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_BitacoraEntries_WasteTypeId" ON "BitacoraEntries" ("WasteTypeId");|duration: 
2025-07-08 12:11:33.8506|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_ContainerTypes_Active" ON "ContainerTypes" ("Active");|duration: 
2025-07-08 12:11:33.8506|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_ContainerTypes_Active_IsDeleted" ON "ContainerTypes" ("Active", "IsDeleted");|duration: 
2025-07-08 12:11:33.8506|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_ContainerTypes_Created" ON "ContainerTypes" ("Created");|duration: 
2025-07-08 12:11:33.8663|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_ContainerTypes_IsDeleted" ON "ContainerTypes" ("IsDeleted");|duration: 
2025-07-08 12:11:33.8663|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_ContainerTypes_Name_Active" ON "ContainerTypes" ("Name", "Active");|duration: 
2025-07-08 12:11:33.8663|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_ContainerTypes_Name_Created" ON "ContainerTypes" ("Name", "Created");|duration: 
2025-07-08 12:11:33.8663|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE UNIQUE INDEX "UQ_ContainerTypes_Name_NotDeleted" ON "ContainerTypes" ("Name") WHERE [IsDeleted] = 0;|duration: 
2025-07-08 12:11:33.8663|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Role_Active" ON "Role" ("Active");|duration: 
2025-07-08 12:11:33.8663|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Role_Active_IsDeleted" ON "Role" ("Active", "IsDeleted");|duration: 
2025-07-08 12:11:33.8663|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (2ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Role_Created" ON "Role" ("Created");|duration: 
2025-07-08 12:11:33.8663|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Role_IsDeleted" ON "Role" ("IsDeleted");|duration: 
2025-07-08 12:11:33.8663|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Role_Name_Active" ON "Role" ("Name", "Active");|duration: 
2025-07-08 12:11:33.8821|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Role_Name_Created" ON "Role" ("Name", "Created");|duration: 
2025-07-08 12:11:33.8821|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE UNIQUE INDEX "UQ_Role_Name_NotDeleted" ON "Role" ("Name") WHERE [IsDeleted] = 0;|duration: 
2025-07-08 12:11:33.8821|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_Active" ON "User" ("Active");|duration: 
2025-07-08 12:11:33.8821|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_Active_IsDeleted" ON "User" ("Active", "IsDeleted");|duration: 
2025-07-08 12:11:33.8821|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_Created" ON "User" ("Created");|duration: 
2025-07-08 12:11:33.8821|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_Email_Active" ON "User" ("Email", "Active");|duration: 
2025-07-08 12:11:33.8821|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_IsDeleted" ON "User" ("IsDeleted");|duration: 
2025-07-08 12:11:33.8821|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_Name" ON "User" ("Name");|duration: 
2025-07-08 12:11:33.8821|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_Name_Created" ON "User" ("Name", "Created");|duration: 
2025-07-08 12:11:33.8976|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_RoleId" ON "User" ("RoleId");|duration: 
2025-07-08 12:11:33.8976|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_RoleId_Active" ON "User" ("RoleId", "Active");|duration: 
2025-07-08 12:11:33.8976|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE UNIQUE INDEX "UQ_User_Email_NotDeleted" ON "User" ("Email") WHERE [IsDeleted] = 0;|duration: 
2025-07-08 12:11:33.8976|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE UNIQUE INDEX "UQ_User_EmployeeNumber_NotDeleted" ON "User" ("EmployeeNumber") WHERE [IsDeleted] = 0;|duration: 
2025-07-08 12:11:33.8976|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_WasteTypes_Active" ON "WasteTypes" ("Active");|duration: 
2025-07-08 12:11:33.8976|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_WasteTypes_Active_IsDeleted" ON "WasteTypes" ("Active", "IsDeleted");|duration: 
2025-07-08 12:11:33.8976|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_WasteTypes_Created" ON "WasteTypes" ("Created");|duration: 
2025-07-08 12:11:33.8976|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_WasteTypes_IsDeleted" ON "WasteTypes" ("IsDeleted");|duration: 
2025-07-08 12:11:33.9132|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_WasteTypes_Name_Active" ON "WasteTypes" ("Name", "Active");|duration: 
2025-07-08 12:11:33.9132|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_WasteTypes_Name_Created" ON "WasteTypes" ("Name", "Created");|duration: 
2025-07-08 12:11:33.9132|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE UNIQUE INDEX "UQ_WasteTypes_Name_NotDeleted" ON "WasteTypes" ("Name") WHERE [IsDeleted] = 0;|duration: 
2025-07-08 12:11:33.9132|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Program|Base de datos inicializada correctamente|duration: 
2025-07-08 12:11:33.9285|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|Inicializando base de datos con datos de prueba...|duration: 
2025-07-08 12:11:34.1013|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (2ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM "Role" AS "r")|duration: 
2025-07-08 12:11:34.2438|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (4ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (Size = 6), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (Size = 45), @p5='?' (Size = 5), @p6='?' (DbType = DateTime), @p7='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "Role" ("Created", "CreatedBy", "Deleted", "DeletedBy", "Description", "Name", "Updated", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:11:34.2575|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|Rol Admin creado exitosamente|duration: 
2025-07-08 12:11:34.5698|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (Size = 6), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (Size = 27), @p5='?' (Size = 6), @p6='?' (Size = 17), @p7='?' (Size = 60), @p8='?' (DbType = Int32), @p9='?' (DbType = DateTime), @p10='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "User" ("Created", "CreatedBy", "Deleted", "DeletedBy", "Email", "EmployeeNumber", "Name", "Password", "RoleId", "Updated", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:11:34.5698|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|Usuario administrador creado exitosamente|duration: 
2025-07-08 12:11:34.6032|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (Size = 6), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (Size = 33), @p5='?' (Size = 4), @p6='?' (DbType = DateTime), @p7='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "Areas" ("Created", "CreatedBy", "Deleted", "DeletedBy", "Description", "Name", "Updated", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:11:34.6032|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (Size = 6), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (Size = 11), @p5='?' (Size = 6), @p6='?' (DbType = DateTime), @p7='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "Areas" ("Created", "CreatedBy", "Deleted", "DeletedBy", "Description", "Name", "Updated", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:11:34.6159|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (Size = 6), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (Size = 15), @p5='?' (Size = 10), @p6='?' (DbType = DateTime), @p7='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "Areas" ("Created", "CreatedBy", "Deleted", "DeletedBy", "Description", "Name", "Updated", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:11:34.6159|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|Areas creadas exitosamente|duration: 
2025-07-08 12:11:34.6381|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (Size = 6), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (Size = 17), @p5='?' (Size = 5), @p6='?' (DbType = DateTime), @p7='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "WasteTypes" ("Created", "CreatedBy", "Deleted", "DeletedBy", "Description", "Name", "Updated", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:11:34.6481|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (Size = 6), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (Size = 19), @p5='?' (Size = 7), @p6='?' (DbType = DateTime), @p7='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "WasteTypes" ("Created", "CreatedBy", "Deleted", "DeletedBy", "Description", "Name", "Updated", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:11:34.6481|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (Size = 6), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (Size = 19), @p5='?' (Size = 7), @p6='?' (DbType = DateTime), @p7='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "WasteTypes" ("Created", "CreatedBy", "Deleted", "DeletedBy", "Description", "Name", "Updated", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:11:34.6481|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (Size = 6), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (Size = 18), @p5='?' (Size = 6), @p6='?' (DbType = DateTime), @p7='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "WasteTypes" ("Created", "CreatedBy", "Deleted", "DeletedBy", "Description", "Name", "Updated", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:11:34.6481|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|WasteTypes creados exitosamente|duration: 
2025-07-08 12:11:34.6704|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (Size = 6), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (Size = 22), @p5='?' (Size = 6), @p6='?' (DbType = DateTime), @p7='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "ContainerTypes" ("Created", "CreatedBy", "Deleted", "DeletedBy", "Description", "Name", "Updated", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:11:34.6790|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|ContainerTypes creados exitosamente|duration: 
2025-07-08 12:11:34.7266|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (2ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime), @p3='?' (Size = 6), @p4='?' (DbType = DateTime), @p5='?', @p6='?' (DbType = DateTime), @p7='?' (Size = 44), @p8='?' (Size = 17), @p9='?' (DbType = DateTime), @p10='?' (DbType = Decimal), @p11='?' (Size = 22), @p12='?' (DbType = Decimal), @p13='?' (DbType = Decimal), @p14='?' (DbType = Decimal), @p15='?' (DbType = Decimal), @p16='?' (DbType = DateTime), @p17='?', @p18='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
INSERT INTO "BitacoraEntries" ("AreaId", "ContainerTypeId", "Created", "CreatedBy", "Deleted", "DeletedBy", "DepartureDate", "Description", "EnteredBy", "EntryDate", "GrossWeight", "Name", "NetWeightKG", "NetWeightLB", "Tare", "UnitPrice", "Updated", "UpdatedBy", "WasteTypeId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:11:34.7266|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime), @p3='?' (Size = 6), @p4='?' (DbType = DateTime), @p5='?', @p6='?' (DbType = DateTime), @p7='?' (Size = 53), @p8='?' (Size = 17), @p9='?' (DbType = DateTime), @p10='?' (DbType = Decimal), @p11='?' (Size = 26), @p12='?' (DbType = Decimal), @p13='?' (DbType = Decimal), @p14='?' (DbType = Decimal), @p15='?' (DbType = Decimal), @p16='?' (DbType = DateTime), @p17='?', @p18='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
INSERT INTO "BitacoraEntries" ("AreaId", "ContainerTypeId", "Created", "CreatedBy", "Deleted", "DeletedBy", "DepartureDate", "Description", "EnteredBy", "EntryDate", "GrossWeight", "Name", "NetWeightKG", "NetWeightLB", "Tare", "UnitPrice", "Updated", "UpdatedBy", "WasteTypeId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:11:34.7424|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime), @p3='?' (Size = 6), @p4='?' (DbType = DateTime), @p5='?', @p6='?' (DbType = DateTime), @p7='?' (Size = 56), @p8='?' (Size = 17), @p9='?' (DbType = DateTime), @p10='?' (DbType = Decimal), @p11='?' (Size = 29), @p12='?' (DbType = Decimal), @p13='?' (DbType = Decimal), @p14='?' (DbType = Decimal), @p15='?' (DbType = Decimal), @p16='?' (DbType = DateTime), @p17='?', @p18='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
INSERT INTO "BitacoraEntries" ("AreaId", "ContainerTypeId", "Created", "CreatedBy", "Deleted", "DeletedBy", "DepartureDate", "Description", "EnteredBy", "EntryDate", "GrossWeight", "Name", "NetWeightKG", "NetWeightLB", "Tare", "UnitPrice", "Updated", "UpdatedBy", "WasteTypeId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:11:34.7424|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime), @p3='?' (Size = 6), @p4='?' (DbType = DateTime), @p5='?', @p6='?' (DbType = DateTime), @p7='?' (Size = 46), @p8='?' (Size = 17), @p9='?' (DbType = DateTime), @p10='?' (DbType = Decimal), @p11='?' (Size = 24), @p12='?' (DbType = Decimal), @p13='?' (DbType = Decimal), @p14='?' (DbType = Decimal), @p15='?' (DbType = Decimal), @p16='?' (DbType = DateTime), @p17='?', @p18='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
INSERT INTO "BitacoraEntries" ("AreaId", "ContainerTypeId", "Created", "CreatedBy", "Deleted", "DeletedBy", "DepartureDate", "Description", "EnteredBy", "EntryDate", "GrossWeight", "Name", "NetWeightKG", "NetWeightLB", "Tare", "UnitPrice", "Updated", "UpdatedBy", "WasteTypeId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:11:34.7424|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|BitacoraEntries de prueba creadas exitosamente|duration: 
2025-07-08 12:11:34.7424|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|Inicialización de base de datos completada exitosamente|duration: 
2025-07-08 12:11:34.7424|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Program|Datos de prueba inicializados correctamente|duration: 
2025-07-08 12:11:34.8609|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Program|Aplicación iniciada correctamente|duration: 
2025-07-08 12:11:34.8988|INFO|Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager|User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.|duration: 
2025-07-08 12:11:34.9603|WARN|Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware|The WebRootPath was not found: C:\Repositories\WebProjects\BitacoraResiduosESH\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend.Backend.WebAPI\wwwroot. Static files may be unavailable.|duration: 
2025-07-08 12:11:35.0411|INFO|Microsoft.Hosting.Lifetime|Now listening on: http://localhost:5001|duration: 
2025-07-08 12:11:35.0411|INFO|Microsoft.Hosting.Lifetime|Application started. Press Ctrl+C to shut down.|duration: 
2025-07-08 12:11:35.0411|INFO|Microsoft.Hosting.Lifetime|Hosting environment: Development|duration: 
2025-07-08 12:11:35.0411|INFO|Microsoft.Hosting.Lifetime|Content root path: C:\Repositories\WebProjects\BitacoraResiduosESH\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend.Backend.WebAPI|duration: 
2025-07-08 12:11:35.4804|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar - - -|duration: 2.702
2025-07-08 12:11:35.5381|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 60.777
2025-07-08 12:11:35.5381|INFO|Microsoft.AspNetCore.Http.Result.RedirectResult|Executing RedirectResult, redirecting to scalar/.|duration: 70.524
2025-07-08 12:11:35.5381|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 70.910
2025-07-08 12:11:35.5543|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar - 302 0 - 73.0119ms|duration: 76.242
2025-07-08 12:11:35.5543|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar/ - - -|duration: 0.148
2025-07-08 12:11:35.5543|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 2.733
2025-07-08 12:11:35.6160|INFO|Microsoft.AspNetCore.Http.Result.ContentResult|Write content with HTTP Response ContentType of text/html|duration: 69.333
2025-07-08 12:11:35.6160|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 69.978
2025-07-08 12:11:35.6160|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar/ - 200 924 text/html 70.2773ms|duration: 70.445
2025-07-08 12:11:35.6390|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar/scalar.js - - -|duration: 0.160
2025-07-08 12:11:35.6390|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar/scalar.aspnetcore.js - - -|duration: 0.151
2025-07-08 12:11:35.6390|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/scalar.js'|duration: 3.280
2025-07-08 12:11:35.6390|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 3.216
2025-07-08 12:11:35.6390|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/scalar.aspnetcore.js'|duration: 3.918
2025-07-08 12:11:35.6496|INFO|Microsoft.AspNetCore.Http.Result.StatusCodeResult|Setting HTTP status code 304.|duration: 14.180
2025-07-08 12:11:35.6496|INFO|Microsoft.AspNetCore.Http.Result.StatusCodeResult|Setting HTTP status code 304.|duration: 15.210
2025-07-08 12:11:35.6496|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/scalar.aspnetcore.js'|duration: 14.594
2025-07-08 12:11:35.6496|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/scalar.js'|duration: 15.556
2025-07-08 12:11:35.6496|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar/scalar.js - 304 - - 15.9790ms|duration: 16.109
2025-07-08 12:11:35.6496|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar/scalar.aspnetcore.js - 304 - - 15.3864ms|duration: 15.502
2025-07-08 12:11:35.8386|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/openapi/v1.json - - -|duration: 0.136
2025-07-08 12:11:35.8386|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /openapi/{documentName}.json'|duration: 1.996
2025-07-08 12:11:36.0703|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /openapi/{documentName}.json'|duration: 231.986
2025-07-08 12:11:36.0703|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/openapi/v1.json - 200 - application/json;charset=utf-8 233.3894ms|duration: 233.516
2025-07-08 12:11:45.5364|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/api/Areas - - -|duration: 3.570
2025-07-08 12:11:45.5417|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 8.268
2025-07-08 12:11:45.6034|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Route matched with {action = "GetAll", controller = "Areas"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[BitacoraResiduosESH.Backend.Backend.Application.Common.PagedResponse`1[BitacoraResiduosESH.Backend.Backend.Application.DTOs.Area.AreaDto]]] GetAll(Int32, Int32, Boolean) on controller BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController (BitacoraResiduosESH.Backend.Backend.WebAPI).|duration: 67.313
2025-07-08 12:11:45.7106|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executing action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI) - Validation state: Valid|duration: 185.648
2025-07-08 12:11:45.8507|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT COUNT(*)
FROM "Areas" AS "a"
WHERE NOT ("a"."IsDeleted")|duration: 316.747
2025-07-08 12:11:45.9215|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
SELECT "a"."Id", "a"."Active", "a"."Created", "a"."CreatedBy", "a"."Deleted", "a"."DeletedBy", "a"."Description", "a"."IsDeleted", "a"."Name", "a"."Updated", "a"."UpdatedBy"
FROM "Areas" AS "a"
WHERE NOT ("a"."IsDeleted")
ORDER BY "a"."Id"
LIMIT @__p_1 OFFSET @__p_0|duration: 387.038
2025-07-08 12:11:45.9294|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 214.0478ms.|duration: 404.567
2025-07-08 12:11:45.9449|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor|Executing OkObjectResult, writing value of type 'BitacoraResiduosESH.Backend.Backend.Application.Common.PagedResponse`1[[BitacoraResiduosESH.Backend.Backend.Application.DTOs.Area.AreaDto, BitacoraResiduosESH.Backend.Backend.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]'.|duration: 423.157
2025-07-08 12:11:45.9682|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI) in 361.343ms|duration: 435.914
2025-07-08 12:11:45.9682|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AreasController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 436.235
2025-07-08 12:11:45.9767|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/api/Areas - 200 - application/json;+charset=utf-8 441.6350ms|duration: 441.835
2025-07-08 12:11:58.9527|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/api/BitacoraEntry - - -|duration: 0.322
2025-07-08 12:11:58.9527|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 2.238
2025-07-08 12:11:58.9527|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Route matched with {action = "GetAll", controller = "BitacoraEntry"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[BitacoraResiduosESH.Backend.Backend.Application.Common.PagedResponse`1[BitacoraResiduosESH.Backend.Backend.Application.DTOs.BitacoraEntry.BitacoraEntryDto]]] GetAll(Int32, Int32, Boolean) on controller BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController (BitacoraResiduosESH.Backend.Backend.WebAPI).|duration: 4.845
2025-07-08 12:11:58.9601|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executing action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI) - Validation state: Valid|duration: 9.584
2025-07-08 12:11:58.9963|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[@__filter_IncludeDeleted_0='?' (DbType = Boolean)], CommandType='Text', CommandTimeout='30']
SELECT COUNT(*)
FROM "BitacoraEntries" AS "b"
WHERE "b"."IsDeleted" = @__filter_IncludeDeleted_0|duration: 50.758
2025-07-08 12:11:59.0540|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[@__filter_IncludeDeleted_0='?' (DbType = Boolean), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
SELECT "b0"."Id", "b0"."Active", "b0"."AreaId", "b0"."ContainerTypeId", "b0"."Created", "b0"."CreatedBy", "b0"."Deleted", "b0"."DeletedBy", "b0"."DepartureDate", "b0"."Description", "b0"."EnteredBy", "b0"."EntryDate", "b0"."GrossWeight", "b0"."IsDeleted", "b0"."Name", "b0"."NetWeightKG", "b0"."NetWeightLB", "b0"."Tare", "b0"."UnitPrice", "b0"."Updated", "b0"."UpdatedBy", "b0"."WasteTypeId", "w"."Id", "w"."Active", "w"."Created", "w"."CreatedBy", "w"."Deleted", "w"."DeletedBy", "w"."Description", "w"."IsDeleted", "w"."Name", "w"."Updated", "w"."UpdatedBy", "c"."Id", "c"."Active", "c"."Created", "c"."CreatedBy", "c"."Deleted", "c"."DeletedBy", "c"."Description", "c"."IsDeleted", "c"."Name", "c"."Updated", "c"."UpdatedBy", "a"."Id", "a"."Active", "a"."Created", "a"."CreatedBy", "a"."Deleted", "a"."DeletedBy", "a"."Description", "a"."IsDeleted", "a"."Name", "a"."Updated", "a"."UpdatedBy"
FROM (
    SELECT "b"."Id", "b"."Active", "b"."AreaId", "b"."ContainerTypeId", "b"."Created", "b"."CreatedBy", "b"."Deleted", "b"."DeletedBy", "b"."DepartureDate", "b"."Description", "b"."EnteredBy", "b"."EntryDate", "b"."GrossWeight", "b"."IsDeleted", "b"."Name", "b"."NetWeightKG", "b"."NetWeightLB", "b"."Tare", "b"."UnitPrice", "b"."Updated", "b"."UpdatedBy", "b"."WasteTypeId"
    FROM "BitacoraEntries" AS "b"
    WHERE "b"."IsDeleted" = @__filter_IncludeDeleted_0
    ORDER BY "b"."Id"
    LIMIT @__p_2 OFFSET @__p_1
) AS "b0"
INNER JOIN "WasteTypes" AS "w" ON "b0"."WasteTypeId" = "w"."Id"
INNER JOIN "ContainerTypes" AS "c" ON "b0"."ContainerTypeId" = "c"."Id"
INNER JOIN "Areas" AS "a" ON "b0"."AreaId" = "a"."Id"
ORDER BY "b0"."Id"|duration: 104.338
2025-07-08 12:11:59.0540|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 103.0787ms.|duration: 112.823
2025-07-08 12:11:59.0540|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor|Executing OkObjectResult, writing value of type 'BitacoraResiduosESH.Backend.Backend.Application.Common.PagedResponse`1[[BitacoraResiduosESH.Backend.Backend.Application.DTOs.BitacoraEntry.BitacoraEntryDto, BitacoraResiduosESH.Backend.Backend.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]'.|duration: 113.548
2025-07-08 12:11:59.0735|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI) in 115.6021ms|duration: 121.069
2025-07-08 12:11:59.0735|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 121.454
2025-07-08 12:11:59.0735|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/api/BitacoraEntry - 200 - application/json;+charset=utf-8 122.1605ms|duration: 122.389
2025-07-08 12:12:16.7692|INFO|Microsoft.Hosting.Lifetime|Application is shutting down...|duration: 
2025-07-08 12:25:31.8509|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (24ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
PRAGMA journal_mode = 'wal';|duration: 
2025-07-08 12:25:31.9459|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE TABLE "Areas" (
    "Id" INTEGER NOT NULL CONSTRAINT "AreasPK" PRIMARY KEY AUTOINCREMENT,
    "Created" datetime2(3) NOT NULL DEFAULT (getutcdate()),
    "CreatedBy" nvarchar(50) NOT NULL DEFAULT (suser_name()),
    "IsDeleted" bit NOT NULL DEFAULT 0,
    "Deleted" datetime2(3) NULL,
    "DeletedBy" nvarchar(50) NULL,
    "Updated" datetime2(3) NULL,
    "UpdatedBy" nvarchar(50) NULL,
    "Active" bit NOT NULL DEFAULT 1,
    "Name" nvarchar(200) NOT NULL,
    "Description" nvarchar(1000) NULL
);|duration: 
2025-07-08 12:25:31.9459|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE TABLE "ContainerTypes" (
    "Id" INTEGER NOT NULL CONSTRAINT "ContainerTypesPK" PRIMARY KEY AUTOINCREMENT,
    "Created" datetime2(3) NOT NULL DEFAULT (getutcdate()),
    "CreatedBy" nvarchar(50) NOT NULL DEFAULT (suser_name()),
    "IsDeleted" bit NOT NULL DEFAULT 0,
    "Deleted" datetime2(3) NULL,
    "DeletedBy" nvarchar(50) NULL,
    "Updated" datetime2(3) NULL,
    "UpdatedBy" nvarchar(50) NULL,
    "Active" bit NOT NULL DEFAULT 1,
    "Name" nvarchar(200) NOT NULL,
    "Description" nvarchar(1000) NULL
);|duration: 
2025-07-08 12:25:31.9459|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE TABLE "Role" (
    "Id" INTEGER NOT NULL CONSTRAINT "RolePK" PRIMARY KEY AUTOINCREMENT,
    "Created" datetime2(3) NOT NULL DEFAULT (getutcdate()),
    "CreatedBy" nvarchar(50) NOT NULL DEFAULT (suser_name()),
    "IsDeleted" bit NOT NULL DEFAULT 0,
    "Deleted" datetime2(3) NULL,
    "DeletedBy" nvarchar(50) NULL,
    "Updated" datetime2(3) NULL,
    "UpdatedBy" nvarchar(50) NULL,
    "Active" bit NOT NULL DEFAULT 1,
    "Name" nvarchar(200) NOT NULL,
    "Description" nvarchar(1000) NULL
);|duration: 
2025-07-08 12:25:31.9459|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE TABLE "WasteTypes" (
    "Id" INTEGER NOT NULL CONSTRAINT "WasteTypesPK" PRIMARY KEY AUTOINCREMENT,
    "Created" datetime2(3) NOT NULL DEFAULT (getutcdate()),
    "CreatedBy" nvarchar(50) NOT NULL DEFAULT (suser_name()),
    "IsDeleted" bit NOT NULL DEFAULT 0,
    "Deleted" datetime2(3) NULL,
    "DeletedBy" nvarchar(50) NULL,
    "Updated" datetime2(3) NULL,
    "UpdatedBy" nvarchar(50) NULL,
    "Active" bit NOT NULL DEFAULT 1,
    "Name" nvarchar(200) NOT NULL,
    "Description" nvarchar(1000) NULL
);|duration: 
2025-07-08 12:25:31.9603|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE TABLE "User" (
    "Id" INTEGER NOT NULL CONSTRAINT "UserPK" PRIMARY KEY AUTOINCREMENT,
    "Name" nvarchar(200) NOT NULL,
    "EmployeeNumber" nvarchar(30) NOT NULL,
    "Email" nvarchar(255) NULL,
    "Password" nvarchar(255) NOT NULL,
    "RoleId" INTEGER NOT NULL,
    "Created" datetime2(3) NOT NULL DEFAULT (getutcdate()),
    "CreatedBy" nvarchar(50) NOT NULL DEFAULT (suser_name()),
    "IsDeleted" bit NOT NULL DEFAULT 0,
    "Deleted" datetime2(3) NULL,
    "DeletedBy" nvarchar(50) NULL,
    "Updated" datetime2(3) NULL,
    "UpdatedBy" nvarchar(50) NULL,
    "Active" bit NOT NULL DEFAULT 1,
    CONSTRAINT "FK_User_RoleId_Role_Id" FOREIGN KEY ("RoleId") REFERENCES "Role" ("Id") ON DELETE RESTRICT
);|duration: 
2025-07-08 12:25:31.9603|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE TABLE "BitacoraEntries" (
    "Id" INTEGER NOT NULL CONSTRAINT "BitacoraEntriesPK" PRIMARY KEY AUTOINCREMENT,
    "Name" nvarchar(200) NOT NULL,
    "Description" nvarchar(1000) NULL,
    "WasteTypeId" INTEGER NOT NULL,
    "GrossWeight" decimal(18,4) NOT NULL,
    "Tare" decimal(18,4) NOT NULL,
    "NetWeightLB" decimal(18,4) NOT NULL,
    "NetWeightKG" decimal(18,4) NOT NULL,
    "UnitPrice" decimal(18,4) NOT NULL,
    "ContainerTypeId" INTEGER NOT NULL,
    "AreaId" INTEGER NOT NULL,
    "EntryDate" datetime2(3) NOT NULL,
    "DepartureDate" datetime2(3) NULL,
    "Created" datetime2(3) NOT NULL DEFAULT (getutcdate()),
    "CreatedBy" nvarchar(50) NOT NULL DEFAULT (suser_name()),
    "IsDeleted" bit NOT NULL DEFAULT 0,
    "Deleted" datetime2(3) NULL,
    "DeletedBy" nvarchar(50) NULL,
    "Updated" datetime2(3) NULL,
    "UpdatedBy" nvarchar(50) NULL,
    "Active" bit NOT NULL DEFAULT 1,
    CONSTRAINT "FK_BitacoraEntries_Areas" FOREIGN KEY ("AreaId") REFERENCES "Areas" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_BitacoraEntries_ContainerTypes" FOREIGN KEY ("ContainerTypeId") REFERENCES "ContainerTypes" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_BitacoraEntries_WasteTypes" FOREIGN KEY ("WasteTypeId") REFERENCES "WasteTypes" ("Id") ON DELETE RESTRICT
);|duration: 
2025-07-08 12:25:31.9603|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Areas_Active" ON "Areas" ("Active");|duration: 
2025-07-08 12:25:31.9603|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Areas_Active_IsDeleted" ON "Areas" ("Active", "IsDeleted");|duration: 
2025-07-08 12:25:31.9603|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Areas_Created" ON "Areas" ("Created");|duration: 
2025-07-08 12:25:31.9603|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Areas_IsDeleted" ON "Areas" ("IsDeleted");|duration: 
2025-07-08 12:25:31.9603|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Areas_Name_Active" ON "Areas" ("Name", "Active");|duration: 
2025-07-08 12:25:31.9603|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Areas_Name_Created" ON "Areas" ("Name", "Created");|duration: 
2025-07-08 12:25:31.9603|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE UNIQUE INDEX "UQ_Areas_Name_NotDeleted" ON "Areas" ("Name") WHERE [IsDeleted] = 0;|duration: 
2025-07-08 12:25:31.9603|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_BitacoraEntries_Active" ON "BitacoraEntries" ("Active");|duration: 
2025-07-08 12:25:31.9756|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_BitacoraEntries_Active_IsDeleted" ON "BitacoraEntries" ("Active", "IsDeleted");|duration: 
2025-07-08 12:25:31.9756|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_BitacoraEntries_AreaId" ON "BitacoraEntries" ("AreaId");|duration: 
2025-07-08 12:25:31.9756|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_BitacoraEntries_ContainerTypeId" ON "BitacoraEntries" ("ContainerTypeId");|duration: 
2025-07-08 12:25:31.9756|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_BitacoraEntries_Created" ON "BitacoraEntries" ("Created");|duration: 
2025-07-08 12:25:31.9756|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_BitacoraEntries_CreatedBy" ON "BitacoraEntries" ("CreatedBy");|duration: 
2025-07-08 12:25:31.9756|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_BitacoraEntries_DepartureDate" ON "BitacoraEntries" ("DepartureDate");|duration: 
2025-07-08 12:25:31.9756|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_BitacoraEntries_EntryDate" ON "BitacoraEntries" ("EntryDate");|duration: 
2025-07-08 12:25:31.9756|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_BitacoraEntries_IsDeleted" ON "BitacoraEntries" ("IsDeleted");|duration: 
2025-07-08 12:25:31.9756|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_BitacoraEntries_WasteTypeId" ON "BitacoraEntries" ("WasteTypeId");|duration: 
2025-07-08 12:25:31.9914|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_ContainerTypes_Active" ON "ContainerTypes" ("Active");|duration: 
2025-07-08 12:25:31.9914|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_ContainerTypes_Active_IsDeleted" ON "ContainerTypes" ("Active", "IsDeleted");|duration: 
2025-07-08 12:25:31.9914|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_ContainerTypes_Created" ON "ContainerTypes" ("Created");|duration: 
2025-07-08 12:25:31.9914|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_ContainerTypes_IsDeleted" ON "ContainerTypes" ("IsDeleted");|duration: 
2025-07-08 12:25:31.9914|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_ContainerTypes_Name_Active" ON "ContainerTypes" ("Name", "Active");|duration: 
2025-07-08 12:25:31.9914|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_ContainerTypes_Name_Created" ON "ContainerTypes" ("Name", "Created");|duration: 
2025-07-08 12:25:31.9914|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE UNIQUE INDEX "UQ_ContainerTypes_Name_NotDeleted" ON "ContainerTypes" ("Name") WHERE [IsDeleted] = 0;|duration: 
2025-07-08 12:25:31.9914|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Role_Active" ON "Role" ("Active");|duration: 
2025-07-08 12:25:31.9914|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Role_Active_IsDeleted" ON "Role" ("Active", "IsDeleted");|duration: 
2025-07-08 12:25:32.0070|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Role_Created" ON "Role" ("Created");|duration: 
2025-07-08 12:25:32.0070|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Role_IsDeleted" ON "Role" ("IsDeleted");|duration: 
2025-07-08 12:25:32.0070|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Role_Name_Active" ON "Role" ("Name", "Active");|duration: 
2025-07-08 12:25:32.0070|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Role_Name_Created" ON "Role" ("Name", "Created");|duration: 
2025-07-08 12:25:32.0070|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE UNIQUE INDEX "UQ_Role_Name_NotDeleted" ON "Role" ("Name") WHERE [IsDeleted] = 0;|duration: 
2025-07-08 12:25:32.0070|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_Active" ON "User" ("Active");|duration: 
2025-07-08 12:25:32.0070|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_Active_IsDeleted" ON "User" ("Active", "IsDeleted");|duration: 
2025-07-08 12:25:32.0070|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_Created" ON "User" ("Created");|duration: 
2025-07-08 12:25:32.0070|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_Email_Active" ON "User" ("Email", "Active");|duration: 
2025-07-08 12:25:32.0070|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_IsDeleted" ON "User" ("IsDeleted");|duration: 
2025-07-08 12:25:32.0070|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_Name" ON "User" ("Name");|duration: 
2025-07-08 12:25:32.0070|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_Name_Created" ON "User" ("Name", "Created");|duration: 
2025-07-08 12:25:32.0227|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_RoleId" ON "User" ("RoleId");|duration: 
2025-07-08 12:25:32.0227|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_RoleId_Active" ON "User" ("RoleId", "Active");|duration: 
2025-07-08 12:25:32.0227|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE UNIQUE INDEX "UQ_User_Email_NotDeleted" ON "User" ("Email") WHERE [IsDeleted] = 0;|duration: 
2025-07-08 12:25:32.0227|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE UNIQUE INDEX "UQ_User_EmployeeNumber_NotDeleted" ON "User" ("EmployeeNumber") WHERE [IsDeleted] = 0;|duration: 
2025-07-08 12:25:32.0227|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_WasteTypes_Active" ON "WasteTypes" ("Active");|duration: 
2025-07-08 12:25:32.0227|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_WasteTypes_Active_IsDeleted" ON "WasteTypes" ("Active", "IsDeleted");|duration: 
2025-07-08 12:25:32.0227|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_WasteTypes_Created" ON "WasteTypes" ("Created");|duration: 
2025-07-08 12:25:32.0227|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_WasteTypes_IsDeleted" ON "WasteTypes" ("IsDeleted");|duration: 
2025-07-08 12:25:32.0227|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_WasteTypes_Name_Active" ON "WasteTypes" ("Name", "Active");|duration: 
2025-07-08 12:25:32.0227|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_WasteTypes_Name_Created" ON "WasteTypes" ("Name", "Created");|duration: 
2025-07-08 12:25:32.0378|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE UNIQUE INDEX "UQ_WasteTypes_Name_NotDeleted" ON "WasteTypes" ("Name") WHERE [IsDeleted] = 0;|duration: 
2025-07-08 12:25:32.0378|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Program|Base de datos inicializada correctamente|duration: 
2025-07-08 12:25:32.0574|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|Inicializando base de datos con datos de prueba...|duration: 
2025-07-08 12:25:32.2288|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (2ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM "Role" AS "r")|duration: 
2025-07-08 12:25:32.3673|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (4ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (Size = 6), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (Size = 45), @p5='?' (Size = 5), @p6='?' (DbType = DateTime), @p7='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "Role" ("Created", "CreatedBy", "Deleted", "DeletedBy", "Description", "Name", "Updated", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:25:32.3856|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|Rol Admin creado exitosamente|duration: 
2025-07-08 12:25:32.8047|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (Size = 6), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (Size = 27), @p5='?' (Size = 6), @p6='?' (Size = 17), @p7='?' (Size = 60), @p8='?' (DbType = Int32), @p9='?' (DbType = DateTime), @p10='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "User" ("Created", "CreatedBy", "Deleted", "DeletedBy", "Email", "EmployeeNumber", "Name", "Password", "RoleId", "Updated", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:25:32.8047|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|Usuario administrador creado exitosamente|duration: 
2025-07-08 12:25:32.8365|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (Size = 6), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (Size = 33), @p5='?' (Size = 4), @p6='?' (DbType = DateTime), @p7='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "Areas" ("Created", "CreatedBy", "Deleted", "DeletedBy", "Description", "Name", "Updated", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:25:32.8365|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (Size = 6), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (Size = 11), @p5='?' (Size = 6), @p6='?' (DbType = DateTime), @p7='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "Areas" ("Created", "CreatedBy", "Deleted", "DeletedBy", "Description", "Name", "Updated", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:25:32.8510|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (Size = 6), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (Size = 15), @p5='?' (Size = 10), @p6='?' (DbType = DateTime), @p7='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "Areas" ("Created", "CreatedBy", "Deleted", "DeletedBy", "Description", "Name", "Updated", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:25:32.8510|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|Areas creadas exitosamente|duration: 
2025-07-08 12:25:32.8737|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (Size = 6), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (Size = 17), @p5='?' (Size = 5), @p6='?' (DbType = DateTime), @p7='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "WasteTypes" ("Created", "CreatedBy", "Deleted", "DeletedBy", "Description", "Name", "Updated", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:25:32.8833|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (Size = 6), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (Size = 19), @p5='?' (Size = 7), @p6='?' (DbType = DateTime), @p7='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "WasteTypes" ("Created", "CreatedBy", "Deleted", "DeletedBy", "Description", "Name", "Updated", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:25:32.8833|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (Size = 6), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (Size = 19), @p5='?' (Size = 7), @p6='?' (DbType = DateTime), @p7='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "WasteTypes" ("Created", "CreatedBy", "Deleted", "DeletedBy", "Description", "Name", "Updated", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:25:32.8833|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (Size = 6), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (Size = 18), @p5='?' (Size = 6), @p6='?' (DbType = DateTime), @p7='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "WasteTypes" ("Created", "CreatedBy", "Deleted", "DeletedBy", "Description", "Name", "Updated", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:25:32.8984|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|WasteTypes creados exitosamente|duration: 
2025-07-08 12:25:32.9154|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (Size = 6), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (Size = 22), @p5='?' (Size = 6), @p6='?' (DbType = DateTime), @p7='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "ContainerTypes" ("Created", "CreatedBy", "Deleted", "DeletedBy", "Description", "Name", "Updated", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:25:32.9154|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|ContainerTypes creados exitosamente|duration: 
2025-07-08 12:25:32.9621|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime), @p3='?' (Size = 6), @p4='?' (DbType = DateTime), @p5='?', @p6='?' (DbType = DateTime), @p7='?' (Size = 44), @p8='?' (DbType = DateTime), @p9='?' (DbType = Decimal), @p10='?' (Size = 22), @p11='?' (DbType = Decimal), @p12='?' (DbType = Decimal), @p13='?' (DbType = Decimal), @p14='?' (DbType = Decimal), @p15='?' (DbType = DateTime), @p16='?', @p17='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
INSERT INTO "BitacoraEntries" ("AreaId", "ContainerTypeId", "Created", "CreatedBy", "Deleted", "DeletedBy", "DepartureDate", "Description", "EntryDate", "GrossWeight", "Name", "NetWeightKG", "NetWeightLB", "Tare", "UnitPrice", "Updated", "UpdatedBy", "WasteTypeId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:25:32.9621|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime), @p3='?' (Size = 6), @p4='?' (DbType = DateTime), @p5='?', @p6='?' (DbType = DateTime), @p7='?' (Size = 53), @p8='?' (DbType = DateTime), @p9='?' (DbType = Decimal), @p10='?' (Size = 26), @p11='?' (DbType = Decimal), @p12='?' (DbType = Decimal), @p13='?' (DbType = Decimal), @p14='?' (DbType = Decimal), @p15='?' (DbType = DateTime), @p16='?', @p17='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
INSERT INTO "BitacoraEntries" ("AreaId", "ContainerTypeId", "Created", "CreatedBy", "Deleted", "DeletedBy", "DepartureDate", "Description", "EntryDate", "GrossWeight", "Name", "NetWeightKG", "NetWeightLB", "Tare", "UnitPrice", "Updated", "UpdatedBy", "WasteTypeId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:25:32.9621|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime), @p3='?' (Size = 6), @p4='?' (DbType = DateTime), @p5='?', @p6='?' (DbType = DateTime), @p7='?' (Size = 56), @p8='?' (DbType = DateTime), @p9='?' (DbType = Decimal), @p10='?' (Size = 29), @p11='?' (DbType = Decimal), @p12='?' (DbType = Decimal), @p13='?' (DbType = Decimal), @p14='?' (DbType = Decimal), @p15='?' (DbType = DateTime), @p16='?', @p17='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
INSERT INTO "BitacoraEntries" ("AreaId", "ContainerTypeId", "Created", "CreatedBy", "Deleted", "DeletedBy", "DepartureDate", "Description", "EntryDate", "GrossWeight", "Name", "NetWeightKG", "NetWeightLB", "Tare", "UnitPrice", "Updated", "UpdatedBy", "WasteTypeId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:25:32.9754|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime), @p3='?' (Size = 6), @p4='?' (DbType = DateTime), @p5='?', @p6='?' (DbType = DateTime), @p7='?' (Size = 46), @p8='?' (DbType = DateTime), @p9='?' (DbType = Decimal), @p10='?' (Size = 24), @p11='?' (DbType = Decimal), @p12='?' (DbType = Decimal), @p13='?' (DbType = Decimal), @p14='?' (DbType = Decimal), @p15='?' (DbType = DateTime), @p16='?', @p17='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
INSERT INTO "BitacoraEntries" ("AreaId", "ContainerTypeId", "Created", "CreatedBy", "Deleted", "DeletedBy", "DepartureDate", "Description", "EntryDate", "GrossWeight", "Name", "NetWeightKG", "NetWeightLB", "Tare", "UnitPrice", "Updated", "UpdatedBy", "WasteTypeId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:25:32.9754|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|BitacoraEntries de prueba creadas exitosamente|duration: 
2025-07-08 12:25:32.9754|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|Inicialización de base de datos completada exitosamente|duration: 
2025-07-08 12:25:32.9754|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Program|Datos de prueba inicializados correctamente|duration: 
2025-07-08 12:25:33.0577|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Program|Aplicación iniciada correctamente|duration: 
2025-07-08 12:25:33.1009|INFO|Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager|User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.|duration: 
2025-07-08 12:25:33.1505|WARN|Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware|The WebRootPath was not found: C:\Repositories\WebProjects\BitacoraResiduosESH\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend.Backend.WebAPI\wwwroot. Static files may be unavailable.|duration: 
2025-07-08 12:25:33.2304|INFO|Microsoft.Hosting.Lifetime|Now listening on: http://localhost:5001|duration: 
2025-07-08 12:25:33.2304|INFO|Microsoft.Hosting.Lifetime|Application started. Press Ctrl+C to shut down.|duration: 
2025-07-08 12:25:33.2412|INFO|Microsoft.Hosting.Lifetime|Hosting environment: Development|duration: 
2025-07-08 12:25:33.2412|INFO|Microsoft.Hosting.Lifetime|Content root path: C:\Repositories\WebProjects\BitacoraResiduosESH\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend.Backend.WebAPI|duration: 
2025-07-08 12:25:33.7406|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar - - -|duration: 2.998
2025-07-08 12:25:33.7935|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 57.698
2025-07-08 12:25:33.8041|INFO|Microsoft.AspNetCore.Http.Result.RedirectResult|Executing RedirectResult, redirecting to scalar/.|duration: 66.088
2025-07-08 12:25:33.8041|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 66.454
2025-07-08 12:25:33.8041|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar - 302 0 - 68.8259ms|duration: 71.818
2025-07-08 12:25:33.8041|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar/ - - -|duration: 0.177
2025-07-08 12:25:33.8041|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 2.857
2025-07-08 12:25:33.8513|INFO|Microsoft.AspNetCore.Http.Result.ContentResult|Write content with HTTP Response ContentType of text/html|duration: 37.963
2025-07-08 12:25:33.8513|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 38.634
2025-07-08 12:25:33.8513|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar/ - 200 924 text/html 38.9803ms|duration: 39.163
2025-07-08 12:25:33.8704|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar/scalar.js - - -|duration: 0.128
2025-07-08 12:25:33.8704|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/scalar.js'|duration: 4.841
2025-07-08 12:25:33.8704|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar/scalar.aspnetcore.js - - -|duration: 0.101
2025-07-08 12:25:33.8704|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 3.359
2025-07-08 12:25:33.8704|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/scalar.aspnetcore.js'|duration: 3.912
2025-07-08 12:25:33.8825|INFO|Microsoft.AspNetCore.Http.Result.StatusCodeResult|Setting HTTP status code 304.|duration: 9.512
2025-07-08 12:25:33.8825|INFO|Microsoft.AspNetCore.Http.Result.StatusCodeResult|Setting HTTP status code 304.|duration: 14.830
2025-07-08 12:25:33.8825|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/scalar.aspnetcore.js'|duration: 9.712
2025-07-08 12:25:33.8825|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/scalar.js'|duration: 15.014
2025-07-08 12:25:33.8825|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar/scalar.aspnetcore.js - 304 - - 10.4515ms|duration: 10.558
2025-07-08 12:25:33.8825|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar/scalar.js - 304 - - 16.3235ms|duration: 16.443
2025-07-08 12:25:34.0616|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/openapi/v1.json - - -|duration: 0.122
2025-07-08 12:25:34.0616|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /openapi/{documentName}.json'|duration: 1.764
2025-07-08 12:25:34.2559|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /openapi/{documentName}.json'|duration: 194.425
2025-07-08 12:25:34.2571|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/openapi/v1.json - 200 - application/json;charset=utf-8 195.9350ms|duration: 196.053
2025-07-08 12:26:31.2833|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/api/BitacoraEntry - - -|duration: 0.651
2025-07-08 12:26:31.2833|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 4.560
2025-07-08 12:26:31.3733|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Route matched with {action = "GetAll", controller = "BitacoraEntry"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[BitacoraResiduosESH.Backend.Backend.Application.Common.PagedResponse`1[BitacoraResiduosESH.Backend.Backend.Application.DTOs.BitacoraEntry.BitacoraEntryDto]]] GetAll(Int32, Int32, Boolean) on controller BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController (BitacoraResiduosESH.Backend.Backend.WebAPI).|duration: 90.510
2025-07-08 12:26:31.5779|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executing action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI) - Validation state: Valid|duration: 294.973
2025-07-08 12:26:31.8820|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (2ms) [Parameters=[@__filter_IncludeDeleted_0='?' (DbType = Boolean)], CommandType='Text', CommandTimeout='30']
SELECT COUNT(*)
FROM "BitacoraEntries" AS "b"
WHERE "b"."IsDeleted" = @__filter_IncludeDeleted_0|duration: 608.128
2025-07-08 12:26:32.2426|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (4ms) [Parameters=[@__filter_IncludeDeleted_0='?' (DbType = Boolean), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
SELECT "b0"."Id", "b0"."Active", "b0"."AreaId", "b0"."ContainerTypeId", "b0"."Created", "b0"."CreatedBy", "b0"."Deleted", "b0"."DeletedBy", "b0"."DepartureDate", "b0"."Description", "b0"."EntryDate", "b0"."GrossWeight", "b0"."IsDeleted", "b0"."Name", "b0"."NetWeightKG", "b0"."NetWeightLB", "b0"."Tare", "b0"."UnitPrice", "b0"."Updated", "b0"."UpdatedBy", "b0"."WasteTypeId", "w"."Id", "w"."Active", "w"."Created", "w"."CreatedBy", "w"."Deleted", "w"."DeletedBy", "w"."Description", "w"."IsDeleted", "w"."Name", "w"."Updated", "w"."UpdatedBy", "c"."Id", "c"."Active", "c"."Created", "c"."CreatedBy", "c"."Deleted", "c"."DeletedBy", "c"."Description", "c"."IsDeleted", "c"."Name", "c"."Updated", "c"."UpdatedBy", "a"."Id", "a"."Active", "a"."Created", "a"."CreatedBy", "a"."Deleted", "a"."DeletedBy", "a"."Description", "a"."IsDeleted", "a"."Name", "a"."Updated", "a"."UpdatedBy"
FROM (
    SELECT "b"."Id", "b"."Active", "b"."AreaId", "b"."ContainerTypeId", "b"."Created", "b"."CreatedBy", "b"."Deleted", "b"."DeletedBy", "b"."DepartureDate", "b"."Description", "b"."EntryDate", "b"."GrossWeight", "b"."IsDeleted", "b"."Name", "b"."NetWeightKG", "b"."NetWeightLB", "b"."Tare", "b"."UnitPrice", "b"."Updated", "b"."UpdatedBy", "b"."WasteTypeId"
    FROM "BitacoraEntries" AS "b"
    WHERE "b"."IsDeleted" = @__filter_IncludeDeleted_0
    ORDER BY "b"."Id"
    LIMIT @__p_2 OFFSET @__p_1
) AS "b0"
INNER JOIN "WasteTypes" AS "w" ON "b0"."WasteTypeId" = "w"."Id"
INNER JOIN "ContainerTypes" AS "c" ON "b0"."ContainerTypeId" = "c"."Id"
INNER JOIN "Areas" AS "a" ON "b0"."AreaId" = "a"."Id"
ORDER BY "b0"."Id"|duration: 970.655
2025-07-08 12:26:32.3443|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 749.9636ms.|duration: 1064.579
2025-07-08 12:26:32.4160|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor|Executing OkObjectResult, writing value of type 'BitacoraResiduosESH.Backend.Backend.Application.Common.PagedResponse`1[[BitacoraResiduosESH.Backend.Backend.Application.DTOs.BitacoraEntry.BitacoraEntryDto, BitacoraResiduosESH.Backend.Backend.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]'.|duration: 1133.011
2025-07-08 12:26:32.4972|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI) in 1105.106ms|duration: 1214.246
2025-07-08 12:26:32.4972|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 1217.508
2025-07-08 12:26:32.5094|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/api/BitacoraEntry - 200 - application/json;+charset=utf-8 1228.3267ms|duration: 1228.618
2025-07-08 12:28:24.8010|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 POST http://localhost:5001/api/BitacoraEntry - application/json 209|duration: 0.788
2025-07-08 12:28:24.8053|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 7.467
2025-07-08 12:28:24.8053|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController.Add (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 8.952
2025-07-08 12:28:24.8367|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Route matched with {action = "Add", controller = "BitacoraEntry"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[BitacoraResiduosESH.Backend.Backend.Application.DTOs.BitacoraEntry.BitacoraEntryDto]] Add(BitacoraResiduosESH.Backend.Backend.Application.DTOs.BitacoraEntry.CreateBitacoraEntryDto) on controller BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController (BitacoraResiduosESH.Backend.Backend.WebAPI).|duration: 39.404
2025-07-08 12:28:24.9203|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executing action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController.Add (BitacoraResiduosESH.Backend.Backend.WebAPI) - Validation state: Valid|duration: 120.593
2025-07-08 12:28:25.0410|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (2ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime), @p3='?' (Size = 6), @p4='?' (DbType = DateTime), @p5='?', @p6='?' (DbType = DateTime), @p7='?', @p8='?' (DbType = DateTime), @p9='?' (DbType = Decimal), @p10='?' (Size = 6), @p11='?' (DbType = Decimal), @p12='?' (DbType = Decimal), @p13='?' (DbType = Decimal), @p14='?' (DbType = Decimal), @p15='?' (DbType = DateTime), @p16='?', @p17='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
INSERT INTO "BitacoraEntries" ("AreaId", "ContainerTypeId", "Created", "CreatedBy", "Deleted", "DeletedBy", "DepartureDate", "Description", "EntryDate", "GrossWeight", "Name", "NetWeightKG", "NetWeightLB", "Tare", "UnitPrice", "Updated", "UpdatedBy", "WasteTypeId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17)
RETURNING "Id", "Active", "IsDeleted";|duration: 252.525
2025-07-08 12:28:25.0615|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Repositories.GenericRepository`1|Entidad "BitacoraEntry" agregada exitosamente con ID: 5|duration: 265.360
2025-07-08 12:28:25.0615|INFO|BitacoraResiduosESH.Backend.Backend.Application.Services.GenericService`5|"BitacoraEntry" agregada exitosamente con ID: 5 por usuario: "system"|duration: 266.989
2025-07-08 12:28:25.0615|INFO|BitacoraResiduosESH.Backend.Backend.Application.Services.BitacoraEntryService|"BitacoraEntry" creada exitosamente con ID: 5 por usuario: "system"|duration: 267.133
2025-07-08 12:28:25.0615|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.GenericController`5|"BitacoraEntry" agregado exitosamente con ID: 5 por usuario: "system"|duration: 267.262
2025-07-08 12:28:25.0698|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController.Add (BitacoraResiduosESH.Backend.Backend.WebAPI), returned result Microsoft.AspNetCore.Mvc.CreatedAtActionResult in 147.4598ms.|duration: 269.056
2025-07-08 12:28:25.0698|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor|Executing CreatedAtActionResult, writing value of type 'BitacoraResiduosESH.Backend.Backend.Application.DTOs.BitacoraEntry.BitacoraEntryDto'.|duration: 274.086
2025-07-08 12:28:25.1390|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController.Add (BitacoraResiduosESH.Backend.Backend.WebAPI) in 302.3607ms|duration: 345.593
2025-07-08 12:28:25.1390|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController.Add (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 346.032
2025-07-08 12:28:25.1479|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 POST http://localhost:5001/api/BitacoraEntry - 201 - application/json;+charset=utf-8 347.5432ms|duration: 347.832
2025-07-08 12:28:41.2419|INFO|Microsoft.Hosting.Lifetime|Application is shutting down...|duration: 
2025-07-08 12:33:25.2789|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (18ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
PRAGMA journal_mode = 'wal';|duration: 
2025-07-08 12:33:25.3700|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE TABLE "Areas" (
    "Id" INTEGER NOT NULL CONSTRAINT "AreasPK" PRIMARY KEY AUTOINCREMENT,
    "Created" datetime2(3) NOT NULL DEFAULT (getutcdate()),
    "CreatedBy" nvarchar(50) NOT NULL DEFAULT (suser_name()),
    "IsDeleted" bit NOT NULL DEFAULT 0,
    "Deleted" datetime2(3) NULL,
    "DeletedBy" nvarchar(50) NULL,
    "Updated" datetime2(3) NULL,
    "UpdatedBy" nvarchar(50) NULL,
    "Active" bit NOT NULL DEFAULT 1,
    "Name" nvarchar(200) NOT NULL,
    "Description" nvarchar(1000) NULL
);|duration: 
2025-07-08 12:33:25.3700|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE TABLE "ContainerTypes" (
    "Id" INTEGER NOT NULL CONSTRAINT "ContainerTypesPK" PRIMARY KEY AUTOINCREMENT,
    "Created" datetime2(3) NOT NULL DEFAULT (getutcdate()),
    "CreatedBy" nvarchar(50) NOT NULL DEFAULT (suser_name()),
    "IsDeleted" bit NOT NULL DEFAULT 0,
    "Deleted" datetime2(3) NULL,
    "DeletedBy" nvarchar(50) NULL,
    "Updated" datetime2(3) NULL,
    "UpdatedBy" nvarchar(50) NULL,
    "Active" bit NOT NULL DEFAULT 1,
    "Name" nvarchar(200) NOT NULL,
    "Description" nvarchar(1000) NULL
);|duration: 
2025-07-08 12:33:25.3823|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (2ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE TABLE "Role" (
    "Id" INTEGER NOT NULL CONSTRAINT "RolePK" PRIMARY KEY AUTOINCREMENT,
    "Created" datetime2(3) NOT NULL DEFAULT (getutcdate()),
    "CreatedBy" nvarchar(50) NOT NULL DEFAULT (suser_name()),
    "IsDeleted" bit NOT NULL DEFAULT 0,
    "Deleted" datetime2(3) NULL,
    "DeletedBy" nvarchar(50) NULL,
    "Updated" datetime2(3) NULL,
    "UpdatedBy" nvarchar(50) NULL,
    "Active" bit NOT NULL DEFAULT 1,
    "Name" nvarchar(200) NOT NULL,
    "Description" nvarchar(1000) NULL
);|duration: 
2025-07-08 12:33:25.3823|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE TABLE "WasteTypes" (
    "Id" INTEGER NOT NULL CONSTRAINT "WasteTypesPK" PRIMARY KEY AUTOINCREMENT,
    "Created" datetime2(3) NOT NULL DEFAULT (getutcdate()),
    "CreatedBy" nvarchar(50) NOT NULL DEFAULT (suser_name()),
    "IsDeleted" bit NOT NULL DEFAULT 0,
    "Deleted" datetime2(3) NULL,
    "DeletedBy" nvarchar(50) NULL,
    "Updated" datetime2(3) NULL,
    "UpdatedBy" nvarchar(50) NULL,
    "Active" bit NOT NULL DEFAULT 1,
    "Name" nvarchar(200) NOT NULL,
    "Description" nvarchar(1000) NULL
);|duration: 
2025-07-08 12:33:25.3823|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE TABLE "User" (
    "Id" INTEGER NOT NULL CONSTRAINT "UserPK" PRIMARY KEY AUTOINCREMENT,
    "Name" nvarchar(200) NOT NULL,
    "EmployeeNumber" nvarchar(30) NOT NULL,
    "Email" nvarchar(255) NULL,
    "Password" nvarchar(255) NOT NULL,
    "RoleId" INTEGER NOT NULL,
    "Created" datetime2(3) NOT NULL DEFAULT (getutcdate()),
    "CreatedBy" nvarchar(50) NOT NULL DEFAULT (suser_name()),
    "IsDeleted" bit NOT NULL DEFAULT 0,
    "Deleted" datetime2(3) NULL,
    "DeletedBy" nvarchar(50) NULL,
    "Updated" datetime2(3) NULL,
    "UpdatedBy" nvarchar(50) NULL,
    "Active" bit NOT NULL DEFAULT 1,
    CONSTRAINT "FK_User_RoleId_Role_Id" FOREIGN KEY ("RoleId") REFERENCES "Role" ("Id") ON DELETE RESTRICT
);|duration: 
2025-07-08 12:33:25.3823|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE TABLE "BitacoraEntries" (
    "Id" INTEGER NOT NULL CONSTRAINT "BitacoraEntriesPK" PRIMARY KEY AUTOINCREMENT,
    "Description" nvarchar(1000) NULL,
    "WasteTypeId" INTEGER NOT NULL,
    "GrossWeight" decimal(18,4) NOT NULL,
    "Tare" decimal(18,4) NOT NULL,
    "NetWeightLB" decimal(18,4) NOT NULL,
    "NetWeightKG" decimal(18,4) NOT NULL,
    "UnitPrice" decimal(18,4) NOT NULL,
    "ContainerTypeId" INTEGER NOT NULL,
    "AreaId" INTEGER NOT NULL,
    "EntryDate" datetime2(3) NOT NULL,
    "DepartureDate" datetime2(3) NULL,
    "Created" datetime2(3) NOT NULL DEFAULT (getutcdate()),
    "CreatedBy" nvarchar(50) NOT NULL DEFAULT (suser_name()),
    "IsDeleted" bit NOT NULL DEFAULT 0,
    "Deleted" datetime2(3) NULL,
    "DeletedBy" nvarchar(50) NULL,
    "Updated" datetime2(3) NULL,
    "UpdatedBy" nvarchar(50) NULL,
    "Active" bit NOT NULL DEFAULT 1,
    CONSTRAINT "FK_BitacoraEntries_Areas" FOREIGN KEY ("AreaId") REFERENCES "Areas" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_BitacoraEntries_ContainerTypes" FOREIGN KEY ("ContainerTypeId") REFERENCES "ContainerTypes" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_BitacoraEntries_WasteTypes" FOREIGN KEY ("WasteTypeId") REFERENCES "WasteTypes" ("Id") ON DELETE RESTRICT
);|duration: 
2025-07-08 12:33:25.3823|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Areas_Active" ON "Areas" ("Active");|duration: 
2025-07-08 12:33:25.3823|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Areas_Active_IsDeleted" ON "Areas" ("Active", "IsDeleted");|duration: 
2025-07-08 12:33:25.3977|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Areas_Created" ON "Areas" ("Created");|duration: 
2025-07-08 12:33:25.3977|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Areas_IsDeleted" ON "Areas" ("IsDeleted");|duration: 
2025-07-08 12:33:25.3977|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Areas_Name_Active" ON "Areas" ("Name", "Active");|duration: 
2025-07-08 12:33:25.3977|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Areas_Name_Created" ON "Areas" ("Name", "Created");|duration: 
2025-07-08 12:33:25.3977|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE UNIQUE INDEX "UQ_Areas_Name_NotDeleted" ON "Areas" ("Name") WHERE [IsDeleted] = 0;|duration: 
2025-07-08 12:33:25.3977|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_BitacoraEntries_Active" ON "BitacoraEntries" ("Active");|duration: 
2025-07-08 12:33:25.3977|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_BitacoraEntries_Active_IsDeleted" ON "BitacoraEntries" ("Active", "IsDeleted");|duration: 
2025-07-08 12:33:25.3977|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_BitacoraEntries_AreaId" ON "BitacoraEntries" ("AreaId");|duration: 
2025-07-08 12:33:25.3977|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_BitacoraEntries_ContainerTypeId" ON "BitacoraEntries" ("ContainerTypeId");|duration: 
2025-07-08 12:33:25.3977|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_BitacoraEntries_Created" ON "BitacoraEntries" ("Created");|duration: 
2025-07-08 12:33:25.4138|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_BitacoraEntries_CreatedBy" ON "BitacoraEntries" ("CreatedBy");|duration: 
2025-07-08 12:33:25.4138|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_BitacoraEntries_DepartureDate" ON "BitacoraEntries" ("DepartureDate");|duration: 
2025-07-08 12:33:25.4138|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_BitacoraEntries_EntryDate" ON "BitacoraEntries" ("EntryDate");|duration: 
2025-07-08 12:33:25.4138|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_BitacoraEntries_IsDeleted" ON "BitacoraEntries" ("IsDeleted");|duration: 
2025-07-08 12:33:25.4138|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_BitacoraEntries_WasteTypeId" ON "BitacoraEntries" ("WasteTypeId");|duration: 
2025-07-08 12:33:25.4138|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_ContainerTypes_Active" ON "ContainerTypes" ("Active");|duration: 
2025-07-08 12:33:25.4138|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_ContainerTypes_Active_IsDeleted" ON "ContainerTypes" ("Active", "IsDeleted");|duration: 
2025-07-08 12:33:25.4138|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_ContainerTypes_Created" ON "ContainerTypes" ("Created");|duration: 
2025-07-08 12:33:25.4138|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_ContainerTypes_IsDeleted" ON "ContainerTypes" ("IsDeleted");|duration: 
2025-07-08 12:33:25.4138|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_ContainerTypes_Name_Active" ON "ContainerTypes" ("Name", "Active");|duration: 
2025-07-08 12:33:25.4295|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_ContainerTypes_Name_Created" ON "ContainerTypes" ("Name", "Created");|duration: 
2025-07-08 12:33:25.4295|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE UNIQUE INDEX "UQ_ContainerTypes_Name_NotDeleted" ON "ContainerTypes" ("Name") WHERE [IsDeleted] = 0;|duration: 
2025-07-08 12:33:25.4295|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Role_Active" ON "Role" ("Active");|duration: 
2025-07-08 12:33:25.4295|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Role_Active_IsDeleted" ON "Role" ("Active", "IsDeleted");|duration: 
2025-07-08 12:33:25.4295|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Role_Created" ON "Role" ("Created");|duration: 
2025-07-08 12:33:25.4295|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Role_IsDeleted" ON "Role" ("IsDeleted");|duration: 
2025-07-08 12:33:25.4295|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Role_Name_Active" ON "Role" ("Name", "Active");|duration: 
2025-07-08 12:33:25.4295|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Role_Name_Created" ON "Role" ("Name", "Created");|duration: 
2025-07-08 12:33:25.4295|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE UNIQUE INDEX "UQ_Role_Name_NotDeleted" ON "Role" ("Name") WHERE [IsDeleted] = 0;|duration: 
2025-07-08 12:33:25.4295|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_Active" ON "User" ("Active");|duration: 
2025-07-08 12:33:25.4455|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_Active_IsDeleted" ON "User" ("Active", "IsDeleted");|duration: 
2025-07-08 12:33:25.4455|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_Created" ON "User" ("Created");|duration: 
2025-07-08 12:33:25.4455|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_Email_Active" ON "User" ("Email", "Active");|duration: 
2025-07-08 12:33:25.4455|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_IsDeleted" ON "User" ("IsDeleted");|duration: 
2025-07-08 12:33:25.4455|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_Name" ON "User" ("Name");|duration: 
2025-07-08 12:33:25.4455|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_Name_Created" ON "User" ("Name", "Created");|duration: 
2025-07-08 12:33:25.4455|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_RoleId" ON "User" ("RoleId");|duration: 
2025-07-08 12:33:25.4455|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_RoleId_Active" ON "User" ("RoleId", "Active");|duration: 
2025-07-08 12:33:25.4455|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE UNIQUE INDEX "UQ_User_Email_NotDeleted" ON "User" ("Email") WHERE [IsDeleted] = 0;|duration: 
2025-07-08 12:33:25.4455|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE UNIQUE INDEX "UQ_User_EmployeeNumber_NotDeleted" ON "User" ("EmployeeNumber") WHERE [IsDeleted] = 0;|duration: 
2025-07-08 12:33:25.4455|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_WasteTypes_Active" ON "WasteTypes" ("Active");|duration: 
2025-07-08 12:33:25.4598|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_WasteTypes_Active_IsDeleted" ON "WasteTypes" ("Active", "IsDeleted");|duration: 
2025-07-08 12:33:25.4598|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_WasteTypes_Created" ON "WasteTypes" ("Created");|duration: 
2025-07-08 12:33:25.4598|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_WasteTypes_IsDeleted" ON "WasteTypes" ("IsDeleted");|duration: 
2025-07-08 12:33:25.4598|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_WasteTypes_Name_Active" ON "WasteTypes" ("Name", "Active");|duration: 
2025-07-08 12:33:25.4598|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_WasteTypes_Name_Created" ON "WasteTypes" ("Name", "Created");|duration: 
2025-07-08 12:33:25.4598|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE UNIQUE INDEX "UQ_WasteTypes_Name_NotDeleted" ON "WasteTypes" ("Name") WHERE [IsDeleted] = 0;|duration: 
2025-07-08 12:33:25.4755|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Program|Base de datos inicializada correctamente|duration: 
2025-07-08 12:33:25.4755|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|Inicializando base de datos con datos de prueba...|duration: 
2025-07-08 12:33:25.6478|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (2ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM "Role" AS "r")|duration: 
2025-07-08 12:33:25.7904|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (4ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (Size = 6), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (Size = 45), @p5='?' (Size = 5), @p6='?' (DbType = DateTime), @p7='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "Role" ("Created", "CreatedBy", "Deleted", "DeletedBy", "Description", "Name", "Updated", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:33:25.8204|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|Rol Admin creado exitosamente|duration: 
2025-07-08 12:33:26.1956|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (2ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (Size = 6), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (Size = 27), @p5='?' (Size = 6), @p6='?' (Size = 17), @p7='?' (Size = 60), @p8='?' (DbType = Int32), @p9='?' (DbType = DateTime), @p10='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "User" ("Created", "CreatedBy", "Deleted", "DeletedBy", "Email", "EmployeeNumber", "Name", "Password", "RoleId", "Updated", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:33:26.2169|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|Usuario administrador creado exitosamente|duration: 
2025-07-08 12:33:26.2488|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (Size = 6), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (Size = 33), @p5='?' (Size = 4), @p6='?' (DbType = DateTime), @p7='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "Areas" ("Created", "CreatedBy", "Deleted", "DeletedBy", "Description", "Name", "Updated", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:33:26.2575|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (Size = 6), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (Size = 11), @p5='?' (Size = 6), @p6='?' (DbType = DateTime), @p7='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "Areas" ("Created", "CreatedBy", "Deleted", "DeletedBy", "Description", "Name", "Updated", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:33:26.2575|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (Size = 6), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (Size = 15), @p5='?' (Size = 10), @p6='?' (DbType = DateTime), @p7='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "Areas" ("Created", "CreatedBy", "Deleted", "DeletedBy", "Description", "Name", "Updated", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:33:26.2575|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|Areas creadas exitosamente|duration: 
2025-07-08 12:33:26.2887|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (Size = 6), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (Size = 17), @p5='?' (Size = 5), @p6='?' (DbType = DateTime), @p7='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "WasteTypes" ("Created", "CreatedBy", "Deleted", "DeletedBy", "Description", "Name", "Updated", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:33:26.2887|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (Size = 6), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (Size = 19), @p5='?' (Size = 7), @p6='?' (DbType = DateTime), @p7='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "WasteTypes" ("Created", "CreatedBy", "Deleted", "DeletedBy", "Description", "Name", "Updated", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:33:26.2887|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (Size = 6), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (Size = 19), @p5='?' (Size = 7), @p6='?' (DbType = DateTime), @p7='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "WasteTypes" ("Created", "CreatedBy", "Deleted", "DeletedBy", "Description", "Name", "Updated", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:33:26.2887|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (Size = 6), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (Size = 18), @p5='?' (Size = 6), @p6='?' (DbType = DateTime), @p7='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "WasteTypes" ("Created", "CreatedBy", "Deleted", "DeletedBy", "Description", "Name", "Updated", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:33:26.3049|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|WasteTypes creados exitosamente|duration: 
2025-07-08 12:33:26.3237|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (Size = 6), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (Size = 22), @p5='?' (Size = 6), @p6='?' (DbType = DateTime), @p7='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "ContainerTypes" ("Created", "CreatedBy", "Deleted", "DeletedBy", "Description", "Name", "Updated", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:33:26.3352|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|ContainerTypes creados exitosamente|duration: 
2025-07-08 12:33:26.3879|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (Size = 44), @p2='?' (DbType = Int32), @p3='?' (DbType = DateTime), @p4='?' (Size = 6), @p5='?' (DbType = DateTime), @p6='?', @p7='?' (DbType = DateTime), @p8='?' (DbType = DateTime), @p9='?' (DbType = Decimal), @p10='?' (DbType = Decimal), @p11='?' (DbType = Decimal), @p12='?' (DbType = Decimal), @p13='?' (DbType = Decimal), @p14='?' (DbType = DateTime), @p15='?', @p16='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
INSERT INTO "BitacoraEntries" ("AreaId", "Description", "ContainerTypeId", "Created", "CreatedBy", "Deleted", "DeletedBy", "DepartureDate", "EntryDate", "GrossWeight", "NetWeightKG", "NetWeightLB", "Tare", "UnitPrice", "Updated", "UpdatedBy", "WasteTypeId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:33:26.3879|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (Size = 53), @p2='?' (DbType = Int32), @p3='?' (DbType = DateTime), @p4='?' (Size = 6), @p5='?' (DbType = DateTime), @p6='?', @p7='?' (DbType = DateTime), @p8='?' (DbType = DateTime), @p9='?' (DbType = Decimal), @p10='?' (DbType = Decimal), @p11='?' (DbType = Decimal), @p12='?' (DbType = Decimal), @p13='?' (DbType = Decimal), @p14='?' (DbType = DateTime), @p15='?', @p16='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
INSERT INTO "BitacoraEntries" ("AreaId", "Description", "ContainerTypeId", "Created", "CreatedBy", "Deleted", "DeletedBy", "DepartureDate", "EntryDate", "GrossWeight", "NetWeightKG", "NetWeightLB", "Tare", "UnitPrice", "Updated", "UpdatedBy", "WasteTypeId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:33:26.3977|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (Size = 56), @p2='?' (DbType = Int32), @p3='?' (DbType = DateTime), @p4='?' (Size = 6), @p5='?' (DbType = DateTime), @p6='?', @p7='?' (DbType = DateTime), @p8='?' (DbType = DateTime), @p9='?' (DbType = Decimal), @p10='?' (DbType = Decimal), @p11='?' (DbType = Decimal), @p12='?' (DbType = Decimal), @p13='?' (DbType = Decimal), @p14='?' (DbType = DateTime), @p15='?', @p16='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
INSERT INTO "BitacoraEntries" ("AreaId", "Description", "ContainerTypeId", "Created", "CreatedBy", "Deleted", "DeletedBy", "DepartureDate", "EntryDate", "GrossWeight", "NetWeightKG", "NetWeightLB", "Tare", "UnitPrice", "Updated", "UpdatedBy", "WasteTypeId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:33:26.3977|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (Size = 46), @p2='?' (DbType = Int32), @p3='?' (DbType = DateTime), @p4='?' (Size = 6), @p5='?' (DbType = DateTime), @p6='?', @p7='?' (DbType = DateTime), @p8='?' (DbType = DateTime), @p9='?' (DbType = Decimal), @p10='?' (DbType = Decimal), @p11='?' (DbType = Decimal), @p12='?' (DbType = Decimal), @p13='?' (DbType = Decimal), @p14='?' (DbType = DateTime), @p15='?', @p16='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
INSERT INTO "BitacoraEntries" ("AreaId", "Description", "ContainerTypeId", "Created", "CreatedBy", "Deleted", "DeletedBy", "DepartureDate", "EntryDate", "GrossWeight", "NetWeightKG", "NetWeightLB", "Tare", "UnitPrice", "Updated", "UpdatedBy", "WasteTypeId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:33:26.3977|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|BitacoraEntries de prueba creadas exitosamente|duration: 
2025-07-08 12:33:26.3977|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|Inicialización de base de datos completada exitosamente|duration: 
2025-07-08 12:33:26.3977|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Program|Datos de prueba inicializados correctamente|duration: 
2025-07-08 12:33:26.4976|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Program|Aplicación iniciada correctamente|duration: 
2025-07-08 12:33:26.5580|INFO|Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager|User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.|duration: 
2025-07-08 12:33:26.6165|WARN|Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware|The WebRootPath was not found: C:\Repositories\WebProjects\BitacoraResiduosESH\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend.Backend.WebAPI\wwwroot. Static files may be unavailable.|duration: 
2025-07-08 12:33:26.6955|INFO|Microsoft.Hosting.Lifetime|Now listening on: http://localhost:5001|duration: 
2025-07-08 12:33:26.6955|INFO|Microsoft.Hosting.Lifetime|Application started. Press Ctrl+C to shut down.|duration: 
2025-07-08 12:33:26.6955|INFO|Microsoft.Hosting.Lifetime|Hosting environment: Development|duration: 
2025-07-08 12:33:26.6955|INFO|Microsoft.Hosting.Lifetime|Content root path: C:\Repositories\WebProjects\BitacoraResiduosESH\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend.Backend.WebAPI|duration: 
2025-07-08 12:33:27.4925|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar - - -|duration: 2.685
2025-07-08 12:33:27.5417|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 53.594
2025-07-08 12:33:27.5417|INFO|Microsoft.AspNetCore.Http.Result.RedirectResult|Executing RedirectResult, redirecting to scalar/.|duration: 61.877
2025-07-08 12:33:27.5417|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 62.283
2025-07-08 12:33:27.5550|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar - 302 0 - 64.5008ms|duration: 67.253
2025-07-08 12:33:27.5550|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar/ - - -|duration: 0.263
2025-07-08 12:33:27.5550|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 2.823
2025-07-08 12:33:27.5952|INFO|Microsoft.AspNetCore.Http.Result.ContentResult|Write content with HTTP Response ContentType of text/html|duration: 39.045
2025-07-08 12:33:27.5952|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 39.738
2025-07-08 12:33:27.6007|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar/ - 200 924 text/html 39.9490ms|duration: 40.246
2025-07-08 12:33:27.6007|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar/scalar.js - - -|duration: 0.201
2025-07-08 12:33:27.6007|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar/scalar.aspnetcore.js - - -|duration: 0.094
2025-07-08 12:33:27.6007|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/scalar.js'|duration: 2.902
2025-07-08 12:33:27.6170|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 2.161
2025-07-08 12:33:27.6170|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/scalar.aspnetcore.js'|duration: 2.737
2025-07-08 12:33:27.6170|INFO|Microsoft.AspNetCore.Http.Result.StatusCodeResult|Setting HTTP status code 304.|duration: 11.820
2025-07-08 12:33:27.6170|INFO|Microsoft.AspNetCore.Http.Result.StatusCodeResult|Setting HTTP status code 304.|duration: 9.270
2025-07-08 12:33:27.6170|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/scalar.js'|duration: 12.137
2025-07-08 12:33:27.6170|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/scalar.aspnetcore.js'|duration: 9.698
2025-07-08 12:33:27.6170|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar/scalar.js - 304 - - 12.3718ms|duration: 12.545
2025-07-08 12:33:27.6170|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar/scalar.aspnetcore.js - 304 - - 10.5151ms|duration: 10.669
2025-07-08 12:33:27.7958|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/openapi/v1.json - - -|duration: 0.196
2025-07-08 12:33:27.7958|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /openapi/{documentName}.json'|duration: 1.593
2025-07-08 12:33:28.0061|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /openapi/{documentName}.json'|duration: 210.460
2025-07-08 12:33:28.0073|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/openapi/v1.json - 200 - application/json;charset=utf-8 211.7691ms|duration: 211.897
2025-07-08 12:33:52.9946|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/api/BitacoraEntry - - -|duration: 1.024
2025-07-08 12:33:52.9946|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 5.245
2025-07-08 12:33:53.0878|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Route matched with {action = "GetAll", controller = "BitacoraEntry"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[BitacoraResiduosESH.Backend.Backend.Application.Common.PagedResponse`1[BitacoraResiduosESH.Backend.Backend.Application.DTOs.BitacoraEntry.BitacoraEntryDto]]] GetAll(Int32, Int32, Boolean) on controller BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController (BitacoraResiduosESH.Backend.Backend.WebAPI).|duration: 93.656
2025-07-08 12:33:53.2748|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executing action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI) - Validation state: Valid|duration: 280.568
2025-07-08 12:33:53.5708|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (2ms) [Parameters=[@__filter_IncludeDeleted_0='?' (DbType = Boolean)], CommandType='Text', CommandTimeout='30']
SELECT COUNT(*)
FROM "BitacoraEntries" AS "b"
WHERE "b"."IsDeleted" = @__filter_IncludeDeleted_0|duration: 577.291
2025-07-08 12:33:53.9141|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (4ms) [Parameters=[@__filter_IncludeDeleted_0='?' (DbType = Boolean), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
SELECT "b0"."Id", "b0"."Active", "b0"."AreaId", "b0"."Description", "b0"."ContainerTypeId", "b0"."Created", "b0"."CreatedBy", "b0"."Deleted", "b0"."DeletedBy", "b0"."DepartureDate", "b0"."EntryDate", "b0"."GrossWeight", "b0"."IsDeleted", "b0"."NetWeightKG", "b0"."NetWeightLB", "b0"."Tare", "b0"."UnitPrice", "b0"."Updated", "b0"."UpdatedBy", "b0"."WasteTypeId", "w"."Id", "w"."Active", "w"."Created", "w"."CreatedBy", "w"."Deleted", "w"."DeletedBy", "w"."Description", "w"."IsDeleted", "w"."Name", "w"."Updated", "w"."UpdatedBy", "c"."Id", "c"."Active", "c"."Created", "c"."CreatedBy", "c"."Deleted", "c"."DeletedBy", "c"."Description", "c"."IsDeleted", "c"."Name", "c"."Updated", "c"."UpdatedBy", "a"."Id", "a"."Active", "a"."Created", "a"."CreatedBy", "a"."Deleted", "a"."DeletedBy", "a"."Description", "a"."IsDeleted", "a"."Name", "a"."Updated", "a"."UpdatedBy"
FROM (
    SELECT "b"."Id", "b"."Active", "b"."AreaId", "b"."Description", "b"."ContainerTypeId", "b"."Created", "b"."CreatedBy", "b"."Deleted", "b"."DeletedBy", "b"."DepartureDate", "b"."EntryDate", "b"."GrossWeight", "b"."IsDeleted", "b"."NetWeightKG", "b"."NetWeightLB", "b"."Tare", "b"."UnitPrice", "b"."Updated", "b"."UpdatedBy", "b"."WasteTypeId"
    FROM "BitacoraEntries" AS "b"
    WHERE "b"."IsDeleted" = @__filter_IncludeDeleted_0
    ORDER BY "b"."Id"
    LIMIT @__p_2 OFFSET @__p_1
) AS "b0"
INNER JOIN "WasteTypes" AS "w" ON "b0"."WasteTypeId" = "w"."Id"
INNER JOIN "ContainerTypes" AS "c" ON "b0"."ContainerTypeId" = "c"."Id"
INNER JOIN "Areas" AS "a" ON "b0"."AreaId" = "a"."Id"
ORDER BY "b0"."Id"|duration: 930.403
2025-07-08 12:33:54.0253|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 734.9029ms.|duration: 1031.105
2025-07-08 12:33:54.0860|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor|Executing OkObjectResult, writing value of type 'BitacoraResiduosESH.Backend.Backend.Application.Common.PagedResponse`1[[BitacoraResiduosESH.Backend.Backend.Application.DTOs.BitacoraEntry.BitacoraEntryDto, BitacoraResiduosESH.Backend.Backend.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]'.|duration: 1095.997
2025-07-08 12:33:54.1488|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI) in 1054.7223ms|duration: 1166.683
2025-07-08 12:33:54.1488|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 1167.371
2025-07-08 12:33:54.1642|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/api/BitacoraEntry - 200 - application/json;+charset=utf-8 1179.5275ms|duration: 1179.770
2025-07-08 12:34:06.2714|INFO|Microsoft.Hosting.Lifetime|Application is shutting down...|duration: 
2025-07-08 12:36:12.6825|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (68ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;|duration: 
2025-07-08 12:36:12.7188|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Program|Base de datos inicializada correctamente|duration: 
2025-07-08 12:36:12.7617|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|Inicializando base de datos con datos de prueba...|duration: 
2025-07-08 12:36:13.6642|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (9ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM "Role" AS "r")|duration: 
2025-07-08 12:36:13.7099|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|La base de datos ya contiene datos. Saltando inicialización.|duration: 
2025-07-08 12:36:13.7099|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Program|Datos de prueba inicializados correctamente|duration: 
2025-07-08 12:36:14.1102|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Program|Aplicación iniciada correctamente|duration: 
2025-07-08 12:36:14.3279|INFO|Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager|User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.|duration: 
2025-07-08 12:36:14.5396|WARN|Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware|The WebRootPath was not found: C:\Repositories\WebProjects\BitacoraResiduosESH\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend.Backend.WebAPI\wwwroot. Static files may be unavailable.|duration: 
2025-07-08 12:36:14.8534|INFO|Microsoft.Hosting.Lifetime|Now listening on: http://localhost:5001|duration: 
2025-07-08 12:36:14.8733|INFO|Microsoft.Hosting.Lifetime|Application started. Press Ctrl+C to shut down.|duration: 
2025-07-08 12:36:14.8733|INFO|Microsoft.Hosting.Lifetime|Hosting environment: Development|duration: 
2025-07-08 12:36:14.8733|INFO|Microsoft.Hosting.Lifetime|Content root path: C:\Repositories\WebProjects\BitacoraResiduosESH\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend.Backend.WebAPI|duration: 
2025-07-08 12:36:22.8308|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar - - -|duration: 10.485
2025-07-08 12:36:23.0394|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 223.307
2025-07-08 12:36:23.0705|INFO|Microsoft.AspNetCore.Http.Result.RedirectResult|Executing RedirectResult, redirecting to scalar/.|duration: 253.181
2025-07-08 12:36:23.0705|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 254.126
2025-07-08 12:36:23.0909|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar - 302 0 - 266.5992ms|duration: 278.733
2025-07-08 12:36:23.1037|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar/ - - -|duration: 0.440
2025-07-08 12:36:23.1037|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 8.380
2025-07-08 12:36:23.2727|INFO|Microsoft.AspNetCore.Http.Result.ContentResult|Write content with HTTP Response ContentType of text/html|duration: 172.224
2025-07-08 12:36:23.2727|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 174.266
2025-07-08 12:36:23.2727|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar/ - 200 924 text/html 175.4233ms|duration: 176.032
2025-07-08 12:36:23.2946|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar/scalar.js - - -|duration: 0.356
2025-07-08 12:36:23.2946|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar/scalar.aspnetcore.js - - -|duration: 0.447
2025-07-08 12:36:23.3042|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/scalar.js'|duration: 10.395
2025-07-08 12:36:23.3042|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 11.990
2025-07-08 12:36:23.3042|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/scalar.aspnetcore.js'|duration: 14.144
2025-07-08 12:36:23.3369|INFO|Microsoft.AspNetCore.Http.Result.StatusCodeResult|Setting HTTP status code 304.|duration: 44.489
2025-07-08 12:36:23.3369|INFO|Microsoft.AspNetCore.Http.Result.StatusCodeResult|Setting HTTP status code 304.|duration: 44.641
2025-07-08 12:36:23.3369|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/scalar.js'|duration: 45.390
2025-07-08 12:36:23.3369|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/scalar.aspnetcore.js'|duration: 45.631
2025-07-08 12:36:23.3369|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar/scalar.js - 304 - - 46.8049ms|duration: 47.149
2025-07-08 12:36:23.3369|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar/scalar.aspnetcore.js - 304 - - 49.0241ms|duration: 49.340
2025-07-08 12:36:24.0984|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/openapi/v1.json - - -|duration: 0.871
2025-07-08 12:36:24.1003|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /openapi/{documentName}.json'|duration: 5.132
2025-07-08 12:36:24.9753|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /openapi/{documentName}.json'|duration: 877.245
2025-07-08 12:36:24.9798|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/openapi/v1.json - 200 - application/json;charset=utf-8 882.2452ms|duration: 882.535
2025-07-08 12:38:08.4804|INFO|Microsoft.Hosting.Lifetime|Application is shutting down...|duration: 
2025-07-08 12:38:22.9863|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (25ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
PRAGMA journal_mode = 'wal';|duration: 
2025-07-08 12:38:23.1024|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE TABLE "Areas" (
    "Id" INTEGER NOT NULL CONSTRAINT "AreasPK" PRIMARY KEY AUTOINCREMENT,
    "Created" datetime2(3) NOT NULL DEFAULT (getutcdate()),
    "CreatedBy" nvarchar(50) NOT NULL DEFAULT (suser_name()),
    "IsDeleted" bit NOT NULL DEFAULT 0,
    "Deleted" datetime2(3) NULL,
    "DeletedBy" nvarchar(50) NULL,
    "Updated" datetime2(3) NULL,
    "UpdatedBy" nvarchar(50) NULL,
    "Active" bit NOT NULL DEFAULT 1,
    "Name" nvarchar(200) NOT NULL,
    "Description" nvarchar(1000) NULL
);|duration: 
2025-07-08 12:38:23.1024|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE TABLE "ContainerTypes" (
    "Id" INTEGER NOT NULL CONSTRAINT "ContainerTypesPK" PRIMARY KEY AUTOINCREMENT,
    "Created" datetime2(3) NOT NULL DEFAULT (getutcdate()),
    "CreatedBy" nvarchar(50) NOT NULL DEFAULT (suser_name()),
    "IsDeleted" bit NOT NULL DEFAULT 0,
    "Deleted" datetime2(3) NULL,
    "DeletedBy" nvarchar(50) NULL,
    "Updated" datetime2(3) NULL,
    "UpdatedBy" nvarchar(50) NULL,
    "Active" bit NOT NULL DEFAULT 1,
    "Name" nvarchar(200) NOT NULL,
    "Description" nvarchar(1000) NULL
);|duration: 
2025-07-08 12:38:23.1024|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE TABLE "Role" (
    "Id" INTEGER NOT NULL CONSTRAINT "RolePK" PRIMARY KEY AUTOINCREMENT,
    "Created" datetime2(3) NOT NULL DEFAULT (getutcdate()),
    "CreatedBy" nvarchar(50) NOT NULL DEFAULT (suser_name()),
    "IsDeleted" bit NOT NULL DEFAULT 0,
    "Deleted" datetime2(3) NULL,
    "DeletedBy" nvarchar(50) NULL,
    "Updated" datetime2(3) NULL,
    "UpdatedBy" nvarchar(50) NULL,
    "Active" bit NOT NULL DEFAULT 1,
    "Name" nvarchar(200) NOT NULL,
    "Description" nvarchar(1000) NULL
);|duration: 
2025-07-08 12:38:23.1024|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE TABLE "WasteTypes" (
    "Id" INTEGER NOT NULL CONSTRAINT "WasteTypesPK" PRIMARY KEY AUTOINCREMENT,
    "Created" datetime2(3) NOT NULL DEFAULT (getutcdate()),
    "CreatedBy" nvarchar(50) NOT NULL DEFAULT (suser_name()),
    "IsDeleted" bit NOT NULL DEFAULT 0,
    "Deleted" datetime2(3) NULL,
    "DeletedBy" nvarchar(50) NULL,
    "Updated" datetime2(3) NULL,
    "UpdatedBy" nvarchar(50) NULL,
    "Active" bit NOT NULL DEFAULT 1,
    "Name" nvarchar(200) NOT NULL,
    "Description" nvarchar(1000) NULL
);|duration: 
2025-07-08 12:38:23.1024|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE TABLE "User" (
    "Id" INTEGER NOT NULL CONSTRAINT "UserPK" PRIMARY KEY AUTOINCREMENT,
    "Name" nvarchar(200) NOT NULL,
    "EmployeeNumber" nvarchar(30) NOT NULL,
    "Email" nvarchar(255) NULL,
    "Password" nvarchar(255) NOT NULL,
    "RoleId" INTEGER NOT NULL,
    "Created" datetime2(3) NOT NULL DEFAULT (getutcdate()),
    "CreatedBy" nvarchar(50) NOT NULL DEFAULT (suser_name()),
    "IsDeleted" bit NOT NULL DEFAULT 0,
    "Deleted" datetime2(3) NULL,
    "DeletedBy" nvarchar(50) NULL,
    "Updated" datetime2(3) NULL,
    "UpdatedBy" nvarchar(50) NULL,
    "Active" bit NOT NULL DEFAULT 1,
    CONSTRAINT "FK_User_RoleId_Role_Id" FOREIGN KEY ("RoleId") REFERENCES "Role" ("Id") ON DELETE RESTRICT
);|duration: 
2025-07-08 12:38:23.1024|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE TABLE "BitacoraEntries" (
    "Id" INTEGER NOT NULL CONSTRAINT "BitacoraEntriesPK" PRIMARY KEY AUTOINCREMENT,
    "Description" nvarchar(1000) NULL,
    "WasteTypeId" INTEGER NOT NULL,
    "GrossWeight" decimal(18,4) NOT NULL,
    "Tare" decimal(18,4) NOT NULL,
    "NetWeightLB" decimal(18,4) NOT NULL,
    "NetWeightKG" decimal(18,4) NOT NULL,
    "UnitPrice" decimal(18,4) NOT NULL,
    "ContainerTypeId" INTEGER NOT NULL,
    "AreaId" INTEGER NOT NULL,
    "EntryDate" datetime2(3) NOT NULL,
    "DepartureDate" datetime2(3) NULL,
    "Created" datetime2(3) NOT NULL DEFAULT (getutcdate()),
    "CreatedBy" nvarchar(50) NOT NULL DEFAULT (suser_name()),
    "IsDeleted" bit NOT NULL DEFAULT 0,
    "Deleted" datetime2(3) NULL,
    "DeletedBy" nvarchar(50) NULL,
    "Updated" datetime2(3) NULL,
    "UpdatedBy" nvarchar(50) NULL,
    "Active" bit NOT NULL DEFAULT 1,
    CONSTRAINT "FK_BitacoraEntries_Areas" FOREIGN KEY ("AreaId") REFERENCES "Areas" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_BitacoraEntries_ContainerTypes" FOREIGN KEY ("ContainerTypeId") REFERENCES "ContainerTypes" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_BitacoraEntries_WasteTypes" FOREIGN KEY ("WasteTypeId") REFERENCES "WasteTypes" ("Id") ON DELETE RESTRICT
);|duration: 
2025-07-08 12:38:23.1024|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Areas_Active" ON "Areas" ("Active");|duration: 
2025-07-08 12:38:23.1024|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Areas_Active_IsDeleted" ON "Areas" ("Active", "IsDeleted");|duration: 
2025-07-08 12:38:23.1172|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Areas_Created" ON "Areas" ("Created");|duration: 
2025-07-08 12:38:23.1172|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Areas_IsDeleted" ON "Areas" ("IsDeleted");|duration: 
2025-07-08 12:38:23.1172|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Areas_Name_Active" ON "Areas" ("Name", "Active");|duration: 
2025-07-08 12:38:23.1172|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Areas_Name_Created" ON "Areas" ("Name", "Created");|duration: 
2025-07-08 12:38:23.1172|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE UNIQUE INDEX "UQ_Areas_Name_NotDeleted" ON "Areas" ("Name") WHERE [IsDeleted] = 0;|duration: 
2025-07-08 12:38:23.1172|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_BitacoraEntries_Active" ON "BitacoraEntries" ("Active");|duration: 
2025-07-08 12:38:23.1172|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_BitacoraEntries_Active_IsDeleted" ON "BitacoraEntries" ("Active", "IsDeleted");|duration: 
2025-07-08 12:38:23.1172|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_BitacoraEntries_AreaId" ON "BitacoraEntries" ("AreaId");|duration: 
2025-07-08 12:38:23.1172|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_BitacoraEntries_ContainerTypeId" ON "BitacoraEntries" ("ContainerTypeId");|duration: 
2025-07-08 12:38:23.1172|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_BitacoraEntries_Created" ON "BitacoraEntries" ("Created");|duration: 
2025-07-08 12:38:23.1172|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_BitacoraEntries_CreatedBy" ON "BitacoraEntries" ("CreatedBy");|duration: 
2025-07-08 12:38:23.1172|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_BitacoraEntries_DepartureDate" ON "BitacoraEntries" ("DepartureDate");|duration: 
2025-07-08 12:38:23.1172|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_BitacoraEntries_EntryDate" ON "BitacoraEntries" ("EntryDate");|duration: 
2025-07-08 12:38:23.1321|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_BitacoraEntries_IsDeleted" ON "BitacoraEntries" ("IsDeleted");|duration: 
2025-07-08 12:38:23.1321|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_BitacoraEntries_WasteTypeId" ON "BitacoraEntries" ("WasteTypeId");|duration: 
2025-07-08 12:38:23.1321|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_ContainerTypes_Active" ON "ContainerTypes" ("Active");|duration: 
2025-07-08 12:38:23.1321|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_ContainerTypes_Active_IsDeleted" ON "ContainerTypes" ("Active", "IsDeleted");|duration: 
2025-07-08 12:38:23.1321|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_ContainerTypes_Created" ON "ContainerTypes" ("Created");|duration: 
2025-07-08 12:38:23.1321|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_ContainerTypes_IsDeleted" ON "ContainerTypes" ("IsDeleted");|duration: 
2025-07-08 12:38:23.1321|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_ContainerTypes_Name_Active" ON "ContainerTypes" ("Name", "Active");|duration: 
2025-07-08 12:38:23.1321|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_ContainerTypes_Name_Created" ON "ContainerTypes" ("Name", "Created");|duration: 
2025-07-08 12:38:23.1321|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE UNIQUE INDEX "UQ_ContainerTypes_Name_NotDeleted" ON "ContainerTypes" ("Name") WHERE [IsDeleted] = 0;|duration: 
2025-07-08 12:38:23.1321|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Role_Active" ON "Role" ("Active");|duration: 
2025-07-08 12:38:23.1321|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Role_Active_IsDeleted" ON "Role" ("Active", "IsDeleted");|duration: 
2025-07-08 12:38:23.1321|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Role_Created" ON "Role" ("Created");|duration: 
2025-07-08 12:38:23.1321|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Role_IsDeleted" ON "Role" ("IsDeleted");|duration: 
2025-07-08 12:38:23.1478|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Role_Name_Active" ON "Role" ("Name", "Active");|duration: 
2025-07-08 12:38:23.1478|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_Role_Name_Created" ON "Role" ("Name", "Created");|duration: 
2025-07-08 12:38:23.1478|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE UNIQUE INDEX "UQ_Role_Name_NotDeleted" ON "Role" ("Name") WHERE [IsDeleted] = 0;|duration: 
2025-07-08 12:38:23.1478|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_Active" ON "User" ("Active");|duration: 
2025-07-08 12:38:23.1478|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_Active_IsDeleted" ON "User" ("Active", "IsDeleted");|duration: 
2025-07-08 12:38:23.1478|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_Created" ON "User" ("Created");|duration: 
2025-07-08 12:38:23.1478|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_Email_Active" ON "User" ("Email", "Active");|duration: 
2025-07-08 12:38:23.1478|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_IsDeleted" ON "User" ("IsDeleted");|duration: 
2025-07-08 12:38:23.1478|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_Name" ON "User" ("Name");|duration: 
2025-07-08 12:38:23.1478|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_Name_Created" ON "User" ("Name", "Created");|duration: 
2025-07-08 12:38:23.1478|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_RoleId" ON "User" ("RoleId");|duration: 
2025-07-08 12:38:23.1478|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_User_RoleId_Active" ON "User" ("RoleId", "Active");|duration: 
2025-07-08 12:38:23.1629|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE UNIQUE INDEX "UQ_User_Email_NotDeleted" ON "User" ("Email") WHERE [IsDeleted] = 0;|duration: 
2025-07-08 12:38:23.1629|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE UNIQUE INDEX "UQ_User_EmployeeNumber_NotDeleted" ON "User" ("EmployeeNumber") WHERE [IsDeleted] = 0;|duration: 
2025-07-08 12:38:23.1629|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_WasteTypes_Active" ON "WasteTypes" ("Active");|duration: 
2025-07-08 12:38:23.1629|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_WasteTypes_Active_IsDeleted" ON "WasteTypes" ("Active", "IsDeleted");|duration: 
2025-07-08 12:38:23.1629|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_WasteTypes_Created" ON "WasteTypes" ("Created");|duration: 
2025-07-08 12:38:23.1629|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_WasteTypes_IsDeleted" ON "WasteTypes" ("IsDeleted");|duration: 
2025-07-08 12:38:23.1629|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_WasteTypes_Name_Active" ON "WasteTypes" ("Name", "Active");|duration: 
2025-07-08 12:38:23.1629|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE INDEX "IX_WasteTypes_Name_Created" ON "WasteTypes" ("Name", "Created");|duration: 
2025-07-08 12:38:23.1629|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE UNIQUE INDEX "UQ_WasteTypes_Name_NotDeleted" ON "WasteTypes" ("Name") WHERE [IsDeleted] = 0;|duration: 
2025-07-08 12:38:23.1807|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Program|Base de datos inicializada correctamente|duration: 
2025-07-08 12:38:23.1807|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|Inicializando base de datos con datos de prueba...|duration: 
2025-07-08 12:38:23.3702|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (5ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM "Role" AS "r")|duration: 
2025-07-08 12:38:23.5236|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (4ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (Size = 6), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (Size = 45), @p5='?' (Size = 5), @p6='?' (DbType = DateTime), @p7='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "Role" ("Created", "CreatedBy", "Deleted", "DeletedBy", "Description", "Name", "Updated", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:38:23.5398|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|Rol Admin creado exitosamente|duration: 
2025-07-08 12:38:23.8676|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (2ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (Size = 6), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (Size = 27), @p5='?' (Size = 6), @p6='?' (Size = 17), @p7='?' (Size = 60), @p8='?' (DbType = Int32), @p9='?' (DbType = DateTime), @p10='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "User" ("Created", "CreatedBy", "Deleted", "DeletedBy", "Email", "EmployeeNumber", "Name", "Password", "RoleId", "Updated", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:38:23.8676|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|Usuario administrador creado exitosamente|duration: 
2025-07-08 12:38:23.8982|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (Size = 6), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (Size = 33), @p5='?' (Size = 4), @p6='?' (DbType = DateTime), @p7='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "Areas" ("Created", "CreatedBy", "Deleted", "DeletedBy", "Description", "Name", "Updated", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:38:23.8982|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (Size = 6), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (Size = 11), @p5='?' (Size = 6), @p6='?' (DbType = DateTime), @p7='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "Areas" ("Created", "CreatedBy", "Deleted", "DeletedBy", "Description", "Name", "Updated", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:38:23.9134|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (2ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (Size = 6), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (Size = 15), @p5='?' (Size = 10), @p6='?' (DbType = DateTime), @p7='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "Areas" ("Created", "CreatedBy", "Deleted", "DeletedBy", "Description", "Name", "Updated", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:38:23.9134|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|Areas creadas exitosamente|duration: 
2025-07-08 12:38:23.9440|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (Size = 6), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (Size = 17), @p5='?' (Size = 5), @p6='?' (DbType = DateTime), @p7='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "WasteTypes" ("Created", "CreatedBy", "Deleted", "DeletedBy", "Description", "Name", "Updated", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:38:23.9440|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (Size = 6), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (Size = 19), @p5='?' (Size = 7), @p6='?' (DbType = DateTime), @p7='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "WasteTypes" ("Created", "CreatedBy", "Deleted", "DeletedBy", "Description", "Name", "Updated", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:38:23.9440|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (Size = 6), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (Size = 19), @p5='?' (Size = 7), @p6='?' (DbType = DateTime), @p7='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "WasteTypes" ("Created", "CreatedBy", "Deleted", "DeletedBy", "Description", "Name", "Updated", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:38:23.9440|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (Size = 6), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (Size = 18), @p5='?' (Size = 6), @p6='?' (DbType = DateTime), @p7='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "WasteTypes" ("Created", "CreatedBy", "Deleted", "DeletedBy", "Description", "Name", "Updated", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:38:23.9613|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|WasteTypes creados exitosamente|duration: 
2025-07-08 12:38:23.9766|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (Size = 6), @p2='?' (DbType = DateTime), @p3='?', @p4='?' (Size = 22), @p5='?' (Size = 6), @p6='?' (DbType = DateTime), @p7='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "ContainerTypes" ("Created", "CreatedBy", "Deleted", "DeletedBy", "Description", "Name", "Updated", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:38:23.9766|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|ContainerTypes creados exitosamente|duration: 
2025-07-08 12:38:24.0267|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (Size = 44), @p2='?' (DbType = Int32), @p3='?' (DbType = DateTime), @p4='?' (Size = 6), @p5='?' (DbType = DateTime), @p6='?', @p7='?' (DbType = DateTime), @p8='?' (DbType = DateTime), @p9='?' (DbType = Decimal), @p10='?' (DbType = Decimal), @p11='?' (DbType = Decimal), @p12='?' (DbType = Decimal), @p13='?' (DbType = Decimal), @p14='?' (DbType = DateTime), @p15='?', @p16='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
INSERT INTO "BitacoraEntries" ("AreaId", "Description", "ContainerTypeId", "Created", "CreatedBy", "Deleted", "DeletedBy", "DepartureDate", "EntryDate", "GrossWeight", "NetWeightKG", "NetWeightLB", "Tare", "UnitPrice", "Updated", "UpdatedBy", "WasteTypeId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:38:24.0385|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (Size = 53), @p2='?' (DbType = Int32), @p3='?' (DbType = DateTime), @p4='?' (Size = 6), @p5='?' (DbType = DateTime), @p6='?', @p7='?' (DbType = DateTime), @p8='?' (DbType = DateTime), @p9='?' (DbType = Decimal), @p10='?' (DbType = Decimal), @p11='?' (DbType = Decimal), @p12='?' (DbType = Decimal), @p13='?' (DbType = Decimal), @p14='?' (DbType = DateTime), @p15='?', @p16='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
INSERT INTO "BitacoraEntries" ("AreaId", "Description", "ContainerTypeId", "Created", "CreatedBy", "Deleted", "DeletedBy", "DepartureDate", "EntryDate", "GrossWeight", "NetWeightKG", "NetWeightLB", "Tare", "UnitPrice", "Updated", "UpdatedBy", "WasteTypeId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:38:24.0385|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (Size = 56), @p2='?' (DbType = Int32), @p3='?' (DbType = DateTime), @p4='?' (Size = 6), @p5='?' (DbType = DateTime), @p6='?', @p7='?' (DbType = DateTime), @p8='?' (DbType = DateTime), @p9='?' (DbType = Decimal), @p10='?' (DbType = Decimal), @p11='?' (DbType = Decimal), @p12='?' (DbType = Decimal), @p13='?' (DbType = Decimal), @p14='?' (DbType = DateTime), @p15='?', @p16='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
INSERT INTO "BitacoraEntries" ("AreaId", "Description", "ContainerTypeId", "Created", "CreatedBy", "Deleted", "DeletedBy", "DepartureDate", "EntryDate", "GrossWeight", "NetWeightKG", "NetWeightLB", "Tare", "UnitPrice", "Updated", "UpdatedBy", "WasteTypeId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:38:24.0385|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (Size = 46), @p2='?' (DbType = Int32), @p3='?' (DbType = DateTime), @p4='?' (Size = 6), @p5='?' (DbType = DateTime), @p6='?', @p7='?' (DbType = DateTime), @p8='?' (DbType = DateTime), @p9='?' (DbType = Decimal), @p10='?' (DbType = Decimal), @p11='?' (DbType = Decimal), @p12='?' (DbType = Decimal), @p13='?' (DbType = Decimal), @p14='?' (DbType = DateTime), @p15='?', @p16='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
INSERT INTO "BitacoraEntries" ("AreaId", "Description", "ContainerTypeId", "Created", "CreatedBy", "Deleted", "DeletedBy", "DepartureDate", "EntryDate", "GrossWeight", "NetWeightKG", "NetWeightLB", "Tare", "UnitPrice", "Updated", "UpdatedBy", "WasteTypeId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16)
RETURNING "Id", "Active", "IsDeleted";|duration: 
2025-07-08 12:38:24.0385|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|BitacoraEntries de prueba creadas exitosamente|duration: 
2025-07-08 12:38:24.0385|INFO|BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.AppDbContext|Inicialización de base de datos completada exitosamente|duration: 
2025-07-08 12:38:24.0537|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Program|Datos de prueba inicializados correctamente|duration: 
2025-07-08 12:38:24.1331|INFO|BitacoraResiduosESH.Backend.Backend.WebAPI.Program|Aplicación iniciada correctamente|duration: 
2025-07-08 12:38:24.1794|INFO|Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager|User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.|duration: 
2025-07-08 12:38:24.2418|WARN|Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware|The WebRootPath was not found: C:\Repositories\WebProjects\BitacoraResiduosESH\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend.Backend.WebAPI\wwwroot. Static files may be unavailable.|duration: 
2025-07-08 12:38:24.3195|INFO|Microsoft.Hosting.Lifetime|Now listening on: http://localhost:5001|duration: 
2025-07-08 12:38:24.3195|INFO|Microsoft.Hosting.Lifetime|Application started. Press Ctrl+C to shut down.|duration: 
2025-07-08 12:38:24.3195|INFO|Microsoft.Hosting.Lifetime|Hosting environment: Development|duration: 
2025-07-08 12:38:24.3195|INFO|Microsoft.Hosting.Lifetime|Content root path: C:\Repositories\WebProjects\BitacoraResiduosESH\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend.Backend.WebAPI|duration: 
2025-07-08 12:38:24.7344|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar - - -|duration: 2.943
2025-07-08 12:38:24.8061|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 76.275
2025-07-08 12:38:24.8061|INFO|Microsoft.AspNetCore.Http.Result.RedirectResult|Executing RedirectResult, redirecting to scalar/.|duration: 85.248
2025-07-08 12:38:24.8061|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 85.620
2025-07-08 12:38:24.8200|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar - 302 0 - 87.5369ms|duration: 90.462
2025-07-08 12:38:24.8200|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar/ - - -|duration: 0.152
2025-07-08 12:38:24.8200|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 2.445
2025-07-08 12:38:24.8620|INFO|Microsoft.AspNetCore.Http.Result.ContentResult|Write content with HTTP Response ContentType of text/html|duration: 41.467
2025-07-08 12:38:24.8677|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/{documentName?}'|duration: 42.126
2025-07-08 12:38:24.8677|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar/ - 200 924 text/html 42.5288ms|duration: 42.684
2025-07-08 12:38:24.8677|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar/scalar.js - - -|duration: 0.130
2025-07-08 12:38:24.8677|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/scalar/scalar.aspnetcore.js - - -|duration: 0.135
2025-07-08 12:38:24.8677|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/scalar.js'|duration: 2.748
2025-07-08 12:38:24.8827|INFO|Microsoft.AspNetCore.Cors.Infrastructure.CorsService|CORS policy execution successful.|duration: 2.685
2025-07-08 12:38:24.8827|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /scalar/scalar.aspnetcore.js'|duration: 3.212
2025-07-08 12:38:24.8827|INFO|Microsoft.AspNetCore.Http.Result.StatusCodeResult|Setting HTTP status code 304.|duration: 12.661
2025-07-08 12:38:24.8827|INFO|Microsoft.AspNetCore.Http.Result.StatusCodeResult|Setting HTTP status code 304.|duration: 11.637
2025-07-08 12:38:24.8827|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/scalar.aspnetcore.js'|duration: 11.916
2025-07-08 12:38:24.8827|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /scalar/scalar.js'|duration: 13.046
2025-07-08 12:38:24.8827|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar/scalar.js - 304 - - 13.2955ms|duration: 13.399
2025-07-08 12:38:24.8827|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/scalar/scalar.aspnetcore.js - 304 - - 12.9165ms|duration: 13.046
2025-07-08 12:38:25.0650|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/openapi/v1.json - - -|duration: 0.150
2025-07-08 12:38:25.0650|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'HTTP: GET /openapi/{documentName}.json'|duration: 1.664
2025-07-08 12:38:25.3098|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'HTTP: GET /openapi/{documentName}.json'|duration: 245.190
2025-07-08 12:38:25.3098|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/openapi/v1.json - 200 - application/json;charset=utf-8 246.8068ms|duration: 246.933
2025-07-08 12:38:57.8791|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request starting HTTP/1.1 GET http://localhost:5001/api/BitacoraEntry - - -|duration: 2.719
2025-07-08 12:38:57.8828|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executing endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 7.276
2025-07-08 12:38:57.9719|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Route matched with {action = "GetAll", controller = "BitacoraEntry"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[BitacoraResiduosESH.Backend.Backend.Application.Common.PagedResponse`1[BitacoraResiduosESH.Backend.Backend.Application.DTOs.BitacoraEntry.BitacoraEntryDto]]] GetAll(Int32, Int32, Boolean) on controller BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController (BitacoraResiduosESH.Backend.Backend.WebAPI).|duration: 93.618
2025-07-08 12:38:58.1526|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executing action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI) - Validation state: Valid|duration: 274.234
2025-07-08 12:38:58.4290|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (2ms) [Parameters=[@__filter_IncludeDeleted_0='?' (DbType = Boolean)], CommandType='Text', CommandTimeout='30']
SELECT COUNT(*)
FROM "BitacoraEntries" AS "b"
WHERE "b"."IsDeleted" = @__filter_IncludeDeleted_0|duration: 558.731
2025-07-08 12:38:58.7931|INFO|Microsoft.EntityFrameworkCore.Database.Command|Executed DbCommand (3ms) [Parameters=[@__filter_IncludeDeleted_0='?' (DbType = Boolean), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
SELECT "b0"."Id", "b0"."Active", "b0"."AreaId", "b0"."Description", "b0"."ContainerTypeId", "b0"."Created", "b0"."CreatedBy", "b0"."Deleted", "b0"."DeletedBy", "b0"."DepartureDate", "b0"."EntryDate", "b0"."GrossWeight", "b0"."IsDeleted", "b0"."NetWeightKG", "b0"."NetWeightLB", "b0"."Tare", "b0"."UnitPrice", "b0"."Updated", "b0"."UpdatedBy", "b0"."WasteTypeId", "w"."Id", "w"."Active", "w"."Created", "w"."CreatedBy", "w"."Deleted", "w"."DeletedBy", "w"."Description", "w"."IsDeleted", "w"."Name", "w"."Updated", "w"."UpdatedBy", "c"."Id", "c"."Active", "c"."Created", "c"."CreatedBy", "c"."Deleted", "c"."DeletedBy", "c"."Description", "c"."IsDeleted", "c"."Name", "c"."Updated", "c"."UpdatedBy", "a"."Id", "a"."Active", "a"."Created", "a"."CreatedBy", "a"."Deleted", "a"."DeletedBy", "a"."Description", "a"."IsDeleted", "a"."Name", "a"."Updated", "a"."UpdatedBy"
FROM (
    SELECT "b"."Id", "b"."Active", "b"."AreaId", "b"."Description", "b"."ContainerTypeId", "b"."Created", "b"."CreatedBy", "b"."Deleted", "b"."DeletedBy", "b"."DepartureDate", "b"."EntryDate", "b"."GrossWeight", "b"."IsDeleted", "b"."NetWeightKG", "b"."NetWeightLB", "b"."Tare", "b"."UnitPrice", "b"."Updated", "b"."UpdatedBy", "b"."WasteTypeId"
    FROM "BitacoraEntries" AS "b"
    WHERE "b"."IsDeleted" = @__filter_IncludeDeleted_0
    ORDER BY "b"."Id"
    LIMIT @__p_2 OFFSET @__p_1
) AS "b0"
INNER JOIN "WasteTypes" AS "w" ON "b0"."WasteTypeId" = "w"."Id"
INNER JOIN "ContainerTypes" AS "c" ON "b0"."ContainerTypeId" = "c"."Id"
INNER JOIN "Areas" AS "a" ON "b0"."AreaId" = "a"."Id"
ORDER BY "b0"."Id"|duration: 923.785
2025-07-08 12:38:58.8928|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action method BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 723.9632ms.|duration: 1014.927
2025-07-08 12:38:58.9766|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor|Executing OkObjectResult, writing value of type 'BitacoraResiduosESH.Backend.Backend.Application.Common.PagedResponse`1[[BitacoraResiduosESH.Backend.Backend.Application.DTOs.BitacoraEntry.BitacoraEntryDto, BitacoraResiduosESH.Backend.Backend.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]'.|duration: 1102.190
2025-07-08 12:38:59.0475|INFO|Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker|Executed action BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI) in 1056.0208ms|duration: 1169.167
2025-07-08 12:38:59.0475|INFO|Microsoft.AspNetCore.Routing.EndpointMiddleware|Executed endpoint 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.BitacoraEntryController.GetAll (BitacoraResiduosESH.Backend.Backend.WebAPI)'|duration: 1170.049
2025-07-08 12:38:59.0575|INFO|Microsoft.AspNetCore.Hosting.Diagnostics|Request finished HTTP/1.1 GET http://localhost:5001/api/BitacoraEntry - 200 - application/json;+charset=utf-8 1181.6844ms|duration: 1182.037
