namespace BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers;

[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public abstract class GenericController<TEntity, TDto, TFilterDto, TCreateDto, TUpdateDto>(
    IGenericService<TEntity, TDto, TFilterDto, TCreateDto, TUpdateDto> service)
    : ControllerBase
    where TEntity : BaseEntity
    where TDto : BaseEntityDto
    where TFilterDto : BaseEntityFilterDto
    where TCreateDto : CreateBaseEntityDto
    where TUpdateDto : UpdateBaseEntityDto
{
    private static readonly ILogger Logger = LogManager.GetCurrentClassLogger();

    #region Operaciones de validación

    /// <summary>
    ///     Verifica si existe un elemento
    /// </summary>
    /// <param name="id">ID del elemento</param>
    /// <param name="includeDeleted">Incluir elementos eliminados</param>
    /// <returns>True si existe, false en caso contrario</returns>
    [HttpGet("{id:int}/exists")]
    [ProducesResponseType(typeof(bool), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<bool>> Exists(int id, [FromQuery] bool includeDeleted = false)
    {
        Logger.Debug("Verificación de existencia para {EntityType} ID: {Id}, IncludeDeleted: {IncludeDeleted}",
            typeof(TEntity).Name, id, includeDeleted);

        try
        {
            var result = await service.ExistsAsync(id, includeDeleted);
            Logger.Debug("Verificación de existencia completada para {EntityType} ID {Id}: {Exists}",
                typeof(TEntity).Name, id, result);
            return Ok(result);
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al verificar existencia del {EntityType} con ID {Id}", typeof(TEntity).Name, id);
            return BadRequest($"Error al verificar la existencia del {typeof(TEntity).Name}");
        }
    }

    #endregion

    #region Operaciones de búsqueda

    /// <summary>
    ///     Busca elementos usando un predicado personalizado con paginación
    /// </summary>
    /// <param name="predicate">Predicado de búsqueda (se debe implementar en el controlador específico)</param>
    /// <param name="pageNumber">Número de página</param>
    /// <param name="pageSize">Tamaño de página</param>
    /// <param name="includeDeleted">Incluir elementos eliminados</param>
    /// <returns>Elementos que coinciden con el predicado paginados</returns>
    [HttpGet("search")]
    [ProducesResponseType(typeof(PagedResponse<BaseEntityDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public virtual ActionResult<PagedResponse<TDto>> Search(
        [FromQuery] string predicate,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] bool includeDeleted = false)
    {
        Logger.Debug(
            "Búsqueda personalizada paginada para {EntityType} con predicado: {Predicate}, Página: {PageNumber}, Tamaño: {PageSize}, IncludeDeleted: {IncludeDeleted}",
            typeof(TEntity).Name, predicate, pageNumber, pageSize, includeDeleted);

        try
        {
            // Esta es una implementación base que debe ser sobrescrita en controladores específicos
            Logger.Warn("Método de búsqueda personalizada paginada no implementado para {EntityType}",
                typeof(TEntity).Name);
            return BadRequest("Búsqueda personalizada paginada no implementada para este tipo de entidad");
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error en búsqueda personalizada paginada para {EntityType}", typeof(TEntity).Name);
            return BadRequest($"Error en la búsqueda de {typeof(TEntity).Name}");
        }
    }

    #endregion

    #region Operaciones de lectura

    /// <summary>
    ///     Obtiene un elemento por su ID
    /// </summary>
    /// <param name="id">ID del elemento</param>
    /// <param name="includeDeleted">Incluir elementos eliminados</param>
    /// <returns>Elemento encontrado</returns>
    [HttpGet("{id:int}")]
    [ProducesResponseType(typeof(BaseEntityDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<TDto>> GetById(int id, [FromQuery] bool includeDeleted = false)
    {
        Logger.Debug("Solicitud GET para {EntityType} con ID: {Id}, IncludeDeleted: {IncludeDeleted}",
            typeof(TEntity).Name, id, includeDeleted);

        try
        {
            var result = await service.GetByIdAsync(id, includeDeleted);

            if (result == null)
            {
                Logger.Warn("{EntityType} con ID {Id} no encontrado", typeof(TEntity).Name, id);
                return NotFound($"No se encontró el elemento con ID {id}");
            }

            Logger.Debug("{EntityType} con ID {Id} recuperado exitosamente", typeof(TEntity).Name, id);
            return Ok(result);
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al obtener {EntityType} con ID {Id}", typeof(TEntity).Name, id);
            return BadRequest("Error al obtener el elemento");
        }
    }

    /// <summary>
    ///     Obtiene todos los elementos con paginación
    /// </summary>
    /// <param name="pageNumber">Número de página</param>
    /// <param name="pageSize">Tamaño de página</param>
    /// <param name="includeDeleted">Incluir elementos eliminados</param>
    /// <returns>Elementos paginados</returns>
    [HttpGet]
    [ProducesResponseType(typeof(PagedResponse<BaseEntityDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<PagedResponse<TDto>>> GetAll(
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] bool includeDeleted = false)
    {
        Logger.Debug(
            "Solicitud GET para todos los {EntityType} paginados - Página: {PageNumber}, Tamaño: {PageSize}, IncludeDeleted: {IncludeDeleted}",
            typeof(TEntity).Name, pageNumber, pageSize, includeDeleted);

        try
        {
            var filter = new PaginationFilter
            {
                PageNumber = pageNumber,
                PageSize = pageSize,
                IncludeDeleted = includeDeleted
            };

            var result = await service.GetAllAsync(filter);
            Logger.Debug("Recuperados {Count} {EntityType} de {TotalRecords} totales", result.Data.Count,
                typeof(TEntity).Name, result.TotalRecords);
            return Ok(result);
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al obtener todos los {EntityType}", typeof(TEntity).Name);
            return BadRequest($"Error al obtener los {typeof(TEntity).Name}");
        }
    }

    /// <summary>
    ///     Obtiene elementos paginados con filtros
    /// </summary>
    /// <param name="filter">Filtros de búsqueda</param>
    /// <returns>Elementos paginados</returns>
    [HttpGet("paged")]
    [ProducesResponseType(typeof(PagedResponse<BaseEntityDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<PagedResponse<TDto>>> GetPaged([FromQuery] TFilterDto filter)
    {
        Logger.Debug(
            "Solicitud GET paginada para {EntityType} - Página: {PageNumber}, Tamaño: {PageSize}, Filtros: {@Filter}",
            typeof(TEntity).Name, filter.PageNumber, filter.PageSize, filter);

        try
        {
            var result = await service.GetPagedAsync(filter);
            Logger.Debug("Paginación completada para {EntityType} - Total: {TotalRecords}, Página: {PageNumber}",
                typeof(TEntity).Name, result.TotalRecords, result.PageNumber);
            return Ok(result);
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al obtener {EntityType} paginados", typeof(TEntity).Name);
            return BadRequest($"Error al obtener los {typeof(TEntity).Name}");
        }
    }

    /// <summary>
    ///     Obtiene solo elementos activos con paginación
    /// </summary>
    /// <param name="pageNumber">Número de página</param>
    /// <param name="pageSize">Tamaño de página</param>
    /// <returns>Lista de elementos activos paginados</returns>
    [HttpGet("active")]
    [ProducesResponseType(typeof(PagedResponse<BaseEntityDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<PagedResponse<TDto>>> GetActive(
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10)
    {
        Logger.Debug("Solicitud GET para {EntityType} activos paginados - Página: {PageNumber}, Tamaño: {PageSize}",
            typeof(TEntity).Name, pageNumber, pageSize);

        try
        {
            var filter = new PaginationFilter
            {
                PageNumber = pageNumber,
                PageSize = pageSize,
                IncludeDeleted = false
            };

            var result = await service.GetActiveAsync(filter);
            Logger.Debug("Recuperados {Count} {EntityType} activos de {TotalRecords} totales", result.Data.Count,
                typeof(TEntity).Name, result.TotalRecords);
            return Ok(result);
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al obtener {EntityType} activos", typeof(TEntity).Name);
            return BadRequest($"Error al obtener los {typeof(TEntity).Name} activos");
        }
    }

    /// <summary>
    ///     Obtiene el conteo de elementos
    /// </summary>
    /// <param name="includeDeleted">Incluir elementos eliminados</param>
    /// <returns>Número total de elementos</returns>
    [HttpGet("count")]
    [ProducesResponseType(typeof(int), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<int>> GetCount([FromQuery] bool includeDeleted = false)
    {
        Logger.Debug("Solicitud GET para conteo de {EntityType}, IncludeDeleted: {IncludeDeleted}",
            typeof(TEntity).Name, includeDeleted);

        try
        {
            var result = await service.CountAsync(includeDeleted);
            Logger.Debug("Conteo completado para {EntityType}: {Count}", typeof(TEntity).Name, result);
            return Ok(result);
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al contar {EntityType}", typeof(TEntity).Name);
            return BadRequest($"Error al contar los {typeof(TEntity).Name}");
        }
    }

    #endregion

    #region Operaciones de escritura

    /// <summary>
    ///     Agrega un nuevo elemento usando DTO de creación
    /// </summary>
    /// <param name="dto">DTO con datos para crear el elemento</param>
    /// <returns>Elemento agregado</returns>
    [HttpPost]
    [ProducesResponseType(typeof(BaseEntityDto), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<TDto>> Add([FromBody] TCreateDto dto)
    {
        Logger.Debug("Solicitud POST para agregar {EntityType}: {@Dto}", typeof(TEntity).Name, dto);

        try
        {
            if (!ModelState.IsValid)
            {
                Logger.Warn("Modelo inválido en solicitud POST para {EntityType}: {@ModelState}", typeof(TEntity).Name,
                    ModelState);
                return BadRequest(ModelState);
            }

            // En un entorno real, obtendrías el usuario del contexto de autenticación
            var createdBy = User.Identity?.Name ?? "system";

            var result = await service.CreateAsync(dto, createdBy);

            Logger.Info("{EntityType} agregado exitosamente con ID: {Id} por usuario: {User}", typeof(TEntity).Name,
                result.Id, createdBy);
            return CreatedAtAction(nameof(GetById), new { id = result.Id }, result);
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al agregar {EntityType}", typeof(TEntity).Name);
            return BadRequest($"Error al agregar el {typeof(TEntity).Name}");
        }
    }

    /// <summary>
    ///     Agrega múltiples elementos usando DTOs de creación
    /// </summary>
    /// <param name="dtos">Lista de DTOs para crear elementos</param>
    /// <returns>Elementos agregados</returns>
    [HttpPost("batch")]
    [ProducesResponseType(typeof(IEnumerable<BaseEntityDto>), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<IEnumerable<TDto>>> AddRange([FromBody] IEnumerable<TCreateDto> dtos)
    {
        Logger.Debug("Solicitud POST para agregar {Count} {EntityType}", dtos.Count(), typeof(TEntity).Name);

        try
        {
            if (!ModelState.IsValid)
            {
                Logger.Warn("Modelo inválido en solicitud POST batch para {EntityType}: {@ModelState}",
                    typeof(TEntity).Name,
                    ModelState);
                return BadRequest(ModelState);
            }

            // En un entorno real, obtendrías el usuario del contexto de autenticación
            var createdBy = User.Identity?.Name ?? "system";

            var results = new List<TDto>();
            foreach (var dto in dtos)
            {
                var result = await service.CreateAsync(dto, createdBy);
                results.Add(result);
            }

            Logger.Info("{Count} {EntityType} agregados exitosamente por usuario: {User}", results.Count,
                typeof(TEntity).Name, createdBy);
            return CreatedAtAction(nameof(GetAll), results);
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al agregar múltiples {EntityType}", typeof(TEntity).Name);
            return BadRequest($"Error al agregar los {typeof(TEntity).Name}");
        }
    }

    /// <summary>
    ///     Actualiza un elemento existente usando DTO de actualización
    /// </summary>
    /// <param name="dto">DTO con datos actualizados</param>
    /// <returns>Elemento actualizado</returns>
    [HttpPut]
    [ProducesResponseType(typeof(BaseEntityDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<TDto>> Update([FromBody] TUpdateDto dto)
    {
        Logger.Debug("Solicitud PUT para actualizar {EntityType} ID: {Id}", typeof(TEntity).Name, dto.Id);

        try
        {
            if (!ModelState.IsValid)
            {
                Logger.Warn("Modelo inválido en solicitud PUT para {EntityType} ID: {Id}", typeof(TEntity).Name,
                    dto.Id);
                return BadRequest(ModelState);
            }

            // En un entorno real, obtendrías el usuario del contexto de autenticación
            var updatedBy = User.Identity?.Name ?? "system";

            var result = await service.UpdateAsync(dto.Id, dto, updatedBy);

            Logger.Info("{EntityType} con ID {Id} actualizado exitosamente por usuario: {User}", typeof(TEntity).Name,
                dto.Id, updatedBy);
            return Ok(result);
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al actualizar {EntityType} con ID {Id}", typeof(TEntity).Name, dto.Id);
            return BadRequest($"Error al actualizar el {typeof(TEntity).Name}");
        }
    }

    /// <summary>
    ///     Actualiza múltiples elementos usando DTOs de actualización
    /// </summary>
    /// <param name="dtos">Lista de DTOs con datos actualizados</param>
    /// <returns>Elementos actualizados</returns>
    [HttpPut("batch")]
    [ProducesResponseType(typeof(IEnumerable<BaseEntityDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<IEnumerable<TDto>>> UpdateRange([FromBody] IEnumerable<TUpdateDto> dtos)
    {
        Logger.Debug("Solicitud PUT para actualizar {Count} {EntityType}", dtos.Count(), typeof(TEntity).Name);

        try
        {
            if (!ModelState.IsValid)
            {
                Logger.Warn("Modelo inválido en solicitud PUT batch para {EntityType}: {@ModelState}",
                    typeof(TEntity).Name,
                    ModelState);
                return BadRequest(ModelState);
            }

            // En un entorno real, obtendrías el usuario del contexto de autenticación
            var updatedBy = User.Identity?.Name ?? "system";

            var results = new List<TDto>();
            foreach (var dto in dtos)
            {
                var result = await service.UpdateAsync(dto.Id, dto, updatedBy);
                results.Add(result);
            }

            Logger.Info("{Count} {EntityType} actualizados exitosamente por usuario: {User}", results.Count,
                typeof(TEntity).Name, updatedBy);
            return Ok(results);
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al actualizar múltiples {EntityType}", typeof(TEntity).Name);
            return BadRequest($"Error al actualizar los {typeof(TEntity).Name}");
        }
    }

    #endregion

    #region Operaciones de escritura con DTOs específicos

    /// <summary>
    ///     Crea un nuevo elemento usando DTO específico
    /// </summary>
    /// <param name="dto">DTO con datos para crear el elemento</param>
    /// <returns>Elemento creado</returns>
    [HttpPost("create")]
    [ProducesResponseType(typeof(BaseEntityDto), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status409Conflict)]
    public async Task<ActionResult<TDto>> Create([FromBody] TCreateDto dto)
    {
        Logger.Debug("Solicitud POST para crear {EntityType} con DTO específico: {@Dto}", typeof(TEntity).Name, dto);

        try
        {
            if (!ModelState.IsValid)
            {
                Logger.Warn("Modelo inválido en solicitud POST create para {EntityType}: {@ModelState}",
                    typeof(TEntity).Name, ModelState);
                return BadRequest(ModelState);
            }

            // En un entorno real, obtendrías el usuario del contexto de autenticación
            var createdBy = User.Identity?.Name ?? "system";

            var result = await service.CreateAsync(dto, createdBy);

            Logger.Info("{EntityType} creado exitosamente con ID: {Id} por usuario: {User}", typeof(TEntity).Name,
                result.Id, createdBy);
            return CreatedAtAction(nameof(GetById), new { id = result.Id }, result);
        }
        catch (InvalidOperationException ex)
        {
            Logger.Warn("Error de validación al crear {EntityType}: {Message}", typeof(TEntity).Name, ex.Message);
            return Conflict(ex.Message);
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al crear {EntityType}", typeof(TEntity).Name);
            return BadRequest($"Error al crear el {typeof(TEntity).Name}");
        }
    }

    /// <summary>
    ///     Actualiza un elemento existente usando DTO específico
    /// </summary>
    /// <param name="id">ID del elemento a actualizar</param>
    /// <param name="dto">DTO con datos actualizados</param>
    /// <returns>Elemento actualizado</returns>
    [HttpPut("update/{id:int}")]
    [ProducesResponseType(typeof(BaseEntityDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status409Conflict)]
    public async Task<ActionResult<TDto>> Update(int id, [FromBody] TUpdateDto dto)
    {
        Logger.Debug("Solicitud PUT para actualizar {EntityType} ID: {Id} con DTO específico: {@Dto}",
            typeof(TEntity).Name, id, dto);

        try
        {
            if (!ModelState.IsValid)
            {
                Logger.Warn("Modelo inválido en solicitud PUT update para {EntityType} ID {Id}: {@ModelState}",
                    typeof(TEntity).Name, id, ModelState);
                return BadRequest(ModelState);
            }

            if (id != dto.Id)
            {
                Logger.Warn("Inconsistencia en ID de {EntityType}: {UrlId} vs {DtoId}", typeof(TEntity).Name, id,
                    dto.Id);
                return BadRequest("El ID en la URL no coincide con el ID en el cuerpo de la petición");
            }

            // En un entorno real, obtendrías el usuario del contexto de autenticación
            var updatedBy = User.Identity?.Name ?? "system";

            var result = await service.UpdateAsync(id, dto, updatedBy);

            Logger.Info("{EntityType} actualizado exitosamente con ID: {Id} por usuario: {User}", typeof(TEntity).Name,
                result.Id, updatedBy);
            return Ok(result);
        }
        catch (InvalidOperationException ex)
        {
            Logger.Warn("Error de validación al actualizar {EntityType} ID {Id}: {Message}", typeof(TEntity).Name, id,
                ex.Message);
            return Conflict(ex.Message);
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al actualizar {EntityType} ID {Id}", typeof(TEntity).Name, id);
            return BadRequest($"Error al actualizar el {typeof(TEntity).Name}");
        }
    }

    #endregion

    #region Operaciones de eliminación

    /// <summary>
    ///     Elimina un elemento (soft delete)
    /// </summary>
    /// <param name="id">ID del elemento</param>
    /// <returns>Resultado de la operación</returns>
    [HttpDelete("{id:int}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult> Delete(int id)
    {
        Logger.Debug("Solicitud DELETE para {EntityType} ID: {Id}", typeof(TEntity).Name, id);

        try
        {
            // En un entorno real, obtendrías el usuario del contexto de autenticación
            var deletedBy = User.Identity?.Name ?? "system";

            var result = await service.DeleteAsync(id, deletedBy);

            if (!result)
            {
                Logger.Warn("{EntityType} con ID {Id} no encontrado para eliminar", typeof(TEntity).Name, id);
                return NotFound($"No se encontró el elemento con ID {id}");
            }

            Logger.Info("{EntityType} con ID {Id} eliminado exitosamente por usuario: {User}", typeof(TEntity).Name, id,
                deletedBy);
            return Ok(new { message = "Elemento eliminado correctamente" });
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al eliminar {EntityType} con ID {Id}", typeof(TEntity).Name, id);
            return BadRequest($"Error al eliminar el {typeof(TEntity).Name}");
        }
    }

    /// <summary>
    ///     Elimina múltiples elementos (soft delete)
    /// </summary>
    /// <param name="ids">Lista de IDs a eliminar</param>
    /// <returns>Resultado de la operación</returns>
    [HttpDelete("batch")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult> DeleteRange([FromBody] IEnumerable<int> ids)
    {
        Logger.Debug("Solicitud DELETE batch para {Count} {EntityType}", ids.Count(), typeof(TEntity).Name);

        try
        {
            // En un entorno real, obtendrías el usuario del contexto de autenticación
            var deletedBy = User.Identity?.Name ?? "system";

            var result = await service.DeleteRangeAsync(ids, deletedBy);

            if (!result)
            {
                Logger.Warn("Error al eliminar {Count} {EntityType}", ids.Count(), typeof(TEntity).Name);
                return BadRequest("Error al eliminar los elementos");
            }

            Logger.Info("{Count} {EntityType} eliminados exitosamente por usuario: {User}", ids.Count(),
                typeof(TEntity).Name, deletedBy);
            return Ok(new { message = "Elementos eliminados correctamente" });
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al eliminar múltiples {EntityType}", typeof(TEntity).Name);
            return BadRequest($"Error al eliminar los {typeof(TEntity).Name}");
        }
    }

    /// <summary>
    ///     Elimina un elemento permanentemente (hard delete)
    /// </summary>
    /// <param name="id">ID del elemento</param>
    /// <returns>Resultado de la operación</returns>
    [HttpDelete("{id:int}/permanent")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult> HardDelete(int id)
    {
        Logger.Debug("Solicitud DELETE permanente para {EntityType} ID: {Id}", typeof(TEntity).Name, id);

        try
        {
            var result = await service.HardDeleteAsync(id);

            if (!result)
            {
                Logger.Warn("{EntityType} con ID {Id} no encontrado para eliminación permanente", typeof(TEntity).Name,
                    id);
                return NotFound($"No se encontró el elemento con ID {id}");
            }

            Logger.Info("{EntityType} con ID {Id} eliminado permanentemente", typeof(TEntity).Name, id);
            return Ok(new { message = "Elemento eliminado permanentemente" });
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al eliminar permanentemente {EntityType} con ID {Id}", typeof(TEntity).Name, id);
            return BadRequest($"Error al eliminar el {typeof(TEntity).Name}");
        }
    }

    /// <summary>
    ///     Elimina múltiples elementos permanentemente (hard delete)
    /// </summary>
    /// <param name="ids">Lista de IDs a eliminar permanentemente</param>
    /// <returns>Resultado de la operación</returns>
    [HttpDelete("batch/permanent")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult> HardDeleteRange([FromBody] IEnumerable<int> ids)
    {
        Logger.Debug("Solicitud DELETE permanente batch para {Count} {EntityType}", ids.Count(), typeof(TEntity).Name);

        try
        {
            var result = await service.HardDeleteRangeAsync(ids);

            if (!result)
            {
                Logger.Warn("Error al eliminar permanentemente {Count} {EntityType}", ids.Count(),
                    typeof(TEntity).Name);
                return BadRequest("Error al eliminar permanentemente los elementos");
            }

            Logger.Info("{Count} {EntityType} eliminados permanentemente", ids.Count(), typeof(TEntity).Name);
            return Ok(new { message = "Elementos eliminados permanentemente" });
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al eliminar permanentemente múltiples {EntityType}", typeof(TEntity).Name);
            return BadRequest($"Error al eliminar permanentemente los {typeof(TEntity).Name}");
        }
    }

    #endregion

    #region Operaciones de activación/desactivación

    /// <summary>
    ///     Activa un elemento
    /// </summary>
    /// <param name="id">ID del elemento</param>
    /// <returns>Resultado de la operación</returns>
    [HttpPatch("{id:int}/activate")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult> Activate(int id)
    {
        Logger.Debug("Solicitud PATCH para activar {EntityType} ID: {Id}", typeof(TEntity).Name, id);

        try
        {
            // En un entorno real, obtendrías el usuario del contexto de autenticación
            var updatedBy = User.Identity?.Name ?? "system";

            var result = await service.ActivateAsync(id, updatedBy);

            if (!result)
            {
                Logger.Warn("{EntityType} con ID {Id} no encontrado para activar", typeof(TEntity).Name, id);
                return NotFound($"No se encontró el elemento con ID {id}");
            }

            Logger.Info("{EntityType} con ID {Id} activado exitosamente por usuario: {User}", typeof(TEntity).Name, id,
                updatedBy);
            return Ok(new { message = "Elemento activado correctamente" });
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al activar {EntityType} con ID {Id}", typeof(TEntity).Name, id);
            return BadRequest($"Error al activar el {typeof(TEntity).Name}");
        }
    }

    /// <summary>
    ///     Desactiva un elemento
    /// </summary>
    /// <param name="id">ID del elemento</param>
    /// <returns>Resultado de la operación</returns>
    [HttpPatch("{id:int}/deactivate")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult> Deactivate(int id)
    {
        Logger.Debug("Solicitud PATCH para desactivar {EntityType} ID: {Id}", typeof(TEntity).Name, id);

        try
        {
            // En un entorno real, obtendrías el usuario del contexto de autenticación
            var updatedBy = User.Identity?.Name ?? "system";

            var result = await service.DeactivateAsync(id, updatedBy);

            if (!result)
            {
                Logger.Warn("{EntityType} con ID {Id} no encontrado para desactivar", typeof(TEntity).Name, id);
                return NotFound($"No se encontró el elemento con ID {id}");
            }

            Logger.Info("{EntityType} con ID {Id} desactivado exitosamente por usuario: {User}", typeof(TEntity).Name,
                id, updatedBy);
            return Ok(new { message = "Elemento desactivado correctamente" });
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al desactivar {EntityType} con ID {Id}", typeof(TEntity).Name, id);
            return BadRequest($"Error al desactivar el {typeof(TEntity).Name}");
        }
    }

    #endregion
}