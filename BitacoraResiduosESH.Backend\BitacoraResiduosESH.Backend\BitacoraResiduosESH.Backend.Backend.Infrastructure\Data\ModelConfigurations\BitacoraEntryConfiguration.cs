﻿namespace BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.ModelConfigurations;

public class BitacoraEntryConfiguration: BaseEntityConfiguration<BitacoraEntry>
{
    protected override string TableName => "BitacoraEntries";

    public override void Configure(EntityTypeBuilder<BitacoraEntry> builder)
    {
        // Apply base configuration
        base.Configure(builder);

        // Configure Name and Description properties (since BaseEntity doesn't have them)
        builder.Property(e => e.Name)
            .HasColumnName("Name")
            .HasColumnType("nvarchar(200)")
            .IsRequired();

        builder.Property(e => e.Description)
            .HasColumnName("Description")
            .HasColumnType("nvarchar(1000)")
            .IsRequired(false);

        // Configure weight properties with precision
        builder.Property(e => e.GrossWeight)
            .HasColumnName("GrossWeight")
            .HasColumnType("decimal(18,4)")
            .IsRequired();

        builder.Property(e => e.Tare)
            .HasColumnName("Tare")
            .HasColumnType("decimal(18,4)")
            .IsRequired();

        builder.Property(e => e.NetWeightLB)
            .HasColumnName("NetWeightLB")
            .HasColumnType("decimal(18,4)")
            .IsRequired();

        builder.Property(e => e.NetWeightKG)
            .HasColumnName("NetWeightKG")
            .HasColumnType("decimal(18,4)")
            .IsRequired();

        // Configure unit price
        builder.Property(e => e.UnitPrice)
            .HasColumnName("UnitPrice")
            .HasColumnType("decimal(18,4)")
            .IsRequired();

        // Configure dates
        builder.Property(e => e.EntryDate)
            .HasColumnName("EntryDate")
            .HasColumnType("datetime2(3)")
            .IsRequired();

        builder.Property(e => e.DepartureDate)
            .HasColumnName("DepartureDate")
            .HasColumnType("datetime2(3)")
            .IsRequired(false);

        // Configure EnteredBy
        builder.Property(e => e.EnteredBy)
            .HasColumnName("EnteredBy")
            .HasColumnType("nvarchar(100)")
            .IsRequired();

        // Configure foreign key relationships
        builder.HasOne(e => e.WasteType)
            .WithMany()
            .HasForeignKey(e => e.WasteTypeId)
            .OnDelete(DeleteBehavior.Restrict)
            .HasConstraintName("FK_BitacoraEntries_WasteTypes");

        builder.HasOne(e => e.ContainerType)
            .WithMany()
            .HasForeignKey(e => e.ContainerTypeId)
            .OnDelete(DeleteBehavior.Restrict)
            .HasConstraintName("FK_BitacoraEntries_ContainerTypes");

        builder.HasOne(e => e.Area)
            .WithMany()
            .HasForeignKey(e => e.AreaId)
            .OnDelete(DeleteBehavior.Restrict)
            .HasConstraintName("FK_BitacoraEntries_Areas");

        // Configure indexes for better performance
        builder.HasIndex(e => e.WasteTypeId)
            .HasDatabaseName("IX_BitacoraEntries_WasteTypeId");

        builder.HasIndex(e => e.ContainerTypeId)
            .HasDatabaseName("IX_BitacoraEntries_ContainerTypeId");

        builder.HasIndex(e => e.AreaId)
            .HasDatabaseName("IX_BitacoraEntries_AreaId");

        builder.HasIndex(e => e.EntryDate)
            .HasDatabaseName("IX_BitacoraEntries_EntryDate");

        builder.HasIndex(e => e.DepartureDate)
            .HasDatabaseName("IX_BitacoraEntries_DepartureDate");

        builder.HasIndex(e => e.EnteredBy)
            .HasDatabaseName("IX_BitacoraEntries_EnteredBy");
    }
}