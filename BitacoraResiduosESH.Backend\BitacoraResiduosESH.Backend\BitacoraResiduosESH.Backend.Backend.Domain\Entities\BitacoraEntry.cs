namespace BitacoraResiduosESH.Backend.Backend.Domain.Entities;

public class BitacoraEntry : SimpleEntity
{
    // Waste Type relationship
    public int WasteTypeId { get; set; }
    public virtual WasteType WasteType { get; set; } = null!;

    // Weight properties
    public decimal GrossWeight { get; set; }
    public decimal Tare { get; set; }
    public decimal NetWeightLB { get; set; }
    public decimal NetWeightKG { get; set; }

    // Price
    public decimal UnitPrice { get; set; }

    // Container Type relationship
    public int ContainerTypeId { get; set; }
    public virtual ContainerType ContainerType { get; set; } = null!;

    // Area relationship
    public int AreaId { get; set; }
    public virtual Area Area { get; set; } = null!;

    // Dates
    public DateTime EntryDate { get; set; }
    public DateTime? DepartureDate { get; set; }

    // User who entered the record
    public string EnteredBy { get; set; } = string.Empty;
}
