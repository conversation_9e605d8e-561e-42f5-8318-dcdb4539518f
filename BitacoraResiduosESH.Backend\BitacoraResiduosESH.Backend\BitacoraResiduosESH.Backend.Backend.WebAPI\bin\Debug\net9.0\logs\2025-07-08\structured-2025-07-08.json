{ 'timestamp': '2025-07-08 08:28:41.3824', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Iniciando aplicación...', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 08:28:43.5299', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Base de datos inicializada correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 08:28:43.7902', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Datos de prueba inicializados correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 08:28:43.8909', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Aplicación iniciada correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 08:28:57.1634', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AuthController', 'message': 'Intento de login local - Email: NULL, EmployeeNumber: "123456"', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/auth/login', 'Method': 'POST', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:28:59.6021', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AuthController', 'message': 'Login exitoso para usuario: "<EMAIL>"/"123456"', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/auth/login', 'Method': 'POST', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:29:22.1727', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Token JWT validado correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/area', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:29:23.1959', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Token JWT validado correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/area', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:29:25.2172', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Token JWT validado correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/area', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:29:29.2421', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Token JWT validado correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/area', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:29:33.3213', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Token JWT validado correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/area', 'Method': 'POST', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:29:34.3554', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Token JWT validado correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/area', 'Method': 'POST', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:29:36.3705', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Token JWT validado correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/area', 'Method': 'POST', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:30:24.5386', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Token JWT validado correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/areas', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:30:26.3644', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Token JWT validado correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/areas', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:30:28.0559', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Token JWT validado correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/areas', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:30:29.1744', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Token JWT validado correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/areas', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:30:30.0544', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Token JWT validado correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/areas', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:30:31.1270', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Token JWT validado correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/areas', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:30:37.8232', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Token JWT validado correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/areas', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:30:41.8378', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Token JWT validado correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/areas', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:31:59.7122', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Iniciando aplicación...', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 08:32:00.7136', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Base de datos inicializada correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 08:32:00.9609', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Datos de prueba inicializados correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 08:32:01.0460', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Aplicación iniciada correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 08:32:05.7928', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Token JWT validado correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/areas', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:32:05.9138', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5', 'message': 'Solicitud GET para todos los "Area" paginados - Página: 1, Tamaño: 10, IncludeDeleted: false', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/areas', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:32:06.0080', 'level': 'Error', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5', 'message': 'Error al obtener todos los "Area"', 'exception': 'Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such table: Areas'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at BitacoraResiduosESH.Backend.Backend.Infrastructure.Repositories.GenericRepository`1.GetAllAsync(PaginationFilter filter) in C:\Repositories\WebProjects\BitacoraResiduosESH\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend.Backend.Infrastructure\Repositories\GenericRepository.cs:line 41
   at BitacoraResiduosESH.Backend.Backend.Application.Services.SimpleEntityService`5.GetAllAsync(PaginationFilter filter) in C:\Repositories\WebProjects\BitacoraResiduosESH\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend.Backend.Application\Services\SimpleEntityService.cs:line 65
   at BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5.GetAll(Int32 pageNumber, Int32 pageSize, Boolean includeDeleted) in C:\Repositories\WebProjects\BitacoraResiduosESH\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend.Backend.WebAPI\Controllers\SimpleEntityController.cs:line 114', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/areas', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:32:07.1717', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Token JWT validado correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/areas', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:32:07.1945', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5', 'message': 'Solicitud GET para todos los "Area" paginados - Página: 1, Tamaño: 10, IncludeDeleted: false', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/areas', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:32:07.2356', 'level': 'Error', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5', 'message': 'Error al obtener todos los "Area"', 'exception': 'Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such table: Areas'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at BitacoraResiduosESH.Backend.Backend.Infrastructure.Repositories.GenericRepository`1.GetAllAsync(PaginationFilter filter) in C:\Repositories\WebProjects\BitacoraResiduosESH\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend.Backend.Infrastructure\Repositories\GenericRepository.cs:line 41
   at BitacoraResiduosESH.Backend.Backend.Application.Services.SimpleEntityService`5.GetAllAsync(PaginationFilter filter) in C:\Repositories\WebProjects\BitacoraResiduosESH\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend.Backend.Application\Services\SimpleEntityService.cs:line 65
   at BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5.GetAll(Int32 pageNumber, Int32 pageSize, Boolean includeDeleted) in C:\Repositories\WebProjects\BitacoraResiduosESH\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend.Backend.WebAPI\Controllers\SimpleEntityController.cs:line 114', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/areas', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:32:09.2656', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Token JWT validado correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/areas', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:32:09.2885', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5', 'message': 'Solicitud GET para todos los "Area" paginados - Página: 1, Tamaño: 10, IncludeDeleted: false', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/areas', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:32:09.3197', 'level': 'Error', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5', 'message': 'Error al obtener todos los "Area"', 'exception': 'Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such table: Areas'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at BitacoraResiduosESH.Backend.Backend.Infrastructure.Repositories.GenericRepository`1.GetAllAsync(PaginationFilter filter) in C:\Repositories\WebProjects\BitacoraResiduosESH\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend.Backend.Infrastructure\Repositories\GenericRepository.cs:line 41
   at BitacoraResiduosESH.Backend.Backend.Application.Services.SimpleEntityService`5.GetAllAsync(PaginationFilter filter) in C:\Repositories\WebProjects\BitacoraResiduosESH\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend.Backend.Application\Services\SimpleEntityService.cs:line 65
   at BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5.GetAll(Int32 pageNumber, Int32 pageSize, Boolean includeDeleted) in C:\Repositories\WebProjects\BitacoraResiduosESH\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend.Backend.WebAPI\Controllers\SimpleEntityController.cs:line 114', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/areas', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:32:26.8679', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Iniciando aplicación...', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 08:32:27.8988', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Base de datos inicializada correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 08:32:28.6176', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Datos de prueba inicializados correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 08:32:28.7126', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Aplicación iniciada correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 08:32:31.6517', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Token JWT validado correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/areas', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:32:31.7424', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5', 'message': 'Solicitud GET para todos los "Area" paginados - Página: 1, Tamaño: 10, IncludeDeleted: false', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/areas', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:32:31.8510', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5', 'message': 'Recuperados 0 "Area" de 0 totales', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/areas', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:32:40.2107', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Token JWT validado correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/areas', 'Method': 'POST', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:32:40.3058', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5', 'message': 'Solicitud POST para crear "Area": {"Name":"Test", "Description":"test"}', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/areas', 'Method': 'POST', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:32:40.6813', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5', 'message': '"Area" creado exitosamente con ID: 1 por usuario: "Maximiliano Ponce"', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/areas', 'Method': 'POST', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:32:40.7726', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Token JWT validado correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/areas', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:32:40.8045', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5', 'message': 'Solicitud GET para todos los "Area" paginados - Página: 1, Tamaño: 10, IncludeDeleted: false', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/areas', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:32:40.8389', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5', 'message': 'Recuperados 1 "Area" de 1 totales', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/areas', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:32:42.4137', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Token JWT validado correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/areas/1/deactivate', 'Method': 'PATCH', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:32:42.4461', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5', 'message': 'Solicitud PATCH para desactivar "Area" ID: 1', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/areas/1/deactivate', 'Method': 'PATCH', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:32:42.5713', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5', 'message': '"Area" con ID 1 desactivado exitosamente por usuario: "Maximiliano Ponce"', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/areas/1/deactivate', 'Method': 'PATCH', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:32:42.5871', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Token JWT validado correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/areas', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:32:42.6019', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5', 'message': 'Solicitud GET para todos los "Area" paginados - Página: 1, Tamaño: 10, IncludeDeleted: false', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/areas', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:32:42.6326', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5', 'message': 'Recuperados 1 "Area" de 1 totales', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/areas', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:32:43.0691', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Token JWT validado correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/areas/1/activate', 'Method': 'PATCH', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:32:43.0851', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5', 'message': 'Solicitud PATCH para activar "Area" ID: 1', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/areas/1/activate', 'Method': 'PATCH', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:32:43.1326', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5', 'message': '"Area" con ID 1 activado exitosamente por usuario: "Maximiliano Ponce"', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/areas/1/activate', 'Method': 'PATCH', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:32:43.1326', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Token JWT validado correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/areas', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:32:43.1637', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5', 'message': 'Solicitud GET para todos los "Area" paginados - Página: 1, Tamaño: 10, IncludeDeleted: false', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/areas', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:32:43.1807', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5', 'message': 'Recuperados 1 "Area" de 1 totales', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/areas', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:40:02.2721', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Token JWT validado correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/areas', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:40:02.2891', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5', 'message': 'Solicitud GET para todos los "Area" paginados - Página: 1, Tamaño: 10, IncludeDeleted: false', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/areas', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:40:02.3198', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5', 'message': 'Recuperados 1 "Area" de 1 totales', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/areas', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:40:18.0558', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Token JWT validado correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/areas', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:40:18.0691', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5', 'message': 'Solicitud GET para todos los "Area" paginados - Página: 1, Tamaño: 10, IncludeDeleted: false', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/areas', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:40:18.1005', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5', 'message': 'Recuperados 1 "Area" de 1 totales', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/areas', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:40:24.0854', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Token JWT validado correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/areas', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:40:24.1007', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5', 'message': 'Solicitud GET para todos los "Area" paginados - Página: 1, Tamaño: 10, IncludeDeleted: false', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/areas', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:40:24.1167', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5', 'message': 'Recuperados 1 "Area" de 1 totales', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/areas', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:40:36.0229', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Token JWT validado correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/areas', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:40:36.0387', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5', 'message': 'Solicitud GET para todos los "Area" paginados - Página: 1, Tamaño: 10, IncludeDeleted: false', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/areas', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:40:36.0694', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5', 'message': 'Recuperados 1 "Area" de 1 totales', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/areas', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:53:43.4601', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AuthController', 'message': 'Intento de login local - Email: NULL, EmployeeNumber: "123456"', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/auth/login', 'Method': 'POST', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Trae/1.100.3 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 08:53:43.7804', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AuthController', 'message': 'Login exitoso para usuario: "<EMAIL>"/"123456"', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/auth/login', 'Method': 'POST', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Trae/1.100.3 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 10:08:24.6327', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Iniciando aplicación...', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 10:08:27.6640', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Base de datos inicializada correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 10:08:28.4455', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Datos de prueba inicializados correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 10:08:28.5543', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Aplicación iniciada correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 10:08:44.3221', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AuthController', 'message': 'Intento de login local - Email: NULL, EmployeeNumber: "123456"', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/auth/login', 'Method': 'POST', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Trae/1.100.3 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 10:08:46.1667', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.AuthController', 'message': 'Login exitoso para usuario: "<EMAIL>"/"123456"', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/auth/login', 'Method': 'POST', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Trae/1.100.3 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 10:08:54.2693', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Token JWT validado correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/role', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Trae/1.100.3 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 10:08:54.4006', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5', 'message': 'Solicitud GET para todos los "Role" paginados - Página: 1, Tamaño: 10, IncludeDeleted: false', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/role', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Trae/1.100.3 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 10:08:54.6024', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5', 'message': 'Recuperados 1 "Role" de 1 totales', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/role', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Trae/1.100.3 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 10:08:57.0072', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Token JWT validado correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/role/1/deactivate', 'Method': 'PATCH', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Trae/1.100.3 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 10:08:57.0550', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5', 'message': 'Solicitud PATCH para desactivar "Role" ID: 1', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/role/1/deactivate', 'Method': 'PATCH', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Trae/1.100.3 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 10:08:57.1634', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5', 'message': '"Role" con ID 1 desactivado exitosamente por usuario: "Maximiliano Ponce"', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/role/1/deactivate', 'Method': 'PATCH', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Trae/1.100.3 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 10:08:57.1789', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Token JWT validado correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/role', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Trae/1.100.3 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 10:08:57.2099', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5', 'message': 'Solicitud GET para todos los "Role" paginados - Página: 1, Tamaño: 10, IncludeDeleted: false', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/role', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Trae/1.100.3 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 10:08:57.2412', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5', 'message': 'Recuperados 1 "Role" de 1 totales', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/role', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Trae/1.100.3 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 10:08:58.0074', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Token JWT validado correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/role/1/activate', 'Method': 'PATCH', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Trae/1.100.3 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 10:08:58.0242', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5', 'message': 'Solicitud PATCH para activar "Role" ID: 1', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/role/1/activate', 'Method': 'PATCH', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Trae/1.100.3 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 10:08:58.0710', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5', 'message': '"Role" con ID 1 activado exitosamente por usuario: "Maximiliano Ponce"', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/role/1/activate', 'Method': 'PATCH', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Trae/1.100.3 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 10:08:58.0860', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Token JWT validado correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/role', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Trae/1.100.3 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 10:08:58.1161', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5', 'message': 'Solicitud GET para todos los "Role" paginados - Página: 1, Tamaño: 10, IncludeDeleted: false', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/role', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Trae/1.100.3 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 10:08:58.1316', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5', 'message': 'Recuperados 1 "Role" de 1 totales', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/role', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Trae/1.100.3 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 10:39:15.5859', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Iniciando aplicación...', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 10:39:18.0698', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Base de datos inicializada correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 10:39:18.9462', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Datos de prueba inicializados correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 10:39:19.0341', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Aplicación iniciada correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 11:16:00.8985', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Iniciando aplicación...', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 11:16:03.1196', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Base de datos inicializada correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 11:16:04.0383', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Datos de prueba inicializados correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 11:16:04.1393', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Aplicación iniciada correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 11:17:32.0393', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Iniciando aplicación...', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 11:17:33.0247', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Base de datos inicializada correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 11:17:33.2901', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Datos de prueba inicializados correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 11:17:33.3883', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Aplicación iniciada correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 11:17:44.2741', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5', 'message': 'Solicitud GET para todos los "BitacoraEntry" paginados - Página: 1, Tamaño: 10, IncludeDeleted: false', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/BitacoraEntry', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 11:17:44.4481', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5', 'message': 'Recuperados 0 "BitacoraEntry" de 0 totales', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/BitacoraEntry', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 11:21:11.1364', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Iniciando aplicación...', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 11:21:13.0699', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Base de datos inicializada correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 11:21:13.3354', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Datos de prueba inicializados correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 11:21:13.4378', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Aplicación iniciada correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 11:22:26.9796', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5', 'message': 'Solicitud POST para crear "Area": {"Name":"test"}', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/Areas', 'Method': 'POST', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 11:22:27.2916', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5', 'message': '"Area" creado exitosamente con ID: 1 por usuario: "system"', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/Areas', 'Method': 'POST', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 11:22:44.0388', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5', 'message': 'Solicitud POST para crear "ContainerType": {"Name":"test"}', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/ContainerTypes', 'Method': 'POST', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 11:22:44.0866', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5', 'message': '"ContainerType" creado exitosamente con ID: 1 por usuario: "system"', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/ContainerTypes', 'Method': 'POST', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 11:22:57.6013', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5', 'message': 'Solicitud POST para crear "WasteType": {"Name":"test"}', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/WasteType', 'Method': 'POST', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 11:22:57.6494', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5', 'message': '"WasteType" creado exitosamente con ID: 1 por usuario: "system"', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/WasteType', 'Method': 'POST', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 11:42:50.0225', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Iniciando aplicación...', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 11:42:51.9160', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Base de datos inicializada correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 11:42:52.1174', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Datos de prueba inicializados correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 11:42:52.2136', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Aplicación iniciada correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 12:00:01.1319', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Iniciando aplicación...', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 12:00:01.9970', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Base de datos inicializada correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 12:00:02.2256', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Datos de prueba inicializados correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 12:00:02.3201', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Aplicación iniciada correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 12:10:29.2113', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Iniciando aplicación...', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 12:10:32.9771', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Base de datos inicializada correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 12:10:33.9310', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Datos de prueba inicializados correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 12:10:34.3093', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Aplicación iniciada correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 12:10:54.6944', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.GenericController`5', 'message': 'Solicitud GET para todos los "BitacoraEntry" paginados - Página: 1, Tamaño: 10, IncludeDeleted: false', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/BitacoraEntry', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 12:10:55.4665', 'level': 'Error', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.GenericController`5', 'message': 'Error al obtener todos los "BitacoraEntry"', 'exception': 'Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such table: WasteTypes'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at BitacoraResiduosESH.Backend.Backend.Infrastructure.Repositories.BitacoraEntryRepository.GetAllAsync(PaginationFilter filter) in C:\Repositories\WebProjects\BitacoraResiduosESH\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend.Backend.Infrastructure\Repositories\BitacoraEntryRepository.cs:line 37
   at BitacoraResiduosESH.Backend.Backend.Application.Services.GenericService`5.GetAllAsync(PaginationFilter filter) in C:\Repositories\WebProjects\BitacoraResiduosESH\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend.Backend.Application\Services\GenericService.cs:line 50
   at BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.GenericController`5.GetAll(Int32 pageNumber, Int32 pageSize, Boolean includeDeleted) in C:\Repositories\WebProjects\BitacoraResiduosESH\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend\BitacoraResiduosESH.Backend.Backend.WebAPI\Controllers\GenericController.cs:line 146', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/BitacoraEntry', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 12:11:10.2273', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5', 'message': 'Solicitud GET para todos los "Area" paginados - Página: 1, Tamaño: 10, IncludeDeleted: false', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/Areas', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 12:11:10.6127', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5', 'message': 'Recuperados 1 "Area" de 1 totales', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/Areas', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 12:11:32.8513', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Iniciando aplicación...', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 12:11:33.9132', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Base de datos inicializada correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 12:11:34.7424', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Datos de prueba inicializados correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 12:11:34.8609', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Aplicación iniciada correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 12:11:45.7271', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5', 'message': 'Solicitud GET para todos los "Area" paginados - Página: 1, Tamaño: 10, IncludeDeleted: false', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/Areas', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 12:11:45.9294', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.SimpleEntityController`5', 'message': 'Recuperados 3 "Area" de 3 totales', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/Areas', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 12:11:58.9601', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.GenericController`5', 'message': 'Solicitud GET para todos los "BitacoraEntry" paginados - Página: 1, Tamaño: 10, IncludeDeleted: false', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/BitacoraEntry', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 12:11:59.0540', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.GenericController`5', 'message': 'Recuperados 4 "BitacoraEntry" de 4 totales', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/BitacoraEntry', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 12:25:29.8833', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Iniciando aplicación...', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 12:25:32.0378', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Base de datos inicializada correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 12:25:32.9754', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Datos de prueba inicializados correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 12:25:33.0577', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Aplicación iniciada correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 12:26:31.6017', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.GenericController`5', 'message': 'Solicitud GET para todos los "BitacoraEntry" paginados - Página: 1, Tamaño: 10, IncludeDeleted: false', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/BitacoraEntry', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 12:26:32.3283', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.GenericController`5', 'message': 'Recuperados 4 "BitacoraEntry" de 4 totales', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/BitacoraEntry', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 12:28:24.9203', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.GenericController`5', 'message': 'Solicitud POST para agregar "BitacoraEntry": {"Name":"asdasd", "WasteTypeId":1, "GrossWeight":1.0, "Tare":1.0, "NetWeightLB":1.0, "NetWeightKG":1.0, "UnitPrice":1.0, "ContainerTypeId":1, "AreaId":1, "Active":true}', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/BitacoraEntry', 'Method': 'POST', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 12:28:25.0615', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.GenericController`5', 'message': '"BitacoraEntry" agregado exitosamente con ID: 5 por usuario: "system"', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/BitacoraEntry', 'Method': 'POST', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 12:33:22.5847', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Iniciando aplicación...', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 12:33:25.4755', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Base de datos inicializada correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 12:33:26.3977', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Datos de prueba inicializados correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 12:33:26.4976', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Aplicación iniciada correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 12:33:53.2893', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.GenericController`5', 'message': 'Solicitud GET para todos los "BitacoraEntry" paginados - Página: 1, Tamaño: 10, IncludeDeleted: false', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/BitacoraEntry', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 12:33:54.0084', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers.GenericController`5', 'message': 'Recuperados 4 "BitacoraEntry" de 4 totales', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': 'http://localhost/api/BitacoraEntry', 'Method': 'GET', 'UserAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'IP': '::1' } }
{ 'timestamp': '2025-07-08 12:36:08.3723', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Iniciando aplicación...', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 12:36:12.7188', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Base de datos inicializada correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 12:36:13.7099', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Datos de prueba inicializados correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 12:36:14.1102', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Aplicación iniciada correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 12:38:22.1180', 'level': 'Debug', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Iniciando aplicación...', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 12:38:23.1807', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Base de datos inicializada correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 12:38:24.0537', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Datos de prueba inicializados correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
{ 'timestamp': '2025-07-08 12:38:24.1331', 'level': 'Info', 'logger': 'BitacoraResiduosESH.Backend.Backend.WebAPI.Program', 'message': 'Aplicación iniciada correctamente', 'exception': '', 'properties': { 'EventId': '0', 'RequestId': '', 'Url': '', 'Method': '', 'UserAgent': '', 'IP': '' } }
