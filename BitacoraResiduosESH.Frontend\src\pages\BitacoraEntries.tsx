import React, { useState, useCallback } from 'react';
import { useBitacoraEntryCrud } from '../hooks/useBitacoraEntryCrud';
import { useAreaCrud } from '../hooks/useAreaCrud';
import { useWasteTypeCrud } from '../hooks/useWasteTypeCrud';
import { useContainerTypeCrud } from '../hooks/useContainerTypeCrud';
import { SearchableSelect, type SearchableSelectOption } from '../components/form/SearchableSelect';
import type { 
    BitacoraEntry, 
    CreateBitacoraEntryDto, 
    UpdateBitacoraEntryDto 
} from '../types/bitacoraEntry';
import { 
    Plus, 
    Edit, 
    Trash2, 
    Eye, 
    Search,
    ChevronLeft,
    ChevronRight,
    MoreHorizontal,
    Power,
    PowerOff
} from 'lucide-react';

const BitacoraEntries: React.FC = () => {
    // Estados para paginación y búsqueda
    const [pageNumber, setPageNumber] = useState(1);
    const [pageSize, setPageSize] = useState(10);
    const [searchTerm, setSearchTerm] = useState('');
    const [showCreateModal, setShowCreateModal] = useState(false);
    const [showEditModal, setShowEditModal] = useState(false);
    const [showViewModal, setShowViewModal] = useState(false);
    const [selectedEntry, setSelectedEntry] = useState<BitacoraEntry | null>(null);

    // Estados para los selects de búsqueda
    const [areaSearchTerm, setAreaSearchTerm] = useState('');
    const [wasteTypeSearchTerm, setWasteTypeSearchTerm] = useState('');
    const [containerTypeSearchTerm, setContainerTypeSearchTerm] = useState('');

    // Hook para operaciones CRUD de BitacoraEntry
    const {
        useGetAll,
        useCreate,
        useUpdate,
        useDelete,
        useActivate,
        useDeactivate,
    } = useBitacoraEntryCrud();

    // Hooks para obtener datos de entidades relacionadas
    const { useSearchByName: useSearchAreas } = useAreaCrud();
    const { useSearchByName: useSearchWasteTypes } = useWasteTypeCrud();
    const { useSearchByName: useSearchContainerTypes } = useContainerTypeCrud();

    // Queries principales
    const { data: entriesData, isLoading } = useGetAll(pageNumber, pageSize, false);

    // Queries para búsquedas de entidades relacionadas
    const { data: areasData } = useSearchAreas(areaSearchTerm || ' ', 1, 50, false);
    const { data: wasteTypesData } = useSearchWasteTypes(wasteTypeSearchTerm || ' ', 1, 50, false);
    const { data: containerTypesData } = useSearchContainerTypes(containerTypeSearchTerm || ' ', 1, 50, false);

    // Mutations
    const createMutation = useCreate();
    const updateMutation = useUpdate();
    const deleteMutation = useDelete();
    const activateMutation = useActivate();
    const deactivateMutation = useDeactivate();

    // Convertir datos a opciones para los selects
    const areaOptions: SearchableSelectOption[] = areasData?.data?.map(area => ({
        id: area.id,
        name: area.name,
        description: area.description
    })) || [];

    const wasteTypeOptions: SearchableSelectOption[] = wasteTypesData?.data?.map(wasteType => ({
        id: wasteType.id,
        name: wasteType.name,
        description: wasteType.description
    })) || [];

    const containerTypeOptions: SearchableSelectOption[] = containerTypesData?.data?.map(containerType => ({
        id: containerType.id,
        name: containerType.name,
        description: containerType.description
    })) || [];

    // Handlers para búsquedas
    const handleAreaSearch = useCallback((searchTerm: string) => {
        setAreaSearchTerm(searchTerm);
    }, []);

    const handleWasteTypeSearch = useCallback((searchTerm: string) => {
        setWasteTypeSearchTerm(searchTerm);
    }, []);

    const handleContainerTypeSearch = useCallback((searchTerm: string) => {
        setContainerTypeSearchTerm(searchTerm);
    }, []);

    // Handlers para CRUD operations
    const handleCreate = async (formData: FormData) => {
        const data: CreateBitacoraEntryDto = {
            name: formData.get('name') as string,
            description: formData.get('description') as string || undefined,
            wasteTypeId: parseInt(formData.get('wasteTypeId') as string),
            grossWeight: parseFloat(formData.get('grossWeight') as string),
            tare: parseFloat(formData.get('tare') as string),
            netWeightLB: parseFloat(formData.get('netWeightLB') as string),
            netWeightKG: parseFloat(formData.get('netWeightKG') as string),
            unitPrice: parseFloat(formData.get('unitPrice') as string),
            containerTypeId: parseInt(formData.get('containerTypeId') as string),
            areaId: parseInt(formData.get('areaId') as string),
            entryDate: formData.get('entryDate') as string,
            departureDate: formData.get('departureDate') as string || undefined,
            enteredBy: formData.get('enteredBy') as string,
            active: true
        };

        await createMutation.mutateAsync(data);
        setShowCreateModal(false);
    };

    const handleUpdate = async (formData: FormData) => {
        if (!selectedEntry) return;

        const data: UpdateBitacoraEntryDto = {
            id: selectedEntry.id,
            name: formData.get('name') as string,
            description: formData.get('description') as string || undefined,
            wasteTypeId: parseInt(formData.get('wasteTypeId') as string),
            grossWeight: parseFloat(formData.get('grossWeight') as string),
            tare: parseFloat(formData.get('tare') as string),
            netWeightLB: parseFloat(formData.get('netWeightLB') as string),
            netWeightKG: parseFloat(formData.get('netWeightKG') as string),
            unitPrice: parseFloat(formData.get('unitPrice') as string),
            containerTypeId: parseInt(formData.get('containerTypeId') as string),
            areaId: parseInt(formData.get('areaId') as string),
            entryDate: formData.get('entryDate') as string,
            departureDate: formData.get('departureDate') as string || undefined,
            enteredBy: formData.get('enteredBy') as string,
            active: selectedEntry.active
        };

        await updateMutation.mutateAsync({ id: selectedEntry.id, data });
        setShowEditModal(false);
        setSelectedEntry(null);
    };

    const handleDelete = async (id: number) => {
        if (confirm('¿Está seguro de que desea eliminar esta entrada de bitácora?')) {
            await deleteMutation.mutateAsync(id);
        }
    };

    const handleActivate = async (id: number) => {
        await activateMutation.mutateAsync(id);
    };

    const handleDeactivate = async (id: number) => {
        await deactivateMutation.mutateAsync(id);
    };

    // Handlers para modales
    const openCreateModal = () => {
        setShowCreateModal(true);
    };

    const openEditModal = (entry: BitacoraEntry) => {
        setSelectedEntry(entry);
        setShowEditModal(true);
    };

    const openViewModal = (entry: BitacoraEntry) => {
        setSelectedEntry(entry);
        setShowViewModal(true);
    };

    const closeModals = () => {
        setShowCreateModal(false);
        setShowEditModal(false);
        setShowViewModal(false);
        setSelectedEntry(null);
    };

    // Handlers para paginación
    const handlePageChange = (page: number) => {
        setPageNumber(page);
    };

    const handlePageSizeChange = (size: number) => {
        setPageSize(size);
        setPageNumber(1);
    };

    // Calcular información de paginación
    const totalPages = Math.ceil((entriesData?.totalRecords || 0) / pageSize);
    const startRecord = (pageNumber - 1) * pageSize + 1;
    const endRecord = Math.min(pageNumber * pageSize, entriesData?.totalRecords || 0);

    return (
        <div className="p-6 bg-white dark:bg-gray-800 min-h-full">
            {/* Header */}
            <div className="mb-6">
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                    Bitácora de Residuos
                </h1>
                <p className="text-gray-600 dark:text-gray-300">
                    Gestión de entradas y salidas de residuos
                </p>
            </div>

            {/* Toolbar */}
            <div className="mb-6 flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center">
                {/* Search */}
                <div className="relative flex-1 max-w-md">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                    <input
                        type="text"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        placeholder="Buscar entradas de bitácora..."
                        className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                </div>

                {/* Actions */}
                <div className="flex gap-2">
                    <button
                        onClick={openCreateModal}
                        className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                    >
                        <Plus className="w-4 h-4" />
                        Nueva Entrada
                    </button>
                </div>
            </div>

            {/* Table */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
                <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead className="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Tipo de Residuo
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Área
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Peso Neto (KG)
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Precio Unitario
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Fecha de Entrada
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Estado
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Acciones
                                </th>
                            </tr>
                        </thead>
                        <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            {isLoading ? (
                                <tr>
                                    <td colSpan={8} className="px-6 py-4 text-center text-gray-500">
                                        Cargando...
                                    </td>
                                </tr>
                            ) : entriesData?.data?.length === 0 ? (
                                <tr>
                                    <td colSpan={8} className="px-6 py-4 text-center text-gray-500">
                                        No se encontraron entradas de bitácora
                                    </td>
                                </tr>
                            ) : (
                                entriesData?.data?.map((entry) => (
                                    <tr key={entry.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                            {entry.wasteType?.name || 'N/A'}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                            {entry.area?.name || 'N/A'}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                            {entry.netWeightKG.toFixed(2)} kg
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                            ${entry.unitPrice.toFixed(2)}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                            {entry.entryDateString}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <span
                                                className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                                    entry.active
                                                        ? 'bg-green-100 text-green-800'
                                                        : 'bg-red-100 text-red-800'
                                                }`}
                                            >
                                                {entry.active ? 'Activo' : 'Inactivo'}
                                            </span>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div className="flex items-center gap-2">
                                                <button
                                                    onClick={() => openViewModal(entry)}
                                                    className="text-blue-600 hover:text-blue-900 p-1"
                                                    title="Ver detalles"
                                                >
                                                    <Eye className="w-4 h-4" />
                                                </button>
                                                <button
                                                    onClick={() => openEditModal(entry)}
                                                    className="text-yellow-600 hover:text-yellow-900 p-1"
                                                    title="Editar"
                                                >
                                                    <Edit className="w-4 h-4" />
                                                </button>
                                                <button
                                                    onClick={() => entry.active ? handleDeactivate(entry.id) : handleActivate(entry.id)}
                                                    className={`p-1 ${
                                                        entry.active 
                                                            ? 'text-orange-600 hover:text-orange-900' 
                                                            : 'text-green-600 hover:text-green-900'
                                                    }`}
                                                    title={entry.active ? 'Desactivar' : 'Activar'}
                                                >
                                                    {entry.active ? <PowerOff className="w-4 h-4" /> : <Power className="w-4 h-4" />}
                                                </button>
                                                <button
                                                    onClick={() => handleDelete(entry.id)}
                                                    className="text-red-600 hover:text-red-900 p-1"
                                                    title="Eliminar"
                                                >
                                                    <Trash2 className="w-4 h-4" />
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                ))
                            )}
                        </tbody>
                    </table>
                </div>

                {/* Pagination */}
                {entriesData && entriesData.totalRecords > 0 && (
                    <div className="bg-white dark:bg-gray-800 px-4 py-3 border-t border-gray-200 dark:border-gray-700 sm:px-6">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center text-sm text-gray-700 dark:text-gray-300">
                                <span>Mostrando </span>
                                <span className="font-medium">{startRecord}</span>
                                <span> a </span>
                                <span className="font-medium">{endRecord}</span>
                                <span> de </span>
                                <span className="font-medium">{entriesData.totalRecords}</span>
                                <span> resultados</span>
                            </div>

                            <div className="flex items-center gap-2">
                                <select
                                    value={pageSize}
                                    onChange={(e) => handlePageSizeChange(parseInt(e.target.value))}
                                    className="border border-gray-300 rounded px-2 py-1 text-sm"
                                >
                                    <option value={10}>10</option>
                                    <option value={25}>25</option>
                                    <option value={50}>50</option>
                                    <option value={100}>100</option>
                                </select>

                                <div className="flex items-center gap-1">
                                    <button
                                        onClick={() => handlePageChange(pageNumber - 1)}
                                        disabled={pageNumber <= 1}
                                        className="p-2 border border-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                                    >
                                        <ChevronLeft className="w-4 h-4" />
                                    </button>

                                    <span className="px-3 py-1 text-sm">
                                        Página {pageNumber} de {totalPages}
                                    </span>

                                    <button
                                        onClick={() => handlePageChange(pageNumber + 1)}
                                        disabled={pageNumber >= totalPages}
                                        className="p-2 border border-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                                    >
                                        <ChevronRight className="w-4 h-4" />
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                )}
            </div>

            {/* Create Modal */}
            {showCreateModal && (
                <div className="fixed inset-0 bg-gray-600/50 overflow-y-auto h-full w-full z-50">
                    <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
                        <div className="mt-3">
                            <h3 className="text-lg font-medium text-gray-900 mb-4">
                                Nueva Entrada de Bitácora
                            </h3>
                            <form onSubmit={(e) => {
                                e.preventDefault();
                                handleCreate(new FormData(e.currentTarget));
                            }}>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    {/* Nombre */}
                                    <div>
                                        <label className="block text-sm font-medium mb-1">
                                            Nombre *
                                        </label>
                                        <input
                                            type="text"
                                            name="name"
                                            required
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            placeholder="Ingrese el nombre de la entrada"
                                        />
                                    </div>

                                    {/* Descripción */}
                                    <div>
                                        <label className="block text-sm font-medium mb-1">
                                            Descripción
                                        </label>
                                        <input
                                            type="text"
                                            name="description"
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            placeholder="Descripción opcional"
                                        />
                                    </div>

                                    {/* Tipo de Residuo */}
                                    <div>
                                        <SearchableSelect
                                            label="Tipo de Residuo"
                                            required
                                            options={wasteTypeOptions}
                                            onChange={(value) => {
                                                const input = document.querySelector('input[name="wasteTypeId"]') as HTMLInputElement;
                                                if (input) input.value = value?.toString() || '';
                                            }}
                                            onSearch={handleWasteTypeSearch}
                                            placeholder="Seleccionar tipo de residuo..."
                                        />
                                        <input type="hidden" name="wasteTypeId" required />
                                    </div>

                                    {/* Área */}
                                    <div>
                                        <SearchableSelect
                                            label="Área"
                                            required
                                            options={areaOptions}
                                            onChange={(value) => {
                                                const input = document.querySelector('input[name="areaId"]') as HTMLInputElement;
                                                if (input) input.value = value?.toString() || '';
                                            }}
                                            onSearch={handleAreaSearch}
                                            placeholder="Seleccionar área..."
                                        />
                                        <input type="hidden" name="areaId" required />
                                    </div>

                                    {/* Tipo de Contenedor */}
                                    <div>
                                        <SearchableSelect
                                            label="Tipo de Contenedor"
                                            required
                                            options={containerTypeOptions}
                                            onChange={(value) => {
                                                const input = document.querySelector('input[name="containerTypeId"]') as HTMLInputElement;
                                                if (input) input.value = value?.toString() || '';
                                            }}
                                            onSearch={handleContainerTypeSearch}
                                            placeholder="Seleccionar tipo de contenedor..."
                                        />
                                        <input type="hidden" name="containerTypeId" required />
                                    </div>

                                    {/* Peso Bruto */}
                                    <div>
                                        <label className="block text-sm font-medium mb-1">
                                            Peso Bruto (kg) *
                                        </label>
                                        <input
                                            type="number"
                                            name="grossWeight"
                                            step="0.01"
                                            min="0"
                                            required
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            placeholder="0.00"
                                        />
                                    </div>

                                    {/* Tara */}
                                    <div>
                                        <label className="block text-sm font-medium mb-1">
                                            Tara (kg) *
                                        </label>
                                        <input
                                            type="number"
                                            name="tare"
                                            step="0.01"
                                            min="0"
                                            required
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            placeholder="0.00"
                                        />
                                    </div>

                                    {/* Peso Neto LB */}
                                    <div>
                                        <label className="block text-sm font-medium mb-1">
                                            Peso Neto (lb) *
                                        </label>
                                        <input
                                            type="number"
                                            name="netWeightLB"
                                            step="0.01"
                                            min="0"
                                            required
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            placeholder="0.00"
                                        />
                                    </div>

                                    {/* Peso Neto KG */}
                                    <div>
                                        <label className="block text-sm font-medium mb-1">
                                            Peso Neto (kg) *
                                        </label>
                                        <input
                                            type="number"
                                            name="netWeightKG"
                                            step="0.01"
                                            min="0"
                                            required
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            placeholder="0.00"
                                        />
                                    </div>

                                    {/* Precio Unitario */}
                                    <div>
                                        <label className="block text-sm font-medium mb-1">
                                            Precio Unitario ($) *
                                        </label>
                                        <input
                                            type="number"
                                            name="unitPrice"
                                            step="0.01"
                                            min="0"
                                            required
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            placeholder="0.00"
                                        />
                                    </div>

                                    {/* Fecha de Entrada */}
                                    <div>
                                        <label className="block text-sm font-medium mb-1">
                                            Fecha de Entrada *
                                        </label>
                                        <input
                                            type="datetime-local"
                                            name="entryDate"
                                            required
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        />
                                    </div>

                                    {/* Fecha de Salida */}
                                    <div>
                                        <label className="block text-sm font-medium mb-1">
                                            Fecha de Salida
                                        </label>
                                        <input
                                            type="datetime-local"
                                            name="departureDate"
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        />
                                    </div>

                                    {/* Ingresado por */}
                                    <div className="md:col-span-2">
                                        <label className="block text-sm font-medium mb-1">
                                            Ingresado por *
                                        </label>
                                        <input
                                            type="text"
                                            name="enteredBy"
                                            required
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            placeholder="Nombre de quien ingresa la entrada"
                                        />
                                    </div>
                                </div>

                                <div className="flex justify-end gap-3 mt-6">
                                    <button
                                        type="button"
                                        onClick={closeModals}
                                        className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                                    >
                                        Cancelar
                                    </button>
                                    <button
                                        type="submit"
                                        disabled={createMutation.isPending}
                                        className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                                    >
                                        {createMutation.isPending ? 'Creando...' : 'Crear Entrada'}
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            )}

            {/* Edit Modal */}
            {showEditModal && selectedEntry && (
                <div className="fixed inset-0 bg-gray-600/50 overflow-y-auto h-full w-full z-50">
                    <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
                        <div className="mt-3">
                            <h3 className="text-lg font-medium text-gray-900 mb-4">
                                Editar Entrada de Bitácora
                            </h3>
                            <form onSubmit={(e) => {
                                e.preventDefault();
                                handleUpdate(new FormData(e.currentTarget));
                            }}>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    {/* Descripción */}
                                    <div>
                                        <label className="block text-sm font-medium mb-1">
                                            Descripción
                                        </label>
                                        <input
                                            type="text"
                                            name="description"
                                            defaultValue={selectedEntry.comments || ''}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            placeholder="Descripción opcional"
                                        />
                                    </div>

                                    {/* Tipo de Residuo */}
                                    <div>
                                        <SearchableSelect
                                            label="Tipo de Residuo"
                                            required
                                            options={wasteTypeOptions}
                                            value={selectedEntry.wasteTypeId}
                                            onChange={(value) => {
                                                const input = document.querySelector('input[name="wasteTypeId"]') as HTMLInputElement;
                                                if (input) input.value = value?.toString() || '';
                                            }}
                                            onSearch={handleWasteTypeSearch}
                                            placeholder="Seleccionar tipo de residuo..."
                                        />
                                        <input type="hidden" name="wasteTypeId" defaultValue={selectedEntry.wasteTypeId} required />
                                    </div>

                                    {/* Área */}
                                    <div>
                                        <SearchableSelect
                                            label="Área"
                                            required
                                            options={areaOptions}
                                            value={selectedEntry.areaId}
                                            onChange={(value) => {
                                                const input = document.querySelector('input[name="areaId"]') as HTMLInputElement;
                                                if (input) input.value = value?.toString() || '';
                                            }}
                                            onSearch={handleAreaSearch}
                                            placeholder="Seleccionar área..."
                                        />
                                        <input type="hidden" name="areaId" defaultValue={selectedEntry.areaId} required />
                                    </div>

                                    {/* Tipo de Contenedor */}
                                    <div>
                                        <SearchableSelect
                                            label="Tipo de Contenedor"
                                            required
                                            options={containerTypeOptions}
                                            value={selectedEntry.containerTypeId}
                                            onChange={(value) => {
                                                const input = document.querySelector('input[name="containerTypeId"]') as HTMLInputElement;
                                                if (input) input.value = value?.toString() || '';
                                            }}
                                            onSearch={handleContainerTypeSearch}
                                            placeholder="Seleccionar tipo de contenedor..."
                                        />
                                        <input type="hidden" name="containerTypeId" defaultValue={selectedEntry.containerTypeId} required />
                                    </div>

                                    {/* Peso Bruto */}
                                    <div>
                                        <label className="block text-sm font-medium mb-1">
                                            Peso Bruto (kg) *
                                        </label>
                                        <input
                                            type="number"
                                            name="grossWeight"
                                            step="0.01"
                                            min="0"
                                            defaultValue={selectedEntry.grossWeight}
                                            required
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            placeholder="0.00"
                                        />
                                    </div>

                                    {/* Tara */}
                                    <div>
                                        <label className="block text-sm font-medium mb-1">
                                            Tara (kg) *
                                        </label>
                                        <input
                                            type="number"
                                            name="tare"
                                            step="0.01"
                                            min="0"
                                            defaultValue={selectedEntry.tare}
                                            required
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            placeholder="0.00"
                                        />
                                    </div>

                                    {/* Peso Neto LB */}
                                    <div>
                                        <label className="block text-sm font-medium mb-1">
                                            Peso Neto (lb) *
                                        </label>
                                        <input
                                            type="number"
                                            name="netWeightLB"
                                            step="0.01"
                                            min="0"
                                            defaultValue={selectedEntry.netWeightLB}
                                            required
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            placeholder="0.00"
                                        />
                                    </div>

                                    {/* Peso Neto KG */}
                                    <div>
                                        <label className="block text-sm font-medium mb-1">
                                            Peso Neto (kg) *
                                        </label>
                                        <input
                                            type="number"
                                            name="netWeightKG"
                                            step="0.01"
                                            min="0"
                                            defaultValue={selectedEntry.netWeightKG}
                                            required
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            placeholder="0.00"
                                        />
                                    </div>

                                    {/* Precio Unitario */}
                                    <div>
                                        <label className="block text-sm font-medium mb-1">
                                            Precio Unitario ($) *
                                        </label>
                                        <input
                                            type="number"
                                            name="unitPrice"
                                            step="0.01"
                                            min="0"
                                            defaultValue={selectedEntry.unitPrice}
                                            required
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            placeholder="0.00"
                                        />
                                    </div>

                                    {/* Fecha de Entrada */}
                                    <div>
                                        <label className="block text-sm font-medium mb-1">
                                            Fecha de Entrada *
                                        </label>
                                        <input
                                            type="datetime-local"
                                            name="entryDate"
                                            defaultValue={selectedEntry.entryDate ? new Date(selectedEntry.entryDate).toISOString().slice(0, 16) : ''}
                                            required
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        />
                                    </div>

                                    {/* Fecha de Salida */}
                                    <div>
                                        <label className="block text-sm font-medium mb-1">
                                            Fecha de Salida
                                        </label>
                                        <input
                                            type="datetime-local"
                                            name="departureDate"
                                            defaultValue={selectedEntry.departureDate ? new Date(selectedEntry.departureDate).toISOString().slice(0, 16) : ''}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        />
                                    </div>

                                    {/* Ingresado por */}
                                    <div className="md:col-span-2">
                                        <label className="block text-sm font-medium mb-1">
                                            Ingresado por *
                                        </label>
                                        <input
                                            type="text"
                                            name="enteredBy"
                                            defaultValue={selectedEntry.enteredBy}
                                            required
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            placeholder="Nombre de quien ingresa la entrada"
                                        />
                                    </div>
                                </div>

                                <div className="flex justify-end gap-3 mt-6">
                                    <button
                                        type="button"
                                        onClick={closeModals}
                                        className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                                    >
                                        Cancelar
                                    </button>
                                    <button
                                        type="submit"
                                        disabled={updateMutation.isPending}
                                        className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                                    >
                                        {updateMutation.isPending ? 'Actualizando...' : 'Actualizar Entrada'}
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            )}

            {/* View Modal */}
            {showViewModal && selectedEntry && (
                <div className="fixed inset-0 bg-gray-600/50 overflow-y-auto h-full w-full z-50">
                    <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
                        <div className="mt-3">
                            <h3 className="text-lg font-medium text-gray-900 mb-4">
                                Detalles de la Entrada de Bitácora
                            </h3>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                {/* Información Básica */}
                                <div className="space-y-4">
                                    <h4 className="font-medium text-gray-900 border-b pb-2">Información Básica</h4>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-500">ID</label>
                                        <p className="text-sm text-gray-900">{selectedEntry.id}</p>
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-500">Comentarios</label>
                                        <p className="text-sm text-gray-900">{selectedEntry.comments || 'Sin descripción'}</p>
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-500">Estado</label>
                                        <span
                                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                                selectedEntry.active
                                                    ? 'bg-green-100 text-green-800'
                                                    : 'bg-red-100 text-red-800'
                                            }`}
                                        >
                                            {selectedEntry.active ? 'Activo' : 'Inactivo'}
                                        </span>
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-500">Ingresado por</label>
                                        <p className="text-sm text-gray-900">{selectedEntry.enteredBy}</p>
                                    </div>
                                </div>

                                {/* Información de Residuos */}
                                <div className="space-y-4">
                                    <h4 className="font-medium text-gray-900 border-b pb-2">Información de Residuos</h4>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-500">Tipo de Residuo</label>
                                        <p className="text-sm text-gray-900">{selectedEntry.wasteType?.name || 'N/A'}</p>
                                        {selectedEntry.wasteType?.description && (
                                            <p className="text-xs text-gray-500">{selectedEntry.wasteType.description}</p>
                                        )}
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-500">Área</label>
                                        <p className="text-sm text-gray-900">{selectedEntry.area?.name || 'N/A'}</p>
                                        {selectedEntry.area?.description && (
                                            <p className="text-xs text-gray-500">{selectedEntry.area.description}</p>
                                        )}
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-500">Tipo de Contenedor</label>
                                        <p className="text-sm text-gray-900">{selectedEntry.containerType?.name || 'N/A'}</p>
                                        {selectedEntry.containerType?.description && (
                                            <p className="text-xs text-gray-500">{selectedEntry.containerType.description}</p>
                                        )}
                                    </div>
                                </div>

                                {/* Información de Pesos */}
                                <div className="space-y-4">
                                    <h4 className="font-medium text-gray-900 border-b pb-2">Información de Pesos</h4>

                                    <div className="grid grid-cols-2 gap-4">
                                        <div>
                                            <label className="block text-sm font-medium text-gray-500">Peso Bruto</label>
                                            <p className="text-sm text-gray-900">{selectedEntry.grossWeight.toFixed(2)} kg</p>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-500">Tara</label>
                                            <p className="text-sm text-gray-900">{selectedEntry.tare.toFixed(2)} kg</p>
                                        </div>
                                    </div>

                                    <div className="grid grid-cols-2 gap-4">
                                        <div>
                                            <label className="block text-sm font-medium text-gray-500">Peso Neto (lb)</label>
                                            <p className="text-sm text-gray-900">{selectedEntry.netWeightLB.toFixed(2)} lb</p>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-500">Peso Neto (kg)</label>
                                            <p className="text-sm text-gray-900">{selectedEntry.netWeightKG.toFixed(2)} kg</p>
                                        </div>
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-500">Precio Unitario</label>
                                        <p className="text-sm text-gray-900">${selectedEntry.unitPrice.toFixed(2)}</p>
                                    </div>
                                </div>

                                {/* Información de Fechas */}
                                <div className="space-y-4">
                                    <h4 className="font-medium text-gray-900 border-b pb-2">Información de Fechas</h4>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-500">Fecha de Entrada</label>
                                        <p className="text-sm text-gray-900">{selectedEntry.entryDateString}</p>
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-500">Fecha de Salida</label>
                                        <p className="text-sm text-gray-900">
                                            {selectedEntry.departureDateString || 'No ha salido'}
                                        </p>
                                    </div>
                                </div>

                                {/* Información de Auditoría */}
                                <div className="md:col-span-2 space-y-4">
                                    <h4 className="font-medium text-gray-900 border-b pb-2">Información de Auditoría</h4>

                                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                        <div>
                                            <label className="block text-sm font-medium text-gray-500">Creado por</label>
                                            <p className="text-sm text-gray-900">{selectedEntry.createdBy}</p>
                                            <p className="text-xs text-gray-500">{selectedEntry.createdLocalString}</p>
                                        </div>

                                        {selectedEntry.updatedBy && (
                                            <div>
                                                <label className="block text-sm font-medium text-gray-500">Actualizado por</label>
                                                <p className="text-sm text-gray-900">{selectedEntry.updatedBy}</p>
                                                <p className="text-xs text-gray-500">{selectedEntry.updatedLocalString}</p>
                                            </div>
                                        )}

                                        {selectedEntry.isDeleted && selectedEntry.deletedBy && (
                                            <div>
                                                <label className="block text-sm font-medium text-gray-500">Eliminado por</label>
                                                <p className="text-sm text-gray-900">{selectedEntry.deletedBy}</p>
                                                <p className="text-xs text-gray-500">{selectedEntry.deletedLocalString}</p>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>

                            <div className="flex justify-end gap-3 mt-6">
                                <button
                                    type="button"
                                    onClick={closeModals}
                                    className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                                >
                                    Cerrar
                                </button>
                                <button
                                    type="button"
                                    onClick={() => {
                                        closeModals();
                                        openEditModal(selectedEntry);
                                    }}
                                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                                >
                                    Editar
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default BitacoraEntries;
