﻿namespace BitacoraResiduosESH.Backend.Backend.Application.DTOs.BitacoraEntry;

public class CreateBitacoraEntryDto : CreateBaseEntityDto
{
    // Basic properties (since BaseEntity doesn't have Name/Description)
    [Required(ErrorMessage = "El nombre es requerido")]
    [StringLength(200, ErrorMessage = "El nombre no puede exceder 200 caracteres")]
    public string Name { get; set; } = string.Empty;

    [StringLength(1000, ErrorMessage = "La descripción no puede exceder 1000 caracteres")]
    public string? Description { get; set; }
    // Waste Type relationship
    [Required(ErrorMessage = "El tipo de residuo es requerido")]
    public int WasteTypeId { get; set; }

    // Weight properties
    [Required(ErrorMessage = "El peso bruto es requerido")]
    [Range(0, double.MaxValue, ErrorMessage = "El peso bruto debe ser mayor o igual a 0")]
    public decimal GrossWeight { get; set; }

    [Required(ErrorMessage = "La tara es requerida")]
    [Range(0, double.MaxValue, ErrorMessage = "La tara debe ser mayor o igual a 0")]
    public decimal Tare { get; set; }

    [Required(ErrorMessage = "El peso neto en libras es requerido")]
    [Range(0, double.MaxValue, ErrorMessage = "El peso neto en libras debe ser mayor o igual a 0")]
    public decimal NetWeightLB { get; set; }

    [Required(ErrorMessage = "El peso neto en kilogramos es requerido")]
    [Range(0, double.MaxValue, ErrorMessage = "El peso neto en kilogramos debe ser mayor o igual a 0")]
    public decimal NetWeightKG { get; set; }

    // Price
    [Required(ErrorMessage = "El precio unitario es requerido")]
    [Range(0, double.MaxValue, ErrorMessage = "El precio unitario debe ser mayor o igual a 0")]
    public decimal UnitPrice { get; set; }

    // Container Type relationship
    [Required(ErrorMessage = "El tipo de contenedor es requerido")]
    public int ContainerTypeId { get; set; }

    // Area relationship
    [Required(ErrorMessage = "El área es requerida")]
    public int AreaId { get; set; }

    // Dates
    [Required(ErrorMessage = "La fecha de entrada es requerida")]
    public DateTime EntryDate { get; set; }

    public DateTime? DepartureDate { get; set; }

    // User who entered the record
    [Required(ErrorMessage = "El usuario que ingresó el registro es requerido")]
    [StringLength(100, ErrorMessage = "El nombre del usuario no puede exceder 100 caracteres")]
    public string EnteredBy { get; set; } = string.Empty;
}
